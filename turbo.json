{"$schema": "https://turborepo.com/schema.json", "globalDependencies": ["**/.env", ".env"], "globalEnv": ["NODE_ENV"], "ui": "tui", "tasks": {"build": {"env": ["*"], "dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"env": ["*"], "cache": true, "dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"env": ["*"], "cache": false, "persistent": true, "inputs": ["$TURBO_DEFAULT$", ".env"]}, "test": {"env": ["*"], "cache": false, "persistent": false, "inputs": ["$TURBO_DEFAULT$", ".env.test"]}, "test:watch": {"env": ["*"], "cache": false, "persistent": true, "inputs": ["$TURBO_DEFAULT$", ".env.test"]}}}