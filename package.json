{"name": "@ps/base", "private": true, "scripts": {"build": "dotenv -- turbo run build", "dev": "dotenv -- turbo run dev", "lint": "dotenv -- turbo run lint", "test": "dotenv -e .env.test -- turbo run test", "test:watch": "dotenv -e .env.test -- turbo run test:watch", "format": "prettier --write \"**/*.{js,ts,tsx,md,json}\"", "check-types": "turbo run check-types", "clean": "rm -rf node_modules .turbo && find . -type d -name 'dist' -o -name 'build' -o -name '.next' -o -name 'node_modules' | grep -v './node_modules' | xargs rm -rf"}, "devDependencies": {"dotenv-cli": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "turbo": "^2.5.4", "typescript": "5.8.3"}, "engines": {"node": ">=22"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}