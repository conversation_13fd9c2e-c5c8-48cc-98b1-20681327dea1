{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "eslint.format.enable": true, "editor.formatOnSave": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.workingDirectories": [{"mode": "auto"}], "typescript.tsdk": "node_modules/typescript/lib", "cSpell.words": ["blogify", "bullmq", "geoip", "kbar", "mailchimp", "<PERSON><PERSON>", "sonner", "superadmin", "vite"]}