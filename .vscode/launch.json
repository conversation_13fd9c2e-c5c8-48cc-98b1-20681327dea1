{"version": "0.2.0", "configurations": [{"name": "Debug Server", "skipFiles": ["<node_internals>/**"], "type": "node", "request": "launch", "cwd": "${workspaceRoot}/apps/server", "runtimeExecutable": "pnpm", "runtimeArgs": ["start:debug"], "console": "integratedTerminal", "sourceMaps": true, "outFiles": ["${workspaceRoot}/apps/server/dist/**/*.js"], "envFile": "${workspaceRoot}/.env"}, {"name": "Debug Server Tests", "type": "node", "request": "launch", "cwd": "${workspaceRoot}/apps/server", "program": "${workspaceRoot}/apps/server/node_modules/.bin/jest", "args": ["--runInBand", "--watch<PERSON>ll"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "envFile": "${workspaceRoot}/.env", "sourceMaps": true}, {"type": "node", "request": "attach", "name": "Attach to Server Process", "port": 9229, "restart": true, "sourceMaps": true, "cwd": "${workspaceRoot}/apps/server", "localRoot": "${workspaceRoot}/apps/server", "remoteRoot": "${workspaceRoot}/apps/server"}]}