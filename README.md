# Full-Stack Application Starter

A modern, full-stack application starter built with TypeScript and powered by Turborepo for optimal development experience and performance.

## 🏗️ Architecture

This monorepo contains a complete application ecosystem with three main applications and shared packages:

### 📱 Applications

#### `apps/server` - NestJS Backend API

- **Technology**: NestJS with TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Features**:
  - JWT Authentication with Google OAuth2
  - Email system with templates (password reset, verification, etc.)
  - Stripe payment integration
  - Bull queue system for background jobs
  - IP-based geo-location and rate limiting
  - Slack integration for notifications
  - RESTful API with OpenAPI documentation
  - Team and user management
  - Settings management

#### `apps/client` - Next.js Frontend

- **Technology**: Next.js 15 with React 19 and TypeScript
- **Features**:
  - Modern authentication with NextAuth.js
  - Responsive dashboard
  - Payment integration with Stripe
  - User profile and team management
  - Dark/light theme support
  - Server-side rendering and static generation
  - Optimized performance with Next.js App Router

#### `apps/admin` - Refine Admin Panel

- **Technology**: Refine with React and TypeScript
- **Features**:
  - Advanced admin dashboard
  - CRUD operations for all resources
  - Data visualization and analytics
  - Customizable UI components

### 📦 Shared Packages

#### `packages/ui` - Design System

- **Technology**: React with Tailwind CSS and shadcn/ui
- **Components**: Buttons, forms, cards, modals, navigation, and more
- **Features**: Dark mode support, responsive components, accessibility compliance

#### `packages/common` - Shared Utilities

- **Contents**: Common utilities, constants, and helper functions
- **Features**: Authentication utilities, time constants, shared business logic

#### `packages/types` - TypeScript Definitions

- **Contents**: Shared TypeScript interfaces and types
- **Features**: User types, team types, subscription types, API response types

#### `packages/eslint-config` - ESLint Configurations

- **Configurations**: Base, Next.js, NestJS, Refine, and React-specific configs
- **Features**: Consistent code quality across all applications

#### `packages/typescript-config` - TypeScript Configurations

- **Configurations**: Base, Next.js, NestJS, Refine, and React library configs
- **Features**: Shared TypeScript settings and compiler options

## 🚀 Getting Started

### Prerequisites

- Node.js >= 22
- pnpm (Package Manager)
- MongoDB (with replica set enabled)
- SSL certificates for development

### Installation

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd <your-project-name>
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up SSL certificates** (Required for HTTPS development)

   ```bash
   # Install mkcert if you haven't already
   brew install mkcert # macOS
   # or
   choco install mkcert # Windows
   # or
   sudo apt install libnss3-tools # Linux (then install mkcert manually)

   # Create and install local CA
   mkcert -install

   # Create certificates directory
   mkdir certs

   # Generate certificates for localhost
   mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 127.0.0.1 ::1
   ```

4. **Set up MongoDB with Replica Set**

   Create a `docker-compose.yml` file in the root directory:

   ```yaml
   version: '3.8'
   services:
     mongodb:
       image: mongo:7
       container_name: app-mongodb
       restart: always
       ports:
         - '27017:27017'
       environment:
         MONGO_INITDB_ROOT_USERNAME: admin
         MONGO_INITDB_ROOT_PASSWORD: password
         MONGO_INITDB_DATABASE: app
       volumes:
         - mongodb_data:/data/db
         - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
       command: ['--replSet', 'rs0', '--bind_ip_all', '--keyFile', '/opt/keyfile']
       networks:
         - app-network

   volumes:
     mongodb_data:

   networks:
     app-network:
       driver: bridge
   ```

   Create a `mongo-init.js` file for initialization:

   ```javascript
   // Initialize replica set
   rs.initiate({
     _id: 'rs0',
     members: [{ _id: 0, host: 'localhost:27017' }],
   });
   ```

   Start MongoDB:

   ```bash
   # Start MongoDB with Docker
   docker-compose up -d mongodb

   # Initialize replica set (run once after first startup)
   docker exec -it app-mongodb mongosh --eval "rs.initiate({_id: 'rs0', members: [{_id: 0, host: 'localhost:27017'}]})"
   ```

5. **Environment Configuration**

- Copy & paste the .env.sample file and rename it to .env:
- Ask a teammate to provide the envs.
- _TODO:_ We'll set up infisical in future to automate this process.

### Development

1. **Start all applications**

   ```bash
   pnpm dev
   ```

   This will start:

   - Server API at `https://localhost:3333`
   - Client app at `https://localhost:3000`
   - Admin panel at `https://localhost:3001`

2. **Start individual applications**

   ```bash
   # Start only the server
   pnpm dev --filter @ps/server

   # Start only the client
   pnpm dev --filter @ps/client

   # Start only the admin panel
   pnpm dev --filter @ps/admin
   ```

3. **Run tests**

   ```bash
   # Run all tests
   pnpm test

   # Run tests in watch mode
   pnpm test:watch
   ```

4. **Lint and format code**

   ```bash
   # Lint all packages
   pnpm lint

   # Format all files
   pnpm format

   # Type checking
   pnpm check-types
   ```

### Build for Production

```bash
# Build all applications and packages
pnpm build

# Build only the server
pnpm build --filter @ps/server

# Build only the client
pnpm build --filter @ps/client

# Build only the admin panel
pnpm build --filter @ps/admin
```

## 🔧 Available Scripts

| Script             | Description                                |
| ------------------ | ------------------------------------------ |
| `pnpm dev`         | Start all applications in development mode |
| `pnpm build`       | Build all applications and packages        |
| `pnpm lint`        | Lint all packages                          |
| `pnpm test`        | Run all tests                              |
| `pnpm test:watch`  | Run tests in watch mode                    |
| `pnpm format`      | Format all files with Prettier             |
| `pnpm check-types` | Type-check all TypeScript files            |

_Note:_ Use `--filter <APP_NAME>` to execute any script for a specific app.

## 🌐 API Documentation

The server provides OpenAPI documentation available at:

- Development: `https://localhost:3333/docs`
- Production: `https://your-domain.com/docs`

## 🔒 Authentication Flow

1. **JWT-based authentication** with refresh tokens
2. **Google OAuth2** integration for social login
3. **Email verification** for new accounts
4. **Password reset** functionality with secure tokens
5. **Role-based access control** for admin features

## 💳 Payment Integration

- **Stripe** integration for subscription management
- **Webhook handling** for payment events
- **Subscription tiers** with feature gating
- **Invoice generation** and management

## 📧 Email System

- **Transactional emails** with Mailchimp
- **Template system** with Nunjucks
- **Email previews** in development
- **Queue system** for reliable delivery

## 🗄️ Database Schema

The application uses MongoDB with the following main collections:

- **users** - User accounts and profiles
- **teams** - Team/organization management
- **settings** - Application configuration

## 🔧 Tech Stack

| Category           | Technologies                                   |
| ------------------ | ---------------------------------------------- |
| **Frontend**       | React 19, Next.js 15, TypeScript, Tailwind CSS |
| **Backend**        | NestJS, Node.js, TypeScript, MongoDB, Mongoose |
| **Admin**          | Refine, React, TypeScript, Vite                |
| **Authentication** | NextAuth.js, JWT, Google OAuth2                |
| **Payment**        | Stripe                                         |
| **Email**          | Mailchimp Transactional                        |
| **Queue**          | BullMQ with Redis                              |
| **Testing**        | Jest, Supertest                                |
| **Linting**        | ESLint, Prettier                               |
| **Build**          | Turborepo, SWC, Vite                           |
| **Deployment**     | Google CloudRun                                |

## 🤝 Contributing

1. Clone the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat(scope): add something amazing'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🆘 Troubleshooting

### Common Issues

1. **MongoDB Connection Issues**

   - Ensure MongoDB is running with replica set enabled
   - Check connection string in environment variables
   - Verify network connectivity

2. **SSL Certificate Issues**

   - Regenerate certificates with mkcert
   - Ensure certificates are in the `certs` directory
   - Check browser security settings

3. **Port Conflicts**

   - Check if ports 3000, 3001, 3333 are available
   - Modify port settings in respective config files

4. **Environment Variables**
   - Ensure all required environment variables are set
   - Check for typos in variable names
   - Verify third-party service credentials

For more help, reach out to a team member via Slack.
