---
description:
globs:
alwaysApply: true
---

# Cursor AI Rules

## TypeScript/Interface Conventions

- Do NOT prefix interfaces with "I" (e.g., use `interface User` not `interface IUser`)
- Use PascalCase for interface names
- Use camelCase for properties and methods
- Use descriptive names for interfaces that reflect their purpose

## Code Style

- Prefer explicit return types for functions when they're not obvious
- Use consistent naming conventions across the codebase
- Follow the existing patterns in the monorepo structure
- Use proper TypeScript typing instead of `any` when possible

## File Organization

- Keep related types in their respective resource directories
- Export types from index files for clean imports
- Follow the established directory structure for new features
- Use kebab-case for file names
- Add suffix extensions like .service.ts, .controller.ts where applicable
- Prefer absolute imports where ever possible
