{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./base.json", "compilerOptions": {"module": "CommonJS", "moduleResolution": "node", "target": "ES2023", "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "skipLibCheck": true, "strictNullChecks": true, "incremental": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false}}