import { DEFAULT_MAX_FILE_SIZE } from '@ps/ui/constants/file';
import API from '@ps/ui/services/api';

const DEFAULT_ALLOWED_TYPES = [
  'image/*',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/csv',
];

interface UploadOptions {
  folder: string;
  userRef?: string;
  maxSize?: number;
  allowedTypes?: string[];
  onProgress?: (progress: number) => void;
}

interface PresignedUploadResponse {
  url: string;
  fields: Record<string, string>;
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / 1024 ** i).toFixed(i ? 1 : 0)} ${sizes[i]}`;
};

const validateFile = (
  file: File,
  maxSize = DEFAULT_MAX_FILE_SIZE,
  allowedTypes = DEFAULT_ALLOWED_TYPES,
): { isValid: boolean; error?: string } => {
  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size ${formatBytes(file.size)} exceeds maximum allowed size of ${formatBytes(maxSize)}`,
    };
  }

  // Check file type
  const isTypeAllowed = allowedTypes.some((allowedType) => {
    if (allowedType.endsWith('/*')) {
      return file.type.startsWith(allowedType.slice(0, -1));
    }
    return file.type === allowedType;
  });

  if (!isTypeAllowed) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`,
    };
  }

  return { isValid: true };
};

const getPresignedUpload = async (
  { name: fileName, type: fileType }: File,
  folderName: string,
  userRef?: string,
): Promise<PresignedUploadResponse> => {
  const body = {
    folderName,
    fileName,
    fileType,
    userRef,
  };

  return API.post<typeof body, PresignedUploadResponse>('/upload', body);
};

const uploadToS3 = async (
  file: File,
  presignedData: PresignedUploadResponse,
  onProgress?: (progress: number) => void,
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();

    // Add all presigned fields
    Object.entries(presignedData.fields).forEach(([key, value]) => {
      formData.append(key, value);
    });

    // Add the file last
    formData.append('file', file);

    const xhr = new XMLHttpRequest();

    // Track upload progress
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });
    }

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        // Construct the final S3 URL
        const s3Url = `${presignedData.url}/${presignedData.fields.key}`;
        resolve(s3Url);
      } else {
        reject(new Error(`Upload failed with status ${xhr.status}: ${xhr.statusText}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Upload failed due to network error'));
    });

    xhr.addEventListener('abort', () => {
      reject(new Error('Upload was aborted'));
    });

    xhr.open('POST', presignedData.url);
    xhr.send(formData);
  });
};

export const uploadFile = async (file: File, options: UploadOptions): Promise<string> => {
  const {
    folder,
    userRef,
    onProgress,
    maxSize = 15 * 1024 * 1024,
    allowedTypes = DEFAULT_ALLOWED_TYPES,
  } = options;

  // Validate file
  const validation = validateFile(file, maxSize, allowedTypes);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // Update progress
  onProgress?.(10);

  try {
    // Get presigned upload data
    const presignedData = await getPresignedUpload(file, folder, userRef);
    onProgress?.(30);

    // Upload to S3 with progress tracking
    const s3Url = await uploadToS3(file, presignedData, (progress) => {
      // Map S3 upload progress to 30-100% range
      const mappedProgress = 30 + progress * 0.7;
      onProgress?.(Math.round(mappedProgress));
    });

    onProgress?.(100);
    return s3Url;
  } catch (error) {
    throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const uploadFiles = async (
  files: File[],
  options: UploadOptions & {
    onFileProgress?: (fileIndex: number, progress: number) => void;
    onOverallProgress?: (progress: number) => void;
  },
): Promise<string[]> => {
  const { onFileProgress, onOverallProgress, ...uploadOptions } = options;
  const results: string[] = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    if (!file) {
      throw new Error(`File at index ${i} is undefined`);
    }

    try {
      const url = await uploadFile(file, {
        ...uploadOptions,
        onProgress: (progress) => {
          onFileProgress?.(i, progress);

          // Calculate overall progress
          const completedFiles = i;
          const currentFileProgress = progress / 100;
          const overallProgress = ((completedFiles + currentFileProgress) / files.length) * 100;
          onOverallProgress?.(Math.round(overallProgress));
        },
      });

      results.push(url);
    } catch (error) {
      throw new Error(
        `Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  return results;
};
