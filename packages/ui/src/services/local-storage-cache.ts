// exp: expiry in minutes
const cacheSet = <T>(key: string, data: T, exp?: number): T => {
  let item: T | string = data;
  if (typeof data === 'object') {
    item = JSON.stringify(data);
  }

  localStorage.setItem(key, item as string);
  if (exp) {
    localStorage.setItem(`${key}_exp`, String(Date.now() + exp * 60 * 1000));
  }

  return data;
};

const cacheGet = <T>(key: string, fallback?: T): T => {
  const exp = localStorage.getItem(`${key}_exp`);
  if (exp) {
    if (parseInt(exp, 10) < Date.now()) {
      return fallback as T;
    }
  }

  const item = localStorage.getItem(key);
  if (!item) return fallback as T;

  try {
    return JSON.parse(item);
  } catch (_) {
    return item as T;
  }
};

const cacheRemove = (key: string): void => localStorage.removeItem(key);

export { cacheGet, cacheSet, cacheRemove };
