import { cookieGet } from '@ps/ui/services/cookies';
import { cacheGet } from '@ps/ui/services/local-storage-cache';

const isNext = typeof process !== 'undefined';
const getToken = async () => {
  if (isNext) {
    const userToken = await cookieGet<string | undefined>('token', undefined);
    return userToken;
  }
  return cacheGet<string | undefined>('token', undefined);
};

const getBaseApiUrl = () => {
  if (isNext) {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore VITE_API_URL is not defined in the browser
  return import.meta.env.VITE_API_URL;
};

const headers = async (skipHeaders = false) => {
  if (skipHeaders) return Promise.resolve({});
  const userToken = await getToken();
  return {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    'x-app-version': '1.0.0',
    ...(userToken ? { Authorization: `Bearer ${userToken}` } : {}),
  } as Record<string, string>;
};

const errorHandler = (err: unknown) => {
  if (err instanceof Error) {
    console.log('API', err.message);
    return Promise.reject(err.message);
  }
  return Promise.reject('Something went wrong! Please try again.');
};

const getPath = (url: string) =>
  url.startsWith('/') ? `${getBaseApiUrl()}${url}` : `${getBaseApiUrl()}/${url}`;

const responseHandler = (res: Promise<Response>) =>
  res
    .then(async (resp) => {
      if (resp.ok) return resp.json();
      const error = await resp.json();
      throw new Error(error.message);
    })
    .catch(errorHandler);

const get = async <T>(
  path: string,
  options: RequestInit & { skipHeaders?: boolean } = {},
): Promise<T> => {
  const url = getPath(path);
  return headers(options.skipHeaders).then((_headers) =>
    responseHandler(
      fetch(url, {
        ...options,
        headers: { ..._headers, ...options.headers },
      }),
    ),
  );
};

const post = async <Body extends object, Result>(
  path: string,
  body: Body,
  options: RequestInit = {},
): Promise<Result> => {
  return headers().then((_headers) =>
    responseHandler(
      fetch(getPath(path), {
        method: 'POST',
        body: JSON.stringify(body),
        ...options,
        headers: { ..._headers, ...options.headers },
      }),
    ),
  );
};

const patch = async <Body extends Record<string | number, unknown>, Result>(
  path: string,
  body: Body,
  options: RequestInit = {},
): Promise<Result> => {
  return headers().then((_headers) =>
    responseHandler(
      fetch(getPath(path), {
        method: 'PATCH',
        body: JSON.stringify(body),
        ...options,
        headers: { ..._headers, ...options.headers },
      }),
    ),
  );
};

const remove = async <T>(path: string, options: RequestInit = {}): Promise<T> => {
  return headers().then((_headers) =>
    responseHandler(
      fetch(getPath(path), {
        method: 'DELETE',
        ...options,
        headers: { ..._headers, ...options.headers },
      }),
    ),
  );
};

const API = {
  get,
  post,
  patch,
  remove,
};
export default API;
