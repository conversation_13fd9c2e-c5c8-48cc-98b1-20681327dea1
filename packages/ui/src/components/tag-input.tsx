// Unofficial ShadCN UI Compatible TagInput from https://github.com/shadcn-ui/ui/issues/3647#issuecomment-2926857409
'use client';

import { forwardRef, useEffect, useState } from 'react';
import { XIcon } from 'lucide-react';

import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';

type TagInputProps = Omit<React.ComponentProps<'input'>, 'value' | 'onChange'> & {
  value: string[];
  onChange: React.Dispatch<React.SetStateAction<string[]>>;
};
const TagInput = forwardRef<HTMLInputElement, TagInputProps>(
  ({ className, value, onChange, ...props }, ref) => {
    const [pendingDataPoint, setPendingDataPoint] = useState('');

    useEffect(() => {
      if (pendingDataPoint.includes(',')) {
        const newDataPoints = new Set([
          ...value,
          ...pendingDataPoint.split(',').map((chunk) => chunk.trim()),
        ]);
        onChange(Array.from(newDataPoints));
        setPendingDataPoint('');
      }
    }, [pendingDataPoint, onChange, value]);

    const addPendingDataPoint = () => {
      if (pendingDataPoint) {
        const newDataPoints = new Set([...value, pendingDataPoint]);
        onChange(Array.from(newDataPoints));
        setPendingDataPoint('');
      }
    };

    return (
      <div
        className={cn(
          'border-input dark:bg-input/30 flex min-h-9 w-full flex-wrap gap-1 rounded-md border bg-transparent p-1 text-sm disabled:cursor-not-allowed disabled:opacity-50',
          'focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]',
          className,
        )}
      >
        {value.map((item) => (
          <Badge className="max-h-7" key={item} variant="secondary">
            <span className="block max-w-xs truncate break-words">{item}</span>
            <Button
              variant="ghost"
              size="icon"
              className="ml-2 size-4 p-0"
              onClick={() => {
                onChange(value.filter((i) => i !== item));
              }}
            >
              <XIcon />
            </Button>
          </Badge>
        ))}
        <input
          className="placeholder:text-muted-foreground ml-2 flex-1 outline-none"
          value={pendingDataPoint}
          onChange={(e) => setPendingDataPoint(e.target.value)}
          onBlur={addPendingDataPoint}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ',') {
              e.preventDefault();
              addPendingDataPoint();
            } else if (e.key === 'Backspace' && pendingDataPoint.length === 0 && value.length > 0) {
              e.preventDefault();
              onChange(value.slice(0, -1));
            }
          }}
          {...props}
          ref={ref}
        />
      </div>
    );
  },
);

TagInput.displayName = 'TagInput';

export { TagInput };
