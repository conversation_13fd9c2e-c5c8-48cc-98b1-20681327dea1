'use client';

import { ToasterProps, Toaster } from 'sonner';
import { useTheme } from 'next-themes';

const Sonner = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();

  return (
    <Toaster
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      style={
        {
          '--normal-bg': 'var(--popover)',
          '--normal-text': 'var(--popover-foreground)',
          '--normal-border': 'var(--border)',
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export { Sonner };
