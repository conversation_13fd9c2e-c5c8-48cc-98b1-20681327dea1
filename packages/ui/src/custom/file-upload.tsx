import { Upload, X } from 'lucide-react';
import { ReactNode, useEffect } from 'react';
import { toast } from 'sonner';

import {
  FileUpload as FileUploadRoot,
  FileUploadItemMetadata,
  FileUploadItemProgress,
  FileUploadItemPreview,
  FileUploadItemDelete,
  FileUploadDropzone,
  FileUploadTrigger,
  FileUploadItem,
  FileUploadList,
  useFileUpload,
} from '@ps/ui/components/file-upload';
import { DEFAULT_MAX_FILE_SIZE, ONE_MB } from '@ps/ui/constants/file';
import { uploadFile } from '@ps/ui/services/upload';
import { FormLabel } from '@ps/ui/components/form';
import { Button } from '@ps/ui/components/button';

const DOCUMENT_MIME_TYPES = [
  'text/csv', // .csv
  'text/plain', // .txt
  'application/pdf', // .pdf
  'application/rtf', // .rtf
  'application/msword', // .doc
  'application/vnd.ms-excel', // .xls
  'application/vnd.ms-powerpoint', // .ppt
  'application/vnd.oasis.opendocument.text', // .odt
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
];
const MIME_TYPES = {
  image: 'image/*',
  audio: 'audio/*',
  video: 'video/*',
  document: DOCUMENT_MIME_TYPES.join(','),
};
const DEFAULT_FOLDER = {
  image: 'uploads/images',
  audio: 'uploads/audios',
  video: 'uploads/videos',
  document: 'uploads/documents',
};

type FileUploadProviderProps = {
  type?: 'image' | 'video' | 'audio' | 'document';
  folder?: string;
  userRef?: string;
} & Omit<Parameters<typeof FileUploadRoot>[0], 'accept'>;
const FileUploadProvider = ({
  type,
  folder,
  userRef,
  maxSize,
  maxFiles,
  ...props
}: FileUploadProviderProps) => {
  const handleFileUpload = async (
    files: File[],
    {
      onProgress,
      onSuccess,
      onError,
    }: {
      onProgress: (file: File, progress: number) => void;
      onSuccess: (file: File) => void;
      onError: (file: File, error: Error) => void;
    },
  ) => {
    const allowedTypes = type
      ? type === 'document'
        ? DOCUMENT_MIME_TYPES
        : MIME_TYPES[type]
          ? [MIME_TYPES[type]]
          : undefined
      : undefined;

    for (const file of files) {
      try {
        const s3Url = await uploadFile(file, {
          onProgress: (progress: number) => onProgress(file, progress),
          folder: folder || (type ? DEFAULT_FOLDER[type] : 'files'),
          maxSize: maxSize || DEFAULT_MAX_FILE_SIZE,
          allowedTypes,
          userRef,
        });

        // @ts-expect-error - file.url is a valid property
        file.url = s3Url;
        onSuccess(file);
      } catch (error) {
        onError(file, error as Error);
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to upload profile picture';
        toast.error(errorMessage);
      }
    }
  };

  return (
    <FileUploadRoot
      onUpload={handleFileUpload}
      accept={type ? MIME_TYPES[type] : undefined}
      maxSize={maxSize ? maxSize * ONE_MB : DEFAULT_MAX_FILE_SIZE}
      maxFiles={maxFiles || 1}
      {...props}
    />
  );
};

export type FileState = {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error' | 'idle';
};

type FileUploadProps = {
  onUpload: (urls: string[]) => void;
  onFilesChange?: (files: FileState[]) => void;
  selectZone?: ReactNode;
  label?: string;
  message?: string;
  buttonLabel?: string;
  fileSizeAndTypeMessage?: string;
};
function FileUpload({ selectZone, label, onUpload, onFilesChange, ...props }: FileUploadProps) {
  const files = useFileUpload((state) => Array.from(state.files.values()));

  useEffect(() => {
    if (files.length > 0 && onUpload) {
      // @ts-expect-error - file.file.url is a valid property
      const urls = files.map((file) => file.file.url).filter(Boolean);
      if (urls.length > 0) {
        onUpload(urls);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [files]);

  useEffect(() => {
    if (onFilesChange) {
      onFilesChange(files);
    }
  }, [files, onFilesChange]);

  return selectZone ? (
    <FileUploadTrigger asChild>{selectZone}</FileUploadTrigger>
  ) : (
    <div className="space-y-2">
      {!!label && <FormLabel>{label}</FormLabel>}

      <>
        <FileUploadDropzone>
          <div className="flex flex-col items-center gap-2 py-4 text-center">
            <Upload className="text-muted-foreground size-8" />
            <p className="text-muted-foreground text-sm">
              {props.message || 'Drag & drop your file or browse from your storage'}
            </p>
            {props.fileSizeAndTypeMessage && (
              <p className="text-muted-foreground text-xs">{props.fileSizeAndTypeMessage}</p>
            )}
            <FileUploadTrigger asChild>
              <Button variant="outline" size="sm">
                {props.buttonLabel || 'Select File'}
              </Button>
            </FileUploadTrigger>
          </div>
        </FileUploadDropzone>

        <FileUploadListComponent files={files} />
      </>
    </div>
  );
}

const FileUploadListComponent = ({ files }: { files: FileState[] }) => {
  return (
    <FileUploadList>
      {files.map(({ file }) => (
        <FileUploadItem key={file.name} value={file}>
          <div className="flex w-full items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <FileUploadItemPreview />
              <FileUploadItemMetadata />
            </div>
            <FileUploadItemDelete asChild>
              <Button variant="ghost" size="sm">
                <X className="size-4" />
              </Button>
            </FileUploadItemDelete>
          </div>
          <FileUploadItemProgress />
        </FileUploadItem>
      ))}
    </FileUploadList>
  );
};

export default FileUploadListComponent;

export { FileUploadProvider, FileUpload };
