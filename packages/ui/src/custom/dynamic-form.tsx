import { UseFormReturn, FieldValues, Control, Path } from 'react-hook-form';

import {
  <PERSON><PERSON>ontent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import { FormControl, FormMessage, FormField, FormLabel, FormItem } from '@ps/ui/components/form';
import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { toCamelCase } from '@ps/common/utils/string';
import { Textarea } from '@ps/ui/components/textarea';
import { Switch } from '@ps/ui/components/switch';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';

export type DynamicInputField = {
  type: 'text' | 'number' | 'select' | 'date' | 'checkbox' | 'radio';
  label: string;
  placeholder?: string;
  options?: { label: string; value: string }[];
  required?: boolean;
  description?: string;
  defaultValue?: string;
  min?: number;
  max?: number;
  step?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  multiple?: boolean;
  accept?: string;
  rows?: number;
  name: string;
};

interface DynamicFormProps<T extends FieldValues> {
  inputFields: DynamicInputField[];
  form: UseFormReturn<T>;
  className?: string;
  children?: React.ReactNode;
}

export function DynamicForm<T extends FieldValues>({
  inputFields,
  form,
  className,
  children,
}: DynamicFormProps<T>) {
  return (
    <div className={cn('space-y-4', className)}>
      {inputFields.map((field, i) => (
        <FormField
          key={i}
          control={form.control as Control<T>}
          name={(field.name || toCamelCase(field.label)) as Path<T>}
          render={({ field: rhfField }) => (
            <FormItem>
              <FormLabel>{field.label}</FormLabel>
              <FormControl>
                {field.type === 'text' || field.type === 'number' ? (
                  field.rows ? (
                    <Textarea placeholder={field.placeholder} rows={field.rows} {...rhfField} />
                  ) : (
                    <Input
                      type={field.type}
                      placeholder={field.placeholder}
                      min={field.min}
                      max={field.max}
                      step={field.step}
                      minLength={field.minLength}
                      maxLength={field.maxLength}
                      pattern={field.pattern}
                      {...rhfField}
                    />
                  )
                ) : field.type === 'select' && field.options ? (
                  <Select
                    value={rhfField.value}
                    onValueChange={rhfField.onChange}
                    defaultValue={field.defaultValue}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={field.placeholder || field.label} />
                    </SelectTrigger>
                    <SelectContent>
                      {field.options.map((opt) => (
                        <SelectItem key={opt.value} value={opt.value}>
                          {opt.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : field.type === 'checkbox' ? (
                  <Switch checked={!!rhfField.value} onCheckedChange={rhfField.onChange} />
                ) : field.type === 'radio' && field.options ? (
                  <RadioGroup value={rhfField.value} onValueChange={rhfField.onChange}>
                    {field.options.map((opt) => (
                      <label key={opt.value} className="flex items-center gap-2">
                        <RadioGroupItem value={opt.value} id={`${field.name}-${opt.value}`} />
                        {opt.label}
                      </label>
                    ))}
                  </RadioGroup>
                ) : field.type === 'date' ? (
                  <Input type="date" placeholder={field.placeholder} {...rhfField} />
                ) : null}
              </FormControl>
              {field.description && (
                <p className="text-muted-foreground text-sm">{field.description}</p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
      ))}
      {children}
    </div>
  );
}
