import {
  Pagination as <PERSON><PERSON><PERSON>R<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>revious,
  Pa<PERSON>ationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
} from '@ps/ui/components/pagination';
import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';

interface Props {
  total: number;
  limit: number;
  page: number;
  onPaging: (params: { limit: number; page: number; queryString: string }) => void;
  className?: string;
}

export function Pagination({ total, limit, page, onPaging, className }: Props) {
  const pagesCount = Math.ceil(total / limit);
  const from = limit * (page - 1) + 1;
  const to = Math.min(limit * page, total);

  const gotoPage = (pageNumber: number) => {
    const searchParams = new URLSearchParams(window.location.search);

    searchParams.delete('page');
    searchParams.append('page', String(pageNumber));

    onPaging({ limit, page: pageNumber, queryString: `?${searchParams.toString()}` });
  };

  const changeLimit = (_limit: number) => {
    const searchParams = new URLSearchParams(window.location.search);

    searchParams.delete('limit');
    searchParams.append('limit', String(_limit));

    const _pageCount = Math.max(Math.ceil(total / _limit), 1);
    if (page > _pageCount) {
      searchParams.delete('page');
      searchParams.append('page', String(_pageCount));
    }

    onPaging({
      limit: _limit,
      page,
      queryString: `?${searchParams.toString()}`,
    });
  };

  const next = () => {
    if (page < pagesCount) {
      gotoPage(page + 1);
    }
  };

  const prev = () => {
    if (page > 1) {
      gotoPage(page - 1);
    }
  };

  return (
    <div
      className={cn(
        'flex flex-wrap items-center justify-center gap-3 lg:flex-nowrap lg:justify-between',
        className,
      )}
    >
      <PaginationRoot className={cn('mx-0 w-auto')}>
        <PaginationContent>
          <PaginationItem>
            <Button className="px-0" variant="ghost" disabled={page === 1} onClick={prev}>
              <PaginationPrevious />
            </Button>
          </PaginationItem>

          {Array.from({ length: pagesCount ? pagesCount : 1 }).map((_, i) => {
            if ((page + 4 > i && page - 4 < i) || i === pagesCount - 1 || i === 0) {
              return (
                <PaginationItem key={i}>
                  {(page + 3 === i || page - 3 === i) && !(i === 0 || i === pagesCount - 1) ? (
                    <PaginationEllipsis />
                  ) : (
                    <Button
                      className={cn('px-0', { '!opacity-100': page === i + 1 })}
                      onClick={() => gotoPage(i + 1)}
                      disabled={page === i + 1}
                      variant="ghost"
                    >
                      <PaginationLink isActive={page === i + 1}>{i + 1}</PaginationLink>
                    </Button>
                  )}
                </PaginationItem>
              );
            }
            return null;
          })}

          <PaginationItem>
            <Button
              className="px-0"
              disabled={page === pagesCount || !pagesCount}
              variant="ghost"
              onClick={next}
            >
              <PaginationNext />
            </Button>
          </PaginationItem>
        </PaginationContent>
      </PaginationRoot>

      <div className="flex items-center gap-4">
        <p className="text-muted-foreground whitespace-nowrap text-xs">
          {total > limit
            ? `Showing results ${from} - ${to} of ${total}`
            : `Showing ${total} result${total > 1 ? 's' : ''}`}
        </p>

        <Select
          onValueChange={(value) => changeLimit(parseInt(value, 10))}
          value={limit.toString()}
        >
          <SelectTrigger className="">
            <SelectValue placeholder="Item Per Page" />
          </SelectTrigger>

          <SelectContent>
            <SelectItem value="10">10</SelectItem>
            <SelectItem value="25">25</SelectItem>
            <SelectItem value="50">50</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
