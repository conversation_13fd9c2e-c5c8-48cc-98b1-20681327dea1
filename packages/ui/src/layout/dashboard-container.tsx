import { cn } from '@ps/ui/lib/utils';

type DashboardContainerProps = React.ComponentProps<'div'> & {
  title?: string;
  label?: string;
  description?: string;
  leftActions?: React.ReactNode;
  actions?: React.ReactNode;
};
export function DashboardContainer({
  label,
  title,
  description,
  children,
  actions,
  leftActions,
  className,
}: DashboardContainerProps) {
  return (
    <div className={cn('mx-auto space-y-6 pb-16', className)}>
      <div className="flex flex-wrap items-end justify-between gap-5">
        {leftActions && <div className="flex items-center gap-2">{leftActions}</div>}

        {(title || label || description) && (
          <div className={cn('max-w-2xl space-y-3', { 'space-y-2': label })}>
            {label && <p className="text-muted-foreground text-sm">{label}</p>}
            {title && <h2 className="text-2xl font-bold">{title}</h2>}
            {description && <p className="text-muted-foreground text-sm">{description}</p>}
          </div>
        )}

        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>

      {children}
    </div>
  );
}
