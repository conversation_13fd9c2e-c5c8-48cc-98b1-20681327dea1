{"name": "tiptap-editor", "version": "0.0.0", "type": "module", "private": true, "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}}, "scripts": {"check-types": "tsc --noEmit"}, "dependencies": {"@floating-ui/react": "^0.27.12", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-subscript": "^2.22.3", "@tiptap/extension-superscript": "^2.22.3", "@tiptap/extension-task-item": "^2.22.3", "@tiptap/extension-task-list": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-typography": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/typescript-config": "workspace:*", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "sass": "^1.89.2", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}