{"name": "@ps/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./nest.js": "./nest.js", "./next-js": "./next.js", "./refine.js": "./refine.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.29.0", "@next/eslint-plugin-next": "^15.3.3", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-turbo": "^2.5.4", "globals": "^16.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}}