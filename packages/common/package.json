{"name": "@ps/common", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["./dist/**"], "publishConfig": {"access": "public"}, "typesVersions": {"*": {"*": ["src/*"]}}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./*": {"types": "./dist/*.d.ts", "import": "./dist/*.mjs", "require": "./dist/*.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0 --fix", "check-types": "tsc --noEmit"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/typescript-config": "workspace:*", "tsup": "^8.5.0", "eslint": "^9.29.0", "typescript": "^5.8.3"}}