interface App {
  name?: string;
  url: string;
}
export interface AppInfo {
  https: boolean;
  name: string;
  slug: string;
  domain: string;
  logo: string;
  server: App;
  client: App;
  admin: App;
  images: App;
  mails: {
    support: string;
  };
  social: {
    facebook: string;
    instagram: string;
    youtube: string;
    linkedin: string;
  };
}

const isHttps = true;
const domain = 'blogify.ai';
const getUrl = (app: string, env: 'development' | 'staging' | 'production' | 'test') => {
  const isDev = env === 'development';
  const protocol = isHttps ? 'https://' : 'http://';
  switch (app) {
    case 'client':
      return `${protocol}${isDev ? 'localhost:3000' : `lms.${domain}`}`;
    case 'admin':
      return `${protocol}${isDev ? 'localhost:3001' : `lms-admin.${domain}`}`;
    case 'server':
      return `${protocol}${isDev ? 'localhost:3333' : `lms-api.${domain}`}`;
    default:
      return '';
  }
};

export default function getAppConfig(
  env: 'development' | 'staging' | 'production' | 'test',
): AppInfo {
  return {
    https: isHttps,
    name: 'Blogify LMS',
    slug: 'blogify-lms',
    domain,
    logo: `https://${domain}/images/blogify.svg`,
    server: { url: getUrl('server', env) },
    client: { url: getUrl('client', env) },
    admin: { url: getUrl('admin', env) },
    images: { url: `https://images.${domain}` },
    mails: {
      support: `info@${domain}`,
    },
    social: {
      facebook: 'https://www.facebook.com/Blogifyai',
      instagram: 'https://www.instagram.com/blogifyaiinc',
      youtube: 'https://www.youtube.com/@blogifyinc.',
      linkedin: 'https://www.linkedin.com/company/blogifyai',
    },
  };
}
