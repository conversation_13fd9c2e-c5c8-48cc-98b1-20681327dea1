export function getInitials(fullName?: string) {
  if (!fullName) return '';
  const names = fullName.split(' ');
  const initials = names.map((name) => name.charAt(0).toUpperCase());
  return initials.join('');
}

export const formatToTitleCase = (text: string): string => {
  return text
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

export const toCamelCase = (str: string) => {
  str = str.replace(/(_|\s)/g, '');
  return str.charAt(0).toLowerCase() + str.slice(1);
};
