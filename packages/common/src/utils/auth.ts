const BASE_URLS = {
  facebook: 'https://www.facebook.com/v2.10/dialog/oauth',
  google: 'https://accounts.google.com/o/oauth2/v2/auth',
};

export const getSocialAuthUrlWithRedirect = (
  platform: 'google' | 'facebook',
  { redirectUrl, clientId, state }: { redirectUrl: string; clientId: string; state?: string },
): string => {
  const url = new URL(BASE_URLS[platform]);

  url.searchParams.set('client_id', clientId);
  url.searchParams.set('redirect_uri', redirectUrl);
  if (state) url.searchParams.set('state', state);

  switch (platform) {
    case 'facebook':
      url.searchParams.set('scope', 'email');
      return url.toString();
    case 'google':
      url.searchParams.set('prompt', 'consent');
      url.searchParams.set('response_type', 'code');
      url.searchParams.set('access_type', 'offline');
      url.searchParams.set(
        'scope',
        [
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/userinfo.profile',
        ].join(' '),
      );
      return url.toString();
    default:
      return '';
  }
};
