export const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hasValidProtocol = urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    const hasValidHostname = urlObj.hostname.split('.').filter(Boolean).length >= 2;
    return hasValidProtocol && hasValidHostname;
  } catch {
    return false;
  }
};

export const normalizeUrl = (url: string): string => {
  if (!url) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `https://${url}`;
};
