import { Category } from './category.types';
import { Base } from './base.types';

export interface Snippet extends Base {
  icon: string;
  name: string;
  description: string;
  promptTemplate?: string;
  enableChatBox: boolean;
  chatboxPlaceholder?: string;
  enableGradeField: boolean;
  inputFields?: {
    type: 'text' | 'number' | 'select' | 'date' | 'checkbox' | 'radio';
    label: string;
    placeholder?: string;
    options?: {
      label: string;
      value: string;
    }[];
    required?: boolean;
    description?: string;
    defaultValue?: string;
    min?: number;
    max?: number;
    step?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    multiple?: boolean;
    accept?: string;
    rows?: number;
  }[];

  category: Category;
}
