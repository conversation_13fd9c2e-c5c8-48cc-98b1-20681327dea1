import type { SubscriptionStatus, SubscriptionPlan, BillingPeriod } from '../subscription.types';
import type { Base } from './base.types';

export interface Team extends Base {
  name: string;

  // Stripe Information
  stripeCustomerId?: string;

  // Subscription Information
  subscriptionPlan: SubscriptionPlan;
  billingPeriod?: BillingPeriod;
  subscriptionStatus?: SubscriptionStatus;
  stripeSubscriptionId?: string;
  subscriptionStartDate?: string | Date;
  subscriptionEndDate?: string | Date;
  trialEndDate?: string | Date;
  cancelAtPeriodEnd?: boolean;
}
