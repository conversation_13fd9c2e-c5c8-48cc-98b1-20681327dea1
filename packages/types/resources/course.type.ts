import { Base } from './base.types';

interface CourseChapter {
  title: string;
  content: string;
  topics: {
    title: string;
    content: string;
    type: 'text' | 'embed' | 'image' | 'video' | 'audio' | 'document';
  }[];
  attachments: {
    name: string;
    type: 'image' | 'video' | 'audio' | 'document';
    url: string;
  }[];
}

export interface Course extends Base {
  coverImage: string;
  title: string;
  description: string;
  audience: string[];

  type: 'on-boarding' | 'training' | 'tutorial';
  status: 'generating' | 'generated' | 'published';

  chapters: CourseChapter[];
  duration?: number;

  inputs: {
    useKnowledgeBase: boolean;
  };
}
