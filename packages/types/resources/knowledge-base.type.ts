import type { Base } from './base.types';

export type KnowledgeBaseFileType = 'video' | 'audio' | 'image' | 'pdf' | 'doc' | 'url' | 'unknown';
export type KnowledgeBaseSource = 'upload' | 'url' | 'text';
export type KnowledgeBaseStatus = 'processing' | 'completed' | 'failed' | 'uploading' | 'analyzing';
export type KnowledgeBaseType =
  | 'text'
  | 'url'
  | 'video'
  | 'audio'
  | 'image'
  | 'pdf'
  | 'doc'
  | 'upload';

export interface KnowledgeBaseItem extends Base {
  id: string;
  type: KnowledgeBaseType;
  name?: string;
  text: string;
  score?: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata: any; // VectorDocument['metadata'];
}
