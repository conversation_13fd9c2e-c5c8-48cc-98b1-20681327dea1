import type { Base } from './base.types';
import type { Team } from './team.types';

export type UserRole = 'superadmin' | 'owner' | 'admin' | 'user';
export type UserStatus = 'active' | 'invited' | 'inactive' | 'deactivated';

export interface User extends Base {
  // User Info
  name: string;
  email: string;
  profilePicture?: string;

  // Auth Info
  googleId: string;
  role: UserRole;
  status: UserStatus;
  verified: boolean;
  lastLoginAt: Date;

  // IP & Geo Location
  ip?: string;
  city?: string;
  country?: string;

  // Relations
  team?: Team;
}
