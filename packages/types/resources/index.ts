declare global {
  export interface ListResponse<Resource> {
    data: Resource[];
    total: number;
    // TODO: Fixed this for for all API in the backend to follow above format
    items: Resource[];
    limit: number;
    offset: number;
    hasMore: boolean;
  }
}

export * from './category.types';
export * from './snippet.types';
export * from './course.type';
export * from './team.types';
export * from './user.type';
export {};
