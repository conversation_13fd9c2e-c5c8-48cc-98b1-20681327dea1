export type SubscriptionPlan = 'no_plan' | 'free' | 'pro';
export type BillingPeriod = 'monthly' | 'yearly' | 'lifetime';

export type SubscriptionStatus =
  | 'active'
  | 'canceled'
  | 'past_due'
  | 'unpaid'
  | 'incomplete'
  | 'incomplete_expired'
  | 'trialing';

export type Subscription = {
  plan: SubscriptionPlan;
  billingPeriod?: BillingPeriod;
  status?: SubscriptionStatus;
  startDate?: string;
  endDate?: string;
  trialEndDate?: string;
  cancelAtPeriodEnd?: boolean;
};
