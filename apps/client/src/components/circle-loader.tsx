import Image from 'next/image';
import React from 'react';

import styles from './circle-loader.module.css';

interface CircleLoaderProps {
  size?: number;
  className?: string;
}

const CHAT_ICON_SRC = '/images/loader.svg';

const CircleLoader: React.FC<CircleLoaderProps> = ({ size = 32, className = '' }) => (
  <span
    className={className}
    style={{
      display: 'inline-block',
      width: size,
      height: size,
    }}
    aria-busy="true"
    aria-label="Loading"
    role="status"
  >
    <Image
      src={CHAT_ICON_SRC}
      alt="Loading..."
      width={size}
      height={size}
      className={styles.loader}
      style={{
        display: 'block',
        width: size,
        height: size,
      }}
      unoptimized
    />
  </span>
);

export default CircleLoader;
