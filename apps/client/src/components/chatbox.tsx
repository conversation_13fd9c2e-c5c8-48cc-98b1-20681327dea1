'use client';

import { MessageSquareText, Send, Sparkles, Volume2 } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@ps/ui/components/button';
import { Slider } from '@ps/ui/components/slider';
import { cn } from '@ps/ui/lib/utils';

import CircleLoader from './circle-loader';

interface ChatBoxProps {
  message: string;
  className?: string;
  /**
   * Size of the chatbox. Can be 'sm', 'md', 'lg', or a number (pixels for avatar/icon). Default is 'md'.
   */
  size?: 'sm' | 'md' | 'lg' | number;
}

type ChatMessage = {
  id: number;
  text: string;
  sender: 'user' | 'ai';
};

const DUMMY_AI_REPLIES = [
  "That's a great question!",
  "Here's an idea you can try...",
  'Let me know if you need more help!',
  'Can you tell me more about your project?',
  "I'm here to assist you!",
];

const SIZE_MAP = {
  sm: {
    loader: 24,
    icon: 14,
    chatIcon: 20,
    minHeight: 40,
    bubble: 'max-w-xs text-xs',
    input: 'text-xs',
    button: 'px-2 py-1 text-xs',
    chatArea: 'max-w-md',
  },
  md: {
    loader: 32,
    icon: 18,
    chatIcon: 24,
    minHeight: 56,
    bubble: 'max-w-xs text-sm',
    input: 'text-sm',
    button: 'px-4 py-2 text-sm',
    chatArea: 'max-w-2xl',
  },
  lg: {
    loader: 48,
    icon: 24,
    chatIcon: 32,
    minHeight: 72,
    bubble: 'max-w-md text-base',
    input: 'text-base',
    button: 'px-6 py-3 text-base',
    chatArea: 'max-w-3xl',
  },
};

const ChatBox = ({ message, className = '', size = 'md' }: ChatBoxProps) => {
  const [chatOpen, setChatOpen] = useState(false);
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [thinking, setThinking] = useState(false);
  const [msgId, setMsgId] = useState(1);
  const [showVolume, setShowVolume] = useState(false);
  const [volume, setVolume] = useState(50);

  // Determine size config
  const sizeKey = typeof size === 'number' ? 'md' : size;
  const config = SIZE_MAP[sizeKey] || SIZE_MAP['md'];
  const loaderSize = typeof size === 'number' ? size : config.loader;
  const iconSize = typeof size === 'number' ? size : config.icon;
  // const chatIconSize = typeof size === 'number' ? size : config.chatIcon;
  const minHeight = typeof size === 'number' ? size + 24 : config.minHeight;
  // For width, use a maxWidth style or class
  const maxWidthClass = config.chatArea;

  const handleSend = () => {
    if (!input.trim()) return;
    const userMsg: ChatMessage = { id: msgId, text: input, sender: 'user' };
    setMessages((prev) => [...prev, userMsg]);
    setInput('');
    setMsgId((id) => id + 1);
    setThinking(true);
    // Simulate AI reply
    setTimeout(() => {
      const aiReply = DUMMY_AI_REPLIES[Math.floor(Math.random() * DUMMY_AI_REPLIES.length)];
      const aiMsg: ChatMessage = {
        id: msgId + 1,
        text: String(aiReply),
        sender: 'ai',
      };
      setMessages((prev) => [...prev, aiMsg]);
      setMsgId((id) => id + 1);
      setThinking(false);
    }, 1200);
  };

  return (
    <div className={cn('w-full', className)}>
      <div className={cn('mx-auto', 'bg-gray-100', 'rounded-lg', 'w-full', maxWidthClass)}>
        {/* Thinking indicator (always visible when chat is open) */}
        <div className={cn('flex items-center gap-2 px-3 py-1')}>
          <span className={cn('text-xs', 'text-gray-400', config.input)}>Thinking..</span>
          <Sparkles size={iconSize} className={cn('text-gray-400')} />
        </div>
        {/* Chat area (appears above input when open) */}
        {chatOpen && (
          <div
            className={cn(
              'mb-2',
              'bg-white',
              'border',
              'border-gray-200',
              'border-t-0',
              'rounded-xl',
              'shadow',
              'p-4',
              'w-full',
            )}
          >
            {/* Message bubbles */}
            <div className={cn('flex flex-col gap-2 overflow-y-auto')} style={{ maxHeight: 320 }}>
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={cn('flex', msg.sender === 'user' ? 'justify-end' : 'justify-start')}
                >
                  <div
                    className={cn(
                      'rounded-lg px-4 py-2',
                      'bg-gray-100',
                      'text-gray-800',
                      config.bubble,
                    )}
                  >
                    {msg.text ?? ''}
                  </div>
                </div>
              ))}
            </div>
            {/* Example initial message bubble if no messages yet */}
            {messages.length === 0 && (
              <div className={cn('mb-2')}>
                <div
                  className={cn(
                    'bg-gray-100',
                    'rounded-lg',
                    'px-4',
                    'py-2',
                    'text-gray-800',
                    'w-fit',
                    config.bubble,
                  )}
                >
                  Using a school garden for science lessons is a fantastic way to engage students
                  with hands-on learning and connect them to real-world natural processes. Here are
                  some ideas on how to integrate the garden into your science curriculum:
                </div>
              </div>
            )}
            {/* Input area */}
            <div
              className={cn(
                'flex items-center',
                'border',
                'border-gray-200',
                'rounded-xl',
                'px-2',
                'py-1',
                'bg-white',
                'mt-2',
                'shadow',
                'mx-auto',
              )}
            >
              <input
                className={cn(
                  'flex-1',
                  'border-none',
                  'outline-none',
                  'p-2',
                  'bg-transparent',
                  config.input,
                )}
                placeholder="Write here.."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSend();
                }}
                disabled={thinking}
              />
              <Button
                variant="default"
                className={cn('rounded-full')}
                onClick={handleSend}
                disabled={thinking}
              >
                <Send size={iconSize} />
                Send
              </Button>
            </div>
          </div>
        )}

        {/* Chat input area */}
        <div
          className={cn(
            'flex items-center',
            'bg-background',
            'border',
            'border-border',
            'rounded-xl',
            'shadow-sm',
            'w-full',
            'px-4',
            'py-2',
          )}
          style={{ minHeight }}
        >
          {/* Circle Loader Avatar */}
          <span
            className={cn('rounded-full', 'mr-3', 'flex items-center justify-center')}
            style={{ width: loaderSize, height: loaderSize }}
          >
            <CircleLoader size={loaderSize} />
          </span>
          {/* Message */}
          <span className={cn('flex-1', 'text-muted-foreground', config.input)}>{message}</span>
          {/* Icon Buttons */}
          <span className={cn('flex', 'gap-2', 'ml-2', 'relative')}>
            <Button
              variant={'outline'}
              style={{ width: loaderSize, height: loaderSize }}
              aria-label="Open chat"
              onClick={() => setChatOpen((v) => !v)}
            >
              <MessageSquareText size={iconSize} strokeWidth={1.5} />
            </Button>
            <Button
              variant={'outline'}
              style={{ width: loaderSize, height: loaderSize }}
              aria-label="Voice"
              onClick={() => setShowVolume((v) => !v)}
            >
              <Volume2 size={iconSize} strokeWidth={1.5} />
            </Button>
            {/* Volume slider popup */}
            {showVolume && (
              <div
                className={cn(
                  'absolute right-0 top-16 z-10 mb-2',
                  'flex items-center rounded-lg border bg-white px-4 py-4 shadow-lg',
                  'w-48',
                )}
                style={{ transform: 'translateY(-50%)' }}
              >
                <div className="relative w-full">
                  <Slider
                    min={0}
                    max={100}
                    value={[volume]}
                    onValueChange={([v]: number[]) => setVolume(v ?? 0)}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ChatBox;
