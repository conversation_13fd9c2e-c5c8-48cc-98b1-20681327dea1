.loader {
  animation: circle-loader-rotate-blur 3.2s linear infinite;
  display: block;
}

@keyframes circle-loader-rotate-blur {
  0% {
    transform: rotate(0deg);
    filter: blur(1px);
  }

  12.5% {
    transform: rotate(45deg);
    filter: blur(1px);
  }

  25% {
    transform: rotate(90deg);
    filter: blur(1px);
  }

  37.5% {
    transform: rotate(135deg);
    filter: blur(1px);
  }

  50% {
    transform: rotate(180deg);
    filter: blur(1px);
  }

  62.5% {
    transform: rotate(225deg);
    filter: blur(1px);
  }

  75% {
    transform: rotate(270deg);
    filter: blur(1px);
  }

  87.5% {
    transform: rotate(315deg);
    filter: blur(1px);
  }

  100% {
    transform: rotate(360deg);
    filter: blur(1px);
  }
}
