'use client';

import type { SubscriptionPlan, BillingPeriod } from '@ps/types';

import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@ps/ui/components/button';
import API from '@ps/ui/services/api';

interface CheckoutButtonProps {
  plan: SubscriptionPlan;
  billingPeriod: BillingPeriod;
  children: React.ReactNode;
  className?: string;
}

export default function CheckoutButton({
  plan,
  billingPeriod,
  children,
  className,
}: CheckoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleCheckout = async () => {
    try {
      setIsLoading(true);

      // Handle free plan differently - no need to go through Stripe
      if (plan === 'free') {
        const resp = await API.post<
          Record<string, string>,
          { success: boolean; redirectUrl: string }
        >('/integrations/stripe/activate-free-plan', {});

        if (resp?.success && resp?.redirectUrl) {
          window.location.href = resp.redirectUrl;
        } else {
          toast.error('Failed to activate free plan');
        }
      } else {
        // For paid plans, create a Stripe checkout session
        const resp = await API.post<
          { plan: SubscriptionPlan; billingPeriod: BillingPeriod },
          { url: string }
        >('/integrations/stripe/create-checkout-session', { plan, billingPeriod });

        if (resp?.url) {
          window.location.href = resp.url;
        } else {
          toast.error('Failed to create checkout session');
        }
      }
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error('An error occurred during the checkout process');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button onClick={handleCheckout} disabled={isLoading} className={className} variant="default">
      {isLoading ? 'Loading...' : children}
    </Button>
  );
}
