'use client';

// import { <PERSON>, Sunset, Trees, Book, Menu, Zap } from 'lucide-react';
import { signOut, signIn } from 'next-auth/react';
import { <PERSON>, Menu } from 'lucide-react';
import Link from 'next/link';

// import {
//   navigationMenuTriggerStyle,
//   NavigationMenuContent,
//   NavigationMenuTrigger,
//   NavigationMenuItem,
//   NavigationMenuLink,
//   NavigationMenuList,
//   NavigationMenu,
// } from '@ps/ui/components/navigation-menu';
// import {
//   AccordionContent,
//   AccordionTrigger,
//   AccordionItem,
//   Accordion,
// } from '@ps/ui/components/accordion';
import {
  SheetContent,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
  Sheet,
} from '@ps/ui/components/sheet';
import { Button } from '@ps/ui/components/button';
import useAppContext from '@/providers/app-context';
import config from '@/config';

import ModeToggle from '../mode-toggle';

// const TopMenu = [
//   {
//     name: 'Getting Started',
//     Dropdown: [
//       {
//         title: 'Introduction',
//         description: 'Re-usable components built using Radix UI and Tailwind CSS.',
//         icon: <Zap className="size-5 shrink-0" />,
//         href: '/docs',
//       },
//       {
//         title: 'Installation',
//         description: 'How to install dependencies and structure your app.',
//         icon: <Sunset className="size-5 shrink-0" />,
//         href: '/docs/installation',
//       },
//       {
//         title: 'Typography',
//         description: 'Styles for headings, paragraphs, lists...etc',
//         icon: <Trees className="size-5 shrink-0" />,
//         href: '/docs/primitives/typography',
//       },
//     ],
//   },
//   {
//     name: 'Resources',
//     Dropdown: [
//       {
//         title: 'Help Center',
//         description: 'Get all the answers you need right here',
//         icon: <Zap className="size-5 shrink-0" />,
//         href: '/',
//       },
//       {
//         title: 'Contact Us',
//         description: 'We are here to help you with any questions you have',
//         icon: <Sunset className="size-5 shrink-0" />,
//         href: '/',
//       },
//       {
//         title: 'Status',
//         description: 'Check the current status of our services and APIs',
//         icon: <Trees className="size-5 shrink-0" />,
//         href: '/',
//       },
//       {
//         title: 'Terms of Service',
//         description: 'Our terms and conditions for using our services',
//         icon: <Book className="size-5 shrink-0" />,
//         href: '/',
//       },
//     ],
//   },
//   { name: 'Pricing', href: '/#pricing' },
//   { name: 'Blog', href: '/blog' },
// ];

export default function Header() {
  return (
    <header className="border-border/40 bg-background/80 sticky top-0 z-50 w-full border-b px-3 py-4 backdrop-blur-md">
      <div className="mx-auto max-w-7xl">
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <Link href="/" className="flex items-center gap-1">
                <Rocket size={32} strokeWidth={2.7} />
                <span className="text-xl font-bold">{config.app.name}</span>
              </Link>
            </div>
          </div>
          <div className="flex items-center gap-6">
            {/* <div className="flex items-center">
              <NavigationMenu className="relative z-[100]">
                <NavigationMenuList>
                  {TopMenu.map((menu, idx) =>
                    menu.Dropdown ? (
                      <NavigationMenuItem key={idx}>
                        <NavigationMenuTrigger className="bg-transparent">
                          {menu.name}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent className="z-[100]">
                          {menu.name === 'Getting Started' ? (
                            <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                              <li className="row-span-3">
                                <NavigationMenuLink asChild>
                                  <Link
                                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                                    href="/"
                                  >
                                    <Badge className="h-6 w-6" />
                                    <div className="mb-2 mt-4 text-lg font-medium">shadcn/ui</div>
                                    <p className="text-sm leading-tight text-muted-foreground">
                                      Beautifully designed components that you can copy and paste
                                      into your apps. Accessible. Customizable. Open Source.
                                    </p>
                                  </Link>
                                </NavigationMenuLink>
                              </li>
                              {menu.Dropdown.map((item, idx) => (
                                <ListItem key={idx} href={item.href} title={item.title}>
                                  {item.description}
                                </ListItem>
                              ))}
                            </ul>
                          ) : (
                            <ul className="w-80 p-3">
                              {menu.Dropdown.map((item, index) => (
                                <li key={index} className="group">
                                  <NavigationMenuLink asChild>
                                    <Link
                                      className={cn(
                                        'flex flex-row select-none gap-4 rounded-md p-3 leading-none no-underline outline-hidden transition-colors',
                                      )}
                                      href={item.href}
                                    >
                                      <span className="transition-colors group-hover:text-primary">
                                        {item.icon}
                                      </span>
                                      <div>
                                        <div className="text-sm font-semibold">{item.title}</div>
                                        <p className="text-sm leading-snug text-muted-foreground">
                                          {item.description}
                                        </p>
                                      </div>
                                    </Link>
                                  </NavigationMenuLink>
                                </li>
                              ))}
                            </ul>
                          )}
                        </NavigationMenuContent>
                      </NavigationMenuItem>
                    ) : (
                      <NavigationMenuItem key={idx}>
                        <Link className={navigationMenuTriggerStyle()} href={menu.href}>
                          {menu.name}
                        </Link>
                      </NavigationMenuItem>
                    ),
                  )}
                </NavigationMenuList>
              </NavigationMenu>
            </div> */}
            <ModeToggle />
            <div className="flex gap-2">
              <AuthButtons />
            </div>
          </div>
        </nav>

        {/* Mobile Menu */}
        <div className="block lg:hidden">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Link href="/" className="flex items-center gap-1">
                <Rocket size={32} strokeWidth={2.7} />
                <span className="text-xl font-bold">{config.app.name}</span>
              </Link>
            </div>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant={'outline'} size={'icon'}>
                  <Menu className="size-4" />
                </Button>
              </SheetTrigger>
              <SheetContent className="overflow-y-auto px-4">
                <SheetHeader className="border-b px-0 pb-4">
                  <SheetTitle>
                    <Link href="/" className="flex items-center gap-1">
                      <Rocket size={32} strokeWidth={2.7} />
                      <span className="text-xl font-bold">{config.app.name}</span>
                    </Link>
                  </SheetTitle>
                </SheetHeader>
                {/* <div className="flex flex-col">
                  {TopMenu.map((menu, idx) =>
                    menu.Dropdown ? (
                      <Accordion key={idx} type="single" collapsible className="w-full mb-1">
                        <AccordionItem value={menu.name} className="border-b border-border/40">
                          <AccordionTrigger className="py-3 font-medium hover:no-underline text-base">
                            {menu.name}
                          </AccordionTrigger>
                          <AccordionContent className="pt-2 pb-3">
                            <div className="flex flex-col space-y-3">
                              {menu.Dropdown.map((item, index) => (
                                <Link
                                  key={index}
                                  className={cn(
                                    'flex items-center gap-3 rounded-md p-2 transition-colors hover:bg-accent hover:text-accent-foreground',
                                  )}
                                  href={item.href}
                                >
                                  <div className="flex-shrink-0 text-primary">{item.icon}</div>
                                  <div>
                                    <div className="font-medium text-sm">{item.title}</div>
                                    <p className="text-xs text-muted-foreground mt-0.5">
                                      {item.description}
                                    </p>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ) : (
                      <Link
                        key={idx}
                        href={menu.href}
                        className="py-3 px-1 font-medium text-base border-b border-border/40 flex items-center"
                      >
                        {menu.name}
                      </Link>
                    ),
                  )}
                </div> */}
                {/* border-t pt-4 */}
                <div className="">
                  <div className="mt-2 flex flex-col gap-2">
                    <AuthButtons />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}

const AuthButtons = () => {
  const { isAuthenticated } = useAppContext();

  return (
    <>
      {isAuthenticated ? (
        <>
          <Button variant="ghost" asChild>
            <Link href="/dashboard">Dashboard</Link>
          </Button>
          <Button onClick={() => signOut()}>Logout</Button>
        </>
      ) : (
        <>
          <Button variant="ghost" size="sm" onClick={() => signIn()}>
            Log In
          </Button>
          <Button size="sm" asChild>
            <Link href="/signup">Get Started</Link>
          </Button>
        </>
      )}
    </>
  );
};

// const ListItem = React.forwardRef<React.ElementRef<'a'>, React.ComponentPropsWithoutRef<'a'>>(
//   ({ className, title, children, ...props }, ref) => {
//     return (
//       <li>
//         <NavigationMenuLink asChild>
//           <a
//             ref={ref}
//             className={cn(
//               'block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground',
//               className,
//             )}
//             {...props}
//           >
//             <div className="text-sm font-medium leading-none">{title}</div>
//             <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">{children}</p>
//           </a>
//         </NavigationMenuLink>
//       </li>
//     );
//   },
// );
// ListItem.displayName = 'ListItem';
