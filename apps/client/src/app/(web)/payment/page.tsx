'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  Card,
} from '@ps/ui/components/card';
import { Ta<PERSON><PERSON>ontent, Ta<PERSON>Trigger, Ta<PERSON>List, Tabs } from '@ps/ui/components/tabs';
import CheckoutButton from '@/components/subscription/checkout-button';

export default function PaymentPage() {
  return (
    <div className="container mx-auto py-10">
      <div className="mx-auto mb-10 max-w-md text-center">
        <h2 className="text-3xl font-bold">Choose a Plan</h2>
        <p className="text-muted-foreground mt-2">Select a plan to continue</p>
      </div>

      <Tabs defaultValue="monthly" className="mx-auto max-w-4xl">
        <div className="mb-8 flex justify-center">
          <TabsList>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="yearly">Yearly (Save 20%)</TabsTrigger>
            <TabsTrigger value="lifetime">Lifetime</TabsTrigger>
          </TabsList>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {/* Free Plan */}
          <Card>
            <CardHeader>
              <CardTitle>Free</CardTitle>
              <CardDescription>Basic features for personal use</CardDescription>
              <div className="mt-4 text-4xl font-bold">$0</div>
            </CardHeader>
            <CardContent className="space-y-2">
              <ul className="space-y-2">
                <li className="flex items-center">
                  <span className="mr-2">✓</span> Basic video editing
                </li>
                <li className="flex items-center">
                  <span className="mr-2">✓</span> 720p export
                </li>
                <li className="flex items-center">
                  <span className="mr-2">✓</span> 5 projects
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <CheckoutButton plan="free" billingPeriod="monthly" className="w-full">
                Get Started
              </CheckoutButton>
            </CardFooter>
          </Card>

          {/* Pro Plan - Monthly */}
          <TabsContent value="monthly" className="mt-0">
            <Card className="border-primary">
              <CardHeader>
                <div className="bg-primary text-primary-foreground mb-2 w-fit rounded-full px-2 py-1 text-xs">
                  POPULAR
                </div>
                <CardTitle>Pro</CardTitle>
                <CardDescription>Advanced features for professionals</CardDescription>
                <div className="mt-4 text-4xl font-bold">
                  $19<span className="text-sm font-normal">/month</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Everything in Free
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Advanced video editing
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> 4K export
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Unlimited projects
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Priority support
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <CheckoutButton plan="pro" billingPeriod="monthly" className="w-full">
                  Subscribe
                </CheckoutButton>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Pro Plan - Yearly */}
          <TabsContent value="yearly" className="mt-0">
            <Card className="border-primary">
              <CardHeader>
                <div className="bg-primary text-primary-foreground mb-2 w-fit rounded-full px-2 py-1 text-xs">
                  BEST VALUE
                </div>
                <CardTitle>Pro</CardTitle>
                <CardDescription>Advanced features for professionals</CardDescription>
                <div className="mt-4 text-4xl font-bold">
                  $180<span className="text-sm font-normal">/year</span>
                </div>
                <div className="text-muted-foreground text-sm">$15/month, billed annually</div>
              </CardHeader>
              <CardContent className="space-y-2">
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Everything in Free
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Advanced video editing
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> 4K export
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Unlimited projects
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Priority support
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <CheckoutButton plan="pro" billingPeriod="yearly" className="w-full">
                  Subscribe
                </CheckoutButton>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Pro Plan - Lifetime */}
          <TabsContent value="lifetime" className="mt-0">
            <Card className="border-primary">
              <CardHeader>
                <div className="bg-primary text-primary-foreground mb-2 w-fit rounded-full px-2 py-1 text-xs">
                  ONE-TIME PAYMENT
                </div>
                <CardTitle>Pro Lifetime</CardTitle>
                <CardDescription>Advanced features forever</CardDescription>
                <div className="mt-4 text-4xl font-bold">$499</div>
                <div className="text-muted-foreground text-sm">One-time payment</div>
              </CardHeader>
              <CardContent className="space-y-2">
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Everything in Pro
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> Lifetime access
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> All future updates
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2">✓</span> VIP support
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <CheckoutButton plan="pro" billingPeriod="lifetime" className="w-full">
                  Buy Lifetime
                </CheckoutButton>
              </CardFooter>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
