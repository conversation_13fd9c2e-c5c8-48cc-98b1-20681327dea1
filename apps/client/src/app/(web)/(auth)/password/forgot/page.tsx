'use client';

import type { ForgotPasswordSchema } from '@/app/(web)/(auth)/utils/validation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import Link from 'next/link';

import {
  FormMessage,
  FormControl,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import { forgotPasswordSchema } from '@/app/(web)/(auth)/utils/validation';
import { CardContent, Card } from '@ps/ui/components/card';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';
import API from '@ps/ui/services/api';

const forgotPassword = (
  values: ForgotPasswordSchema,
): Promise<{ status?: string; message?: string }> =>
  API.post<ForgotPasswordSchema, { status?: string; message?: string }>(
    '/auth/password/forgot',
    values,
  );

const ForgotPasswordPage = () => {
  const form = useForm<ForgotPasswordSchema>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const { mutate, isSuccess } = useMutation({
    mutationFn: forgotPassword,
  });

  const onSubmit = (values: ForgotPasswordSchema) => mutate(values);

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-md">
        <div className={cn('flex flex-col gap-6')}>
          <Card className="overflow-hidden p-0">
            <CardContent className="grid p-0 md:grid-cols-1">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col items-center text-center">
                      <h1 className="text-2xl font-bold">Forgot Password</h1>
                      <p className="text-muted-foreground text-balance">
                        {isSuccess
                          ? 'We just sent you a mail with reset instructions'
                          : 'Please enter the email address associated with your account'}
                      </p>
                    </div>

                    {isSuccess ? (
                      <div className="space-y-4 text-center">
                        <p className="text-base leading-normal">
                          Please click on the link provided in the mail to proceed.
                        </p>
                        <div className="text-center text-sm">
                          <Link href="/login" className="underline underline-offset-4">
                            Back to Login
                          </Link>
                        </div>
                      </div>
                    ) : (
                      <>
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email</FormLabel>
                              <FormControl>
                                <Input type="email" placeholder="<EMAIL>" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={form.formState.isSubmitting}
                        >
                          {form.formState.isSubmitting ? 'Sending...' : 'Send Reset Link'}
                        </Button>

                        <div className="text-center text-sm">
                          Remember your password?{' '}
                          <Link href="/login" className="underline underline-offset-4">
                            Back to Login
                          </Link>
                        </div>
                      </>
                    )}
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
          <div className="text-muted-foreground *:[a]:hover:text-primary *:[a]:underline *:[a]:underline-offset-4 text-balance text-center text-xs">
            By clicking continue, you agree to our <a href="#">Terms of Service</a> and{' '}
            <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  );
};
export default ForgotPasswordPage;
