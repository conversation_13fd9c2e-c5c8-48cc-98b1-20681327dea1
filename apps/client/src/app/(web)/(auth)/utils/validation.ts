import { z } from 'zod';

export const emailSchema = z
  .string({ required_error: 'Email is required' })
  .email('Email address is invalid')
  .toLowerCase()
  .trim();

const passwordSchema = z
  .string({ required_error: 'Password is required' })
  .min(8, 'Password must have at least 8 characters')
  .max(256, 'Password must be less than 256 characters')
  .regex(/\w*[a-z]\w*/, 'Password must have a lowercase letter')
  .regex(/\w*[A-Z]\w*/, 'Password must have a capital letter')
  .regex(/\d/, 'Password must have a number');

//* Login
export const loginSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});
export type LoginSchema = z.infer<typeof loginSchema>;

//* Signup
export const signupSchema = z.object({
  name: z.string({ required_error: 'Name is required' }),
  email: emailSchema,
  password: passwordSchema,
});
export type SignupSchema = z.infer<typeof signupSchema>;

//* Forgot Password
export const forgotPasswordSchema = z.object({
  email: emailSchema,
});
export type ForgotPasswordSchema = z.infer<typeof forgotPasswordSchema>;

//* Reset Password
export const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirm_password: z.string({ required_error: 'Confirm Password is required' }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: 'Passwords do not match',
    path: ['confirm_password'],
  });
export type ResetPasswordSchema = z.infer<typeof resetPasswordSchema>;
