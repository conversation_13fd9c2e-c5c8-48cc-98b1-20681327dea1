'use server';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { signIn } from '@/auth';

import { LoginSchema } from '../utils/validation';
import { AuthError } from '../utils';

export const login = async (
  values: Partial<LoginSchema> & {
    token?: string;
    provider?: 'custom'; // (custom -> for social logins via redirect)
    redirect?: boolean;
  },
  callbackUrl?: string | null,
): Promise<{ otpSent?: boolean; message?: string; csrfToken?: string }> => {
  try {
    await signIn('credentials', {
      ...values,
      redirectTo: callbackUrl || '/',
    });
    return { message: '' };
  } catch (error) {
    if (isRedirectError(error)) {
      // console.log('Redirect error:', error);
      throw error; // Next.js. will handle this
    }
    const err = error as AuthError;
    if (error instanceof AuthError) {
      const errorObj = JSON.parse(err?.message || '{}');
      return { ...errorObj };
    }
    return { message: err?.message || 'Something went wrong!' };
  }
};
