'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { login } from '../actions/login';

export default function AuthRedirect() {
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirectTo');
  const token = searchParams.get('token') || '';

  useEffect(() => {
    login({ token, provider: 'custom' }, redirectTo);
  }, [redirectTo, token]);

  return <div className="sm:[h-85vh] h-[90vh] px-3 py-6 text-center">Redirecting...</div>;
}
