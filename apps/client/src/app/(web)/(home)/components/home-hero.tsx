'use client';
import { <PERSON><PERSON><PERSON> } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import Image from 'next/image';

import DashboardImage from '@img/page-assets/home/<USER>';

export default function Hero01() {
  return (
    <section className="pb-20 pt-36 md:pb-32 md:pt-48">
      <div className="mx-3 grid place-items-center gap-8 md:mx-auto lg:max-w-screen-xl">
        <div className="space-y-8 text-center">
          <Badge variant="outline" className="py-1 text-sm">
            <span className="text-primary mr-2">
              <Badge>New</Badge>
            </span>
            <span> AI-powered features are here! </span>
          </Badge>

          <div className="mx-auto max-w-screen-md text-center text-4xl font-bold tracking-tighter md:text-7xl">
            <h1>
              The AI-Powered
              <span className="from-primary bg-gradient-to-r bg-clip-text px-2 text-transparent">
                Learning Management System
              </span>
              for Modern Education
            </h1>
          </div>

          <p className="text-muted-foreground mx-auto max-w-screen-md text-xl">
            Empower your school, college, or university with an AI-driven platform for creating
            interactive learning materials, managing courses, and building a centralized knowledge
            base.
          </p>

          <div className="space-y-4 md:space-x-4 md:space-y-0">
            <Button className="group/arrow w-5/6 font-bold md:w-1/4" asChild>
              <Link href="/signup">
                Get Started
                <ArrowRight className="ml-2 size-5 transition-transform group-hover/arrow:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>

        <div className="group relative mt-14">
          <div className="bg-primary/50 absolute left-1/2 top-2 mx-auto h-24 w-[90%] -translate-x-1/2 transform rounded-full blur-3xl lg:-top-8 lg:h-80"></div>
          <Image
            className="border-secondary border-t-primary/30 relative mx-auto flex w-full items-center rounded-lg border border-t-2 leading-none md:w-[1200px]"
            src={DashboardImage}
            alt="dashboard"
            priority
          />
        </div>
      </div>
    </section>
  );
}
