import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON> } from '@ps/ui/components/tabs';
import { MessageCircleMore, ListChecks, Lightbulb } from 'lucide-react';
import { Badge } from '@ps/ui/components/badge';
import Image, { StaticImageData } from 'next/image';

import LearningMaterialsImage from '@img/page-assets/home/<USER>';
import KnowledgeBaseImage from '@img/page-assets/home/<USER>';
import CoursesImage from '@img/page-assets/home/<USER>';

const featuresData = [
  {
    value: 'panel-0',
    title: "Centralize Your Institution's Knowledge.",
    description:
      'Empower your AI by uploading documents, videos, audio files, and PDFs. Create a single source of truth for your entire institution, making information instantly accessible.',
    features: [
      {
        title: 'Train Your AI',
        description: 'Upload documents, audio, video, and PDFs to train your custom AI model.',
        imgSrc: 'https://cdn-icons-png.flaticon.com/512/4727/4727266.png',
      },
      {
        title: 'Semantic Search',
        description:
          'Enable students and faculty to find the exact information they need instantly.',
        imgSrc: 'https://cdn-icons-png.flaticon.com/512/584/584796.png',
      },
    ],
    imageSrc: KnowledgeBaseImage,
  },
  {
    value: 'panel-1',
    title: 'Create Interactive Learning Experiences',
    description:
      'Go beyond static text. Generate dynamic, interactive learning materials like quizzes, puzzles, and simulations to keep students engaged and improve retention.',
    features: [
      {
        title: 'Interactive Quizzes & Games',
        description:
          'Automatically generate engaging quizzes and educational games from your materials.',
        imgSrc: 'https://cdn-icons-png.flaticon.com/512/6106/6106288.png',
      },
      {
        title: 'Dynamic Material Generation',
        description: 'Create diverse, interactive learning materials tailored to your curriculum.',
        imgSrc: 'https://cdn-icons-png.flaticon.com/512/2313/2313906.png',
      },
    ],
    imageSrc: LearningMaterialsImage,
  },
  {
    value: 'panel-2',
    title: 'Streamline Course & Curriculum Management',
    description:
      'Effortlessly design and manage courses for teacher onboarding, student curriculums, and professional development. Our intuitive builder makes it simple.',
    features: [
      {
        title: 'Intuitive Course Builder',
        description:
          'Organize modules, lessons, and materials with a simple drag-and-drop interface.',
        imgSrc: 'https://cdn-icons-png.flaticon.com/512/3340/3340200.png',
      },
      {
        title: 'Teacher & Student Dashboards',
        description: 'Track student progress, manage assignments, and facilitate communication.',
        imgSrc: 'https://cdn-icons-png.flaticon.com/512/5405/5405929.png',
      },
    ],
    imageSrc: CoursesImage,
  },
];

export default function HomeFeatures() {
  return (
    <section className="mx-auto max-w-7xl pb-20 pt-20 md:pb-32 md:pt-32">
      <div className="mx-auto max-w-4xl space-y-4 pb-16 text-center">
        <Badge>FEATURES</Badge>
        <h2 className="mx-auto mt-4 text-3xl font-bold tracking-tight sm:text-5xl">
          A Powerful Toolkit for Modern Educators
        </h2>
        <p className="text-muted-foreground pt-1 text-xl">
          Blogify LMS provides a suite of AI-powered tools to generate, manage, and deliver engaging
          learning experiences for educational institutions.
        </p>
      </div>
      <Tabs defaultValue="panel-0">
        <TabsList className="bg-background flex h-auto w-full flex-col gap-2 md:flex-row">
          <TabsTrigger
            value="panel-0"
            className="text-primary hover:border-primary/40 data-[state=active]:border-primary flex w-full flex-col items-start justify-start gap-1 whitespace-normal rounded-md border p-4 text-left"
          >
            <div className="flex items-center gap-2 md:flex-col md:items-start lg:gap-4">
              <span className="bg-accent flex size-8 items-center justify-center rounded-full lg:size-10">
                <MessageCircleMore className="text-primary size-4" />
              </span>
              <p className="text-primary text-lg font-semibold md:text-2xl lg:text-xl">
                AI Knowledge Base
              </p>
            </div>
            <p className="text-muted-foreground font-normal md:block">
              Train your AI with institution-specific documents, videos, and more.
            </p>
          </TabsTrigger>
          <TabsTrigger
            value="panel-1"
            className="text-primary hover:border-primary/40 data-[state=active]:border-primary flex w-full flex-col items-start justify-start gap-1 whitespace-normal rounded-md border p-4 text-left"
          >
            <div className="flex items-center gap-2 md:flex-col md:items-start lg:gap-4">
              <span className="bg-accent flex size-8 items-center justify-center rounded-full lg:size-10">
                <Lightbulb className="text-primary size-4" />
              </span>
              <p className="text-primary text-lg font-semibold md:text-2xl lg:text-xl">
                Interactive Materials
              </p>
            </div>
            <p className="text-muted-foreground font-normal md:block">
              Generate quizzes, games, and other interactive materials with AI.
            </p>
          </TabsTrigger>
          <TabsTrigger
            value="panel-2"
            className="text-primary hover:border-primary/40 data-[state=active]:border-primary flex w-full flex-col items-start justify-start gap-1 whitespace-normal rounded-md border p-4 text-left"
          >
            <div className="flex items-center gap-2 md:flex-col md:items-start lg:gap-4">
              <span className="bg-accent flex size-8 items-center justify-center rounded-full lg:size-10">
                <ListChecks className="text-primary size-4" />
              </span>
              <p className="text-primary text-lg font-semibold md:text-2xl lg:text-xl">
                Course Management
              </p>
            </div>
            <p className="text-muted-foreground font-normal md:block">
              Build and manage student curriculum and teacher onboarding.
            </p>
          </TabsTrigger>
        </TabsList>

        {featuresData.map((panel) => (
          <TabsContent key={panel.value} value={panel.value} className="mt-20">
            <PanelContent
              title={panel.title}
              description={panel.description}
              features={panel.features}
              imageSrc={panel.imageSrc}
            />
          </TabsContent>
        ))}
      </Tabs>
    </section>
  );
}

function PanelContent({
  title,
  description,
  features,
  imageSrc,
}: {
  title: string;
  description: string;
  features: {
    title: string;
    description: string;
    imgSrc: string;
  }[];
  imageSrc: StaticImageData;
}) {
  return (
    <div className="gap-6 space-y-12 md:flex md:space-y-0">
      <div className="relative md:w-1/2">
        <div className="flex flex-col justify-center">
          <h3 className="text-start text-xl font-bold md:text-2xl">{title}</h3>
          <p className="text-muted-foreground mt-4 text-start">{description}</p>
          <div className="mt-12 space-y-6">
            {features.map(
              (feature: { title: string; description: string; imgSrc: string }, index: number) => (
                <Feature
                  key={index}
                  title={feature.title}
                  description={feature.description}
                  imgSrc={feature.imgSrc}
                />
              ),
            )}
          </div>
        </div>
      </div>
      <div className="-m-4 overflow-hidden p-4 sm:-mx-12 sm:px-12 md:mx-0 md:w-1/2 md:overflow-visible md:px-0">
        <div className="bg-muted before:border-border after:border-border relative before:absolute before:inset-0 before:scale-x-110 before:border-y after:absolute after:inset-0 after:scale-y-110 after:border-x">
          <div className="relative overflow-clip p-10">
            <Image
              src={imageSrc}
              className="mx-auto h-80 w-full rounded-3xl border border-transparent bg-white object-contain object-top shadow-2xl sm:h-[28rem]"
              alt="Feature preview"
              width={500}
              height={500}
              unoptimized
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Individual feature item
function Feature({
  title,
  description,
  imgSrc,
}: {
  title: string;
  description: string;
  imgSrc: string;
}) {
  return (
    <div className="flex items-center gap-6">
      <div className="border-border bg-card flex h-20 w-20 rounded-3xl border p-4">
        <Image
          className="m-auto h-8 w-auto"
          alt={`${title} icon`}
          src={imgSrc}
          height={500}
          width={500}
          unoptimized
        />
      </div>
      <div className="flex w-[calc(100%-7.5rem)] flex-col items-start">
        <h4 className="text-start text-lg font-semibold">{title}</h4>
        <p className="text-muted-foreground mt-1 text-start">{description}</p>
      </div>
    </div>
  );
}
