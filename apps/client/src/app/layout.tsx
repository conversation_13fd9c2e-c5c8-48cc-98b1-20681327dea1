import { <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';

import { AppContextProvider } from '@/providers/app-context';
import { ReactQueryProvider } from '@/providers/react-query';
import { SessionProvider } from '@/providers/session';
import { ThemeProvider } from '@/providers/theme';
import { getAppContext } from '@/providers/app-context/action';
import { auth } from '@/auth';
import { cn } from '@ps/ui/lib/utils';
import NextTopLoader from 'nextjs-toploader';

import '@ps/ui/globals.css';

const fontSans = Geist({
  subsets: ['latin'],
  variable: '--font-sans',
});

const fontMono = Geist_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const appContext = await getAppContext();
  const session = await auth();

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(fontSans.variable, fontMono.variable, 'font-sans antialiased')}
        suppressHydrationWarning
      >
        <NextTopLoader color="#F2470D" />

        <AppContextProvider value={appContext}>
          <SessionProvider session={session} refetchOnWindowFocus={false} basePath="/api/auth">
            <ReactQueryProvider>
              <ThemeProvider>{children}</ThemeProvider>
            </ReactQueryProvider>
          </SessionProvider>
        </AppContextProvider>
      </body>
    </html>
  );
}
