'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';

interface ConversationEntry {
  id: string;
  type: 'user' | 'assistant';
  text: string;
  timestamp: Date;
  audioUrl?: string;
}

interface VoiceAgentHook {
  isConnected: boolean;
  isRecording: boolean;
  isProcessing: boolean;
  processingStatus: string;
  conversationHistory: ConversationEntry[];
  error: string | null;
  startRecording: () => void;
  stopRecording: () => void;
  clearConversation: () => void;
  connect: () => void;
  disconnect: () => void;
  playAudio: (audioUrl: string) => void;
  isAudioEnabled: boolean;
  toggleAudio: () => void;
}

export function useVoiceAgent(): VoiceAgentHook {
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [conversationHistory, setConversationHistory] = useState<ConversationEntry[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);

  const socketRef = useRef<Socket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  // Initialize WebSocket connection
  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'https://localhost:3333';

    socketRef.current = io(`${serverUrl}/voice-agent`, {
      transports: ['websocket'],
      forceNew: true,
    });

    socketRef.current.on('connect', () => {
      setIsConnected(true);
      setError(null);
      toast.success('Connected to voice agent');
    });

    socketRef.current.on('disconnect', () => {
      setIsConnected(false);
      toast.info('Disconnected from voice agent');
    });

    socketRef.current.on('connected', (data) => {
      console.log('Session established:', data.sessionId);
    });

    socketRef.current.on('transcription', (data) => {
      const userEntry: ConversationEntry = {
        id: Date.now().toString(),
        type: 'user',
        text: data.text,
        timestamp: new Date(),
      };

      setConversationHistory((prev) => [...prev, userEntry]);
    });

    socketRef.current.on('ai_response', (data) => {
      const assistantEntry: ConversationEntry = {
        id: Date.now().toString(),
        type: 'assistant',
        text: data.text,
        timestamp: new Date(),
      };

      setConversationHistory((prev) => [...prev, assistantEntry]);
    });

    socketRef.current.on('audio_response', (data) => {
      const audioUrl = `data:${data.mimeType};base64,${data.audioData}`;

      // Update the last assistant entry with audio
      setConversationHistory((prev) => {
        const updated = [...prev];
        const lastAssistantIndex = updated.length - 1;
        if (updated[lastAssistantIndex] && updated[lastAssistantIndex].type === 'assistant') {
          updated[lastAssistantIndex] = {
            ...updated[lastAssistantIndex],
            audioUrl,
          };
        }
        return updated;
      });

      // Auto-play if enabled
      if (isAudioEnabled) {
        playAudio(audioUrl);
      }
    });

    socketRef.current.on('processing_status', (data) => {
      setProcessingStatus(data.status);
      if (data.status === 'completed') {
        setIsProcessing(false);
        setProcessingStatus('');
      }
    });

    socketRef.current.on('conversation_cleared', () => {
      setConversationHistory([]);
      toast.success('Conversation cleared');
    });

    socketRef.current.on('error', (data) => {
      setError(data.message);
      setIsProcessing(false);
      toast.error(data.message);
    });

    socketRef.current.on('connect_error', () => {
      setError('Failed to connect to voice agent');
      toast.error('Connection failed');
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAudioEnabled]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setIsConnected(false);
  }, []);

  // Start audio recording
  const startRecording = useCallback(async () => {
    if (!isConnected) {
      toast.error('Please connect first');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      streamRef.current = stream;
      audioChunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

        // Convert to base64 and send via WebSocket
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64Audio = reader.result as string;
          const audioData = base64Audio.split(',')[1]; // Remove data:audio/webm;base64, prefix

          if (socketRef.current && audioData) {
            setIsProcessing(true);
            socketRef.current.emit('voice_message', {
              audioData,
              mimeType: 'audio/webm',
            });
          }
        };
        reader.readAsDataURL(audioBlob);

        // Cleanup
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      mediaRecorder.start();
      setIsRecording(true);
      setError(null);
    } catch (error) {
      console.error('Error starting recording:', error);
      setError('Failed to start recording. Please check your microphone permissions.');
      toast.error('Failed to start recording');
    }
  }, [isConnected]);

  // Stop audio recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  }, []);

  // Clear conversation
  const clearConversation = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('clear_conversation');
    } else {
      setConversationHistory([]);
    }
  }, []);

  // Play audio
  const playAudio = useCallback(
    (audioUrl: string) => {
      if (!isAudioEnabled) return;

      const audio = new Audio(audioUrl);
      audio.play().catch((error) => {
        console.error('Error playing audio:', error);
      });
    },
    [isAudioEnabled],
  );

  // Toggle audio playback
  const toggleAudio = useCallback(() => {
    setIsAudioEnabled((prev) => !prev);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  return {
    isConnected,
    isRecording,
    isProcessing,
    processingStatus,
    conversationHistory,
    error,
    startRecording,
    stopRecording,
    clearConversation,
    connect,
    disconnect,
    playAudio,
    isAudioEnabled,
    toggleAudio,
  };
}
