'use client';

import { Avatar, AvatarFallback } from '@ps/ui/components/avatar';
import { Button } from '@ps/ui/components/button';
import { formatDistanceToNow } from 'date-fns';
import { Bot, Play, User } from 'lucide-react';

interface ConversationEntry {
  id: string;
  type: 'user' | 'assistant';
  text: string;
  timestamp: Date;
  audioUrl?: string;
}

interface ConversationHistoryProps {
  history: ConversationEntry[];
  onPlayAudio: (audioUrl: string) => void;
  isAudioEnabled: boolean;
}

export function ConversationHistory({
  history,
  onPlayAudio,
  isAudioEnabled,
}: ConversationHistoryProps) {
  if (history.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="bg-muted mb-4 rounded-full p-4">
          <Bot className="text-muted-foreground h-8 w-8" />
        </div>
        <p className="text-muted-foreground text-sm">
          No conversation yet. Start by recording your first message!
        </p>
      </div>
    );
  }

  return (
    <div className="max-h-96 space-y-4 overflow-y-auto">
      {history.map((entry) => (
        <div
          key={entry.id}
          className={`flex items-start space-x-3 ${
            entry.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
          }`}
        >
          <Avatar className="h-8 w-8">
            <AvatarFallback>
              {entry.type === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
            </AvatarFallback>
          </Avatar>

          <div className={`flex-1 space-y-2 ${entry.type === 'user' ? 'text-right' : 'text-left'}`}>
            <div
              className={`inline-block max-w-[80%] rounded-lg px-4 py-2 ${
                entry.type === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}
            >
              <p className="text-sm">{entry.text}</p>
            </div>

            <div
              className={`text-muted-foreground flex items-center space-x-2 text-xs ${
                entry.type === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <span>{formatDistanceToNow(entry.timestamp, { addSuffix: true })}</span>

              {entry.audioUrl && isAudioEnabled && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2"
                  onClick={() => onPlayAudio(entry.audioUrl!)}
                >
                  <Play className="mr-1 h-3 w-3" />
                  Play
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
