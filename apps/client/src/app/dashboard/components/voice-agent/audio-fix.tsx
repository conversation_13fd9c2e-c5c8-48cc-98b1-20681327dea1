'use client';

import { useCallback, useRef } from 'react';

export class AudioStreamer {
  private audioContext: AudioContext | null = null;
  private nextScheduledTime: number = 0;

  async initialize() {
    if (!this.audioContext || this.audioContext.state === 'closed') {
      this.audioContext = new AudioContext({ sampleRate: 24000 });
      console.log('🎵 AudioContext created, state:', this.audioContext.state);
    }

    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
      console.log('🎵 AudioContext resumed, state:', this.audioContext.state);
    }

    return this.audioContext;
  }

  async playPCMChunk(pcmSamples: Int16Array, sampleRate: number = 24000): Promise<void> {
    const audioContext = await this.initialize();
    if (!audioContext) return;

    try {
      // Convert to Float32Array
      const floatData = new Float32Array(pcmSamples.length);
      for (let i = 0; i < pcmSamples.length; i++) {
        floatData[i] = (pcmSamples?.[i] ?? 0) / 32768.0;
      }

      // Create audio buffer
      const audioBuffer = audioContext.createBuffer(1, floatData.length, sampleRate);
      audioBuffer.copyToChannel(floatData, 0);

      // Calculate when to start this chunk
      const now = audioContext.currentTime;
      const startTime = Math.max(now, this.nextScheduledTime);
      const duration = audioBuffer.length / audioBuffer.sampleRate;

      // Create and schedule source
      const source = audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContext.destination);

      source.start(startTime);
      console.log(
        `🎵 Scheduled audio: start=${startTime.toFixed(3)}s, duration=${duration.toFixed(3)}s`,
      );

      // Update next scheduled time for seamless continuation
      this.nextScheduledTime = startTime + duration;
    } catch (error) {
      console.error('❌ Error playing PCM chunk:', error);
    }
  }

  // Process base64 PCM data from server
  async playBase64PCM(base64Data: string, sampleRate: number = 24000): Promise<void> {
    try {
      // Decode base64 PCM data
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Convert to 16-bit PCM samples
      const samples = new Int16Array(bytes.buffer);
      console.log('🎵 Decoded', samples.length, 'PCM samples');

      await this.playPCMChunk(samples, sampleRate);
    } catch (error) {
      console.error('❌ Error processing base64 PCM:', error);
    }
  }

  reset() {
    this.nextScheduledTime = 0;
    console.log('🎵 Audio streamer reset');
  }

  close() {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
    this.audioContext = null;
    this.nextScheduledTime = 0;
  }
}

// Hook to use the audio streamer
export function useAudioStreamer() {
  const streamerRef = useRef<AudioStreamer | null>(null);

  const getStreamer = useCallback(() => {
    if (!streamerRef.current) {
      streamerRef.current = new AudioStreamer();
    }
    return streamerRef.current;
  }, []);

  const playAudio = useCallback(
    async (base64Data: string, sampleRate = 24000) => {
      const streamer = getStreamer();
      await streamer.playBase64PCM(base64Data, sampleRate);
    },
    [getStreamer],
  );

  const resetAudio = useCallback(() => {
    const streamer = getStreamer();
    streamer.reset();
  }, [getStreamer]);

  const closeAudio = useCallback(() => {
    if (streamerRef.current) {
      streamerRef.current.close();
      streamerRef.current = null;
    }
  }, []);

  return { playAudio, resetAudio, closeAudio };
}
