/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';
import { useAudioStreamer } from './audio-fix';

interface ConversationEntry {
  id: string;
  type: 'user' | 'assistant';
  text: string;
  timestamp: Date;
  audioUrl?: string;
  isLive?: boolean;
}

interface LiveVoiceAgentHook {
  isConnected: boolean;
  isLiveActive: boolean;
  isStreaming: boolean;
  conversationHistory: ConversationEntry[];
  error: string | null;
  liveSessionStatus: any;
  startLiveSession: (options?: { model?: string; systemInstruction?: string }) => void;
  stopLiveSession: () => void;
  connect: () => void;
  disconnect: () => void;
  clearConversation: () => void;
  playAudio: (audioUrl: string) => void;
  isAudioEnabled: boolean;
  toggleAudio: () => void;
  silenceThreshold: number;
  setSilenceThreshold: (threshold: number) => void;
}

export function useVoiceAgentLive(): LiveVoiceAgentHook {
  const [isConnected, setIsConnected] = useState(false);
  const [isLiveActive, setIsLiveActive] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [conversationHistory, setConversationHistory] = useState<ConversationEntry[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [liveSessionStatus, setLiveSessionStatus] = useState<any>({});
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [silenceThreshold, setSilenceThreshold] = useState(-30);

  const socketRef = useRef<Socket | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const isLiveActiveRef = useRef<boolean>(false);

  // Replace the complex audio system with the simple streamer
  const { playAudio, resetAudio, closeAudio } = useAudioStreamer();

  // Update the ref whenever isLiveActive changes
  useEffect(() => {
    isLiveActiveRef.current = isLiveActive;
  }, [isLiveActive]);

  const startRealTimeAudioStreaming = useCallback(async () => {
    if (isStreaming) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1,
        },
      });

      streamRef.current = stream;
      setIsStreaming(true);

      // Use Web Audio API to capture PCM data directly
      const audioContext = new AudioContext({ sampleRate: 16000 });
      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      console.log('🎙️ AudioContext created with sample rate:', audioContext.sampleRate);
      console.log('🎙️ Stream active:', stream.active);
      console.log('🎙️ Stream tracks:', stream.getTracks().length);

      processor.onaudioprocess = (event) => {
        // Only process and send audio if live session is active (using ref for current value)
        if (!isLiveActiveRef.current || !socketRef.current) {
          return;
        }

        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);

        // Convert Float32Array to Int16Array (PCM 16-bit)
        const pcmData = new Int16Array(inputData.length);
        for (let i = 0; i < inputData.length; i++) {
          const sample = Math.max(-1, Math.min(1, inputData[i] || 0));
          pcmData[i] = Math.round(sample < 0 ? sample * 0x8000 : sample * 0x7fff);
        }

        // Convert to base64
        const buffer = new ArrayBuffer(pcmData.length * 2);
        const view = new DataView(buffer);
        for (let i = 0; i < pcmData.length; i++) {
          view.setInt16(i * 2, pcmData[i] || 0, true); // little-endian
        }

        const base64 = btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(buffer))));

        if (base64) {
          console.log(
            '📤 Emitting PCM audio chunk:',
            base64.length,
            'chars',
            'Live session active:',
            isLiveActiveRef.current,
          );
          socketRef.current.emit('live_audio_chunk', {
            audioData: base64,
            mimeType: 'audio/pcm;rate=16000',
          });
        }
      };

      source.connect(processor);
      processor.connect(audioContext.destination);

      // Store references for cleanup
      (streamRef.current as any).audioContext = audioContext;
      (streamRef.current as any).processor = processor;
      toast.success('🎙️ Live streaming started');
    } catch (error) {
      console.error('Error starting live streaming:', error);
      setError('Failed to start live streaming. Please check microphone permissions.');
      toast.error('Failed to start live streaming');
      setIsStreaming(false);
    }
  }, [isStreaming]);

  const stopRealTimeAudioStreaming = useCallback(() => {
    if (streamRef.current) {
      // Clean up Web Audio API components for microphone
      const audioContext = (streamRef.current as any).audioContext;
      const processor = (streamRef.current as any).processor;

      if (processor) {
        processor.disconnect();
      }

      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }

      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }

    // Clear audio playback queue but keep playback AudioContext alive
    // It will be reused for subsequent responses
    setIsStreaming(false);
  }, []);

  const startLiveSession = useCallback(
    (options?: { model?: string; systemInstruction?: string }) => {
      console.log('🚀 Starting Live session...', { isConnected, isLiveActive });

      if (!isConnected || isLiveActive) {
        console.log('❌ Cannot start Live session - not connected or already active');
        toast.error('Cannot start Live session');
        return;
      }

      console.log('📤 Emitting start_live_session event');
      socketRef.current?.emit('start_live_session', {
        model: options?.model || 'gemini-2.5-flash-preview-native-audio-dialog',
        systemInstruction:
          options?.systemInstruction ||
          'You are a helpful AI voice assistant. Respond naturally and conversationally. Keep responses concise but friendly.',
      });
    },
    [isConnected, isLiveActive],
  );

  const stopLiveSession = useCallback(() => {
    if (!isLiveActive) return;

    stopRealTimeAudioStreaming();
    socketRef.current?.emit('stop_live_session');

    setIsLiveActive(false);
    setIsStreaming(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stopRealTimeAudioStreaming]);

  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    console.log('🔄 Attempting to connect to server...');
    const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'https://localhost:3333';
    console.log('🌐 Connecting to:', serverUrl);

    const socket = io(`${serverUrl}/voice-agent`, {
      transports: ['websocket'],
      rejectUnauthorized: false, // For self-signed certificates
    });

    socketRef.current = socket;

    socket.on('connect', async () => {
      console.log('✅ Connected to server:', socket.id);
      setIsConnected(true);
      setError(null);
    });

    // Audio chunk sent confirmation
    socket.on('audio_chunk_sent', (data) => {
      console.log('✅ Audio chunk confirmed by backend:', data.size, 'bytes');
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Connection error:', error);
      setError(`Connection failed: ${error.message}`);
      toast.error('Connection failed');
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Disconnected:', reason);
      setIsConnected(false);
    });

    socket.on('live_session_started', () => {
      console.log('📡 Live session started event received');
      setIsLiveActive(true);

      // // Start audio streaming after a short delay to ensure state is updated
      // setTimeout(async () => {
      //   try {
      //     await startRealTimeAudioStreaming();
      //     toast.success('🎙️ Live session active - speak naturally!');
      //   } catch (error) {
      //     console.error('❌ Failed to start audio streaming:', error);
      //     toast.error('Failed to start microphone - please check permissions');
      //     setIsLiveActive(false); // Reset state on error
      //   }
      // }, 100);
    });

    socket.on('live_session_ready', async () => {
      console.log('📡 Live session ready event received (fallback)');
      setIsLiveActive(true);
      try {
        await startRealTimeAudioStreaming();
        toast.success('🎙️ Live session active - speak naturally!');
      } catch (error) {
        console.error('❌ Failed to start audio streaming:', error);
        toast.error('Failed to start microphone - please check permissions');
        setIsLiveActive(false); // Reset state on error
      }
    });

    // Enhanced audio response handler with seamless streaming
    socket.on('live_audio_response', async (data) => {
      console.log(
        '🎵 Received live audio response:',
        data.mimeType,
        data.audioData?.length || 0,
        'chars',
        'Audio enabled:',
        isAudioEnabled,
      );

      if (isAudioEnabled && data.audioData) {
        // Gemini Live API returns PCM audio at 24kHz
        const mimeType = data.mimeType || 'audio/pcm;rate=24000';
        console.log('🔊 Processing audio chunk with MIME type:', mimeType);

        if (mimeType.includes('pcm')) {
          try {
            // Use the new audio streamer for seamless playback
            await playAudio(data.audioData, 24000);
          } catch (error) {
            console.error('❌ Error processing PCM audio chunk:', error);
          }
        } else {
          console.log('⚠️ Non-PCM audio format, skipping');
        }
      } else {
        console.log('⚠️ Audio skipped - enabled:', isAudioEnabled, 'hasData:', !!data.audioData);
      }
    });

    socket.on('live_text_response', (data) => {
      const assistantEntry: ConversationEntry = {
        id: `live-${Date.now()}`,
        type: 'assistant',
        text: data.text,
        timestamp: new Date(),
        isLive: true,
      };
      setConversationHistory((prev) => [...prev, assistantEntry]);
    });

    socket.on('live_turn_complete', () => {
      console.log('🔄 Live turn completed');
      resetAudio(); // Reset timing for next audio stream
      setLiveSessionStatus((prev: any) => ({ ...prev, isSpeaking: false }));
      setLiveSessionStatus((prev: any) => ({ ...prev, turnComplete: true }));
    });

    socket.on('live_session_stopped', () => {
      console.log('📡 Live session stopped event received');
      setIsLiveActive(false);
      setIsStreaming(false);
      stopRealTimeAudioStreaming();
      toast.info('Live session ended');
    });

    socket.on('live_session_status_response', (status) => {
      setLiveSessionStatus(status);
    });

    socket.on('error', (data) => {
      setError(data.message);
      console.error('❌ Socket error:', data.message);

      // If it's a live session error, stop the audio streaming
      if (data.message.includes('live session') || data.message.includes('Live session')) {
        console.log('🛑 Stopping audio streaming due to live session error');
        setIsLiveActive(false);
        stopRealTimeAudioStreaming();
      }

      toast.error(data.message);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAudioEnabled, startRealTimeAudioStreaming, playAudio, resetAudio]);

  const disconnect = useCallback(() => {
    if (isLiveActive) {
      stopLiveSession();
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    setIsConnected(false);
    setIsLiveActive(false);
    setIsStreaming(false);
  }, [isLiveActive, stopLiveSession]);

  const clearConversation = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('clear_conversation');
    } else {
      setConversationHistory([]);
    }
  }, []);

  const toggleAudio = useCallback(() => {
    setIsAudioEnabled((prev) => !prev);
  }, []);

  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Remove dependencies to prevent reconnection loop

  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  // Cleanup function
  useEffect(() => {
    return () => {
      closeAudio();
    };
  }, [closeAudio]);

  return {
    isConnected,
    isLiveActive,
    isStreaming,
    conversationHistory,
    error,
    liveSessionStatus,
    startLiveSession,
    stopLiveSession,
    connect,
    disconnect,
    clearConversation,
    playAudio: startRealTimeAudioStreaming,
    isAudioEnabled,
    toggleAudio,
    silenceThreshold,
    setSilenceThreshold,
  };
}
