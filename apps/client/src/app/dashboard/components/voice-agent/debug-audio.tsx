'use client';

import { useState } from 'react';

export function AudioDebugger() {
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs((prev) => [...prev.slice(-10), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testBasicAudio = async () => {
    try {
      addLog('🔔 Testing basic audio...');

      // Create AudioContext
      const audioContext = new AudioContext({ sampleRate: 24000 });
      addLog(`AudioContext created, state: ${audioContext.state}`);

      // Resume if suspended
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
        addLog(`AudioContext resumed, new state: ${audioContext.state}`);
      }

      // Create a 440Hz beep for 0.5 seconds
      const sampleRate = 24000;
      const duration = 0.5;
      const frequency = 440;
      const samples = Math.floor(sampleRate * duration);

      const audioBuffer = audioContext.createBuffer(1, samples, sampleRate);
      const channelData = audioBuffer.getChannelData(0);

      for (let i = 0; i < samples; i++) {
        channelData[i] = Math.sin((2 * Math.PI * frequency * i) / sampleRate) * 0.3;
      }

      const source = audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContext.destination);

      source.onended = () => {
        addLog('✅ Beep finished playing');
      };

      source.start();
      addLog('🎵 Beep should be playing now...');
    } catch (error) {
      addLog(`❌ Error: ${error}`);
    }
  };

  const testPCMDecoding = () => {
    try {
      addLog('🔧 Testing PCM decoding...');

      // Test with sample base64 PCM data (small chunk)
      const testBase64 = 'AAAAAAAAAAAAAAAA'; // Some zeros

      // Decode base64 PCM data
      const binaryString = atob(testBase64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Convert to 16-bit PCM samples
      const samples = new Int16Array(bytes.buffer);
      addLog(`✅ Decoded ${samples.length} PCM samples`);
    } catch (error) {
      addLog(`❌ PCM decoding error: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="rounded-lg border bg-gray-50 p-4">
      <h3 className="mb-2 font-bold">Audio Debug Console</h3>

      <div className="mb-4 space-x-2">
        <button
          onClick={testBasicAudio}
          className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        >
          Test Basic Audio
        </button>
        <button
          onClick={testPCMDecoding}
          className="rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
        >
          Test PCM Decoding
        </button>
        <button
          onClick={clearLogs}
          className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
        >
          Clear Logs
        </button>
      </div>

      <div className="h-60 overflow-y-auto rounded bg-black p-3 font-mono text-sm text-green-400">
        {logs.length === 0 ? (
          <div className="text-gray-500">No logs yet. Click a test button above.</div>
        ) : (
          logs.map((log, index) => <div key={index}>{log}</div>)
        )}
      </div>

      <div className="mt-2 text-sm text-gray-600">
        <p>
          <strong>Instructions:</strong>
        </p>
        <ol>
          <li>1. Click &ldquo;Test Basic Audio&rdquo; - you should hear a beep</li>
          <li>2. If no beep, check browser audio permissions</li>
          <li>3. Click &ldquo;Test PCM Decoding&rdquo; to verify data processing</li>
          <li>4. Check browser console for additional logs</li>
        </ol>
      </div>
    </div>
  );
}
