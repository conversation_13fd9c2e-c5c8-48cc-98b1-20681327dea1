'use client';

import { Badge } from '@ps/ui/components/badge';
import { Card, CardContent } from '@ps/ui/components/card';
import { Loader2, MessageSquare, Mic, Volume2 } from 'lucide-react';

interface ProcessingStatusProps {
  status: string;
}

export function ProcessingStatus({ status }: ProcessingStatusProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'transcribing':
        return {
          icon: <Mic className="h-4 w-4" />,
          label: 'Transcribing audio...',
          description: 'Converting your voice to text',
          color: 'bg-blue-500' as const,
        };
      case 'generating_response':
        return {
          icon: <MessageSquare className="h-4 w-4" />,
          label: 'Generating response...',
          description: 'AI is thinking about your message',
          color: 'bg-orange-500' as const,
        };
      case 'generating_audio':
        return {
          icon: <Volume2 className="h-4 w-4" />,
          label: 'Generating audio...',
          description: 'Converting response to speech',
          color: 'bg-green-500' as const,
        };
      default:
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          label: 'Processing...',
          description: 'Please wait',
          color: 'bg-gray-500' as const,
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center space-x-4">
          <div className={`rounded-full p-2 ${config.color} text-white`}>{config.icon}</div>

          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{config.label}</span>
              <Badge variant="secondary" className="animate-pulse">
                Processing
              </Badge>
            </div>
            <p className="text-muted-foreground mt-1 text-sm">{config.description}</p>
          </div>

          <div className="flex space-x-1">
            <div className="bg-primary h-2 w-2 animate-bounce rounded-full"></div>
            <div
              className="bg-primary h-2 w-2 animate-bounce rounded-full"
              style={{ animationDelay: '0.1s' }}
            ></div>
            <div
              className="bg-primary h-2 w-2 animate-bounce rounded-full"
              style={{ animationDelay: '0.2s' }}
            ></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
