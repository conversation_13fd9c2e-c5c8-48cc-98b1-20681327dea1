import { FormControl, FormField, FormItem, FormMessage } from '@ps/ui/components/form';
import { Textarea } from '@ps/ui/components/textarea';
import FileUploadListComponent, {
  FileState,
  FileUpload,
  FileUploadProvider,
} from '@ps/ui/custom/file-upload';
import { cn } from '@ps/ui/lib/utils';
import { CircleStop, Globe, LinkIcon, Mic, Paperclip, Sparkles, X } from 'lucide-react';
import { Button } from '@ps/ui/components/button';
import { Popover, PopoverContent, PopoverTrigger } from '@ps/ui/components/popover';
import { Input } from '@ps/ui/components/input';
import { useState } from 'react';
import { toast } from 'sonner';
import { isValidUrl, normalizeUrl } from '@ps/common/utils/url';
import { UseFormReturn } from 'react-hook-form';

export interface ChatFieldConfig {
  enableFileUpload?: boolean;
  enableUrlInput?: boolean;
  enableWebSearch?: boolean;
  enableVoiceInput?: boolean;
  textareaHeight?: string;
  placeholder?: string;
  submitButtonText?: string;
  maxPromptLength?: number;
  hideSubmitButton?: boolean;
  submitButtonType?: 'button' | 'submit';
}

const defaultConfig: Required<ChatFieldConfig> = {
  enableFileUpload: true,
  enableUrlInput: true,
  enableWebSearch: true,
  enableVoiceInput: true,
  textareaHeight: 'h-[140px]',
  placeholder: 'Ask Anything..',
  submitButtonText: 'Ask AI',
  maxPromptLength: 1000,
  hideSubmitButton: false,
  submitButtonType: 'submit',
};

type Props = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturn<any>;
  isLoading: boolean;
  config?: ChatFieldConfig;
  onSubmit?: () => void;
};

const ChatArea = ({ isLoading, form, config = {}, onSubmit }: Props) => {
  const finalConfig = { ...defaultConfig, ...config };
  const [files, setFiles] = useState<FileState[]>([]);
  const [isLinkPopoverOpen, setIsLinkPopoverOpen] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [addedUrls, setAddedUrls] = useState<string[]>([]);

  const handleAddLink = () => {
    if (currentUrl) {
      if (!isValidUrl(normalizeUrl(currentUrl))) {
        toast.error('Invalid URL');
        return;
      }
      const newUrls = [...addedUrls, currentUrl];
      setAddedUrls(newUrls);
      form.setValue('urls', newUrls);
      setCurrentUrl('');
      setIsLinkPopoverOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddLink();
    }
  };

  const handleRemoveLink = (urlToRemove: string) => {
    const newUrls = addedUrls.filter((url) => url !== urlToRemove);
    setAddedUrls(newUrls);
    form.setValue('urls', newUrls);
  };

  // Conditionally render components based on config
  const renderFileUpload = () => {
    if (!finalConfig.enableFileUpload) return null;

    return (
      <FileUpload
        selectZone={
          <Button type="button" size="icon" variant="outline" className="size-8 rounded-full">
            <Paperclip />
          </Button>
        }
        onUpload={(urls) => form.setValue('fileUrls', urls)}
        onFilesChange={(files) => setFiles(files)}
      />
    );
  };

  const renderUrlInput = () => {
    if (!finalConfig.enableUrlInput) return null;

    return (
      <Popover open={isLinkPopoverOpen} onOpenChange={setIsLinkPopoverOpen}>
        <PopoverTrigger asChild>
          <Button type="button" size="icon" variant="outline" className="size-8 rounded-full">
            <LinkIcon />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <div className="flex space-x-2">
            <Input
              type="url"
              placeholder="Enter URL"
              value={currentUrl}
              onChange={(e) => setCurrentUrl(e.target.value)}
              onKeyDown={handleKeyDown}
            />
            <Button type="button" variant="outline" onClick={handleAddLink}>
              Add
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  const renderWebSearch = () => {
    if (!finalConfig.enableWebSearch) return null;

    return (
      <FormField
        control={form.control}
        name="webSearch"
        render={({ field }) => (
          <FormItem className="inline">
            <FormControl>
              <Button
                variant={!field.value ? 'outline' : 'default'}
                size="sm"
                className="rounded-full"
                onClick={() => field.onChange(!field.value)}
                type="button"
              >
                <Globe />
                Web Search
              </Button>
            </FormControl>
          </FormItem>
        )}
      />
    );
  };

  const renderVoiceInput = () => {
    if (!finalConfig.enableVoiceInput) return null;

    return (
      <div className="absolute left-4 top-[17px]">
        <Mic className="size-5" />
      </div>
    );
  };

  const renderUrlPreview = () => {
    if (!finalConfig.enableUrlInput || addedUrls.length === 0) return null;

    return (
      <div className="space-y-2">
        {addedUrls.map((url, index) => (
          <div
            key={index}
            className="flex items-center justify-between gap-3 rounded-md border px-4 py-2"
          >
            <div className="flex items-center gap-3">
              <LinkIcon className="size-4" />
              <p className="flex-1 truncate text-sm font-medium">{url}</p>
            </div>
            <X
              className="text-muted-foreground size-4 cursor-pointer"
              onClick={() => handleRemoveLink(url)}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderFileList = () => {
    if (!finalConfig.enableFileUpload || files.length === 0) return null;

    return <FileUploadListComponent files={files} />;
  };

  const renderSubmitButton = () => {
    if (finalConfig.hideSubmitButton) return null;

    return (
      <Button
        size="sm"
        type={finalConfig.submitButtonType}
        className="rounded-full"
        onClick={() => onSubmit && onSubmit()}
      >
        {isLoading ? (
          <>
            <CircleStop /> Stop
          </>
        ) : (
          <>
            <Sparkles /> {finalConfig.submitButtonText}
          </>
        )}
      </Button>
    );
  };

  const needsFileUploadProvider = finalConfig.enableFileUpload;

  const chatFieldContent = (
    <div className="rounded-t-lg">
      {isLoading && (
        <p className="text-muted-foreground flex animate-pulse items-center gap-1 p-1.5 text-sm">
          Thinking.. <Sparkles className="size-4 font-medium" />
        </p>
      )}
      <div className="bg-background relative rounded-lg">
        <FormField
          control={form.control}
          name="prompt"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder={finalConfig.placeholder}
                  className={cn(
                    finalConfig.textareaHeight,
                    'resize-none p-4',
                    finalConfig.enableVoiceInput ? 'pl-12' : 'pl-4',
                  )}
                  maxLength={finalConfig.maxPromptLength}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {renderVoiceInput()}

        <div
          className={cn('absolute bottom-4 left-0 flex w-full items-center justify-between px-4', {
            'bottom-10': form.formState.errors.prompt,
          })}
        >
          <div className="space-x-2">
            {renderUrlInput()}
            {renderFileUpload()}
            {renderWebSearch()}
          </div>

          {renderSubmitButton()}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-2">
      {needsFileUploadProvider ? (
        <FileUploadProvider type="document">
          {chatFieldContent}
          {renderFileList()}
        </FileUploadProvider>
      ) : (
        chatFieldContent
      )}
      {renderUrlPreview()}
    </div>
  );
};

export default ChatArea;
