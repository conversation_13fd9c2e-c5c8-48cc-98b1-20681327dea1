'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';

import {
  BreadcrumbSeparator,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  Breadcrumb,
} from '@ps/ui/components/breadcrumb';
import { Fragment } from 'react';
import { formatToTitleCase } from '@ps/common/utils/string';

interface BreadcrumbItem {
  label: string;
  link: string;
}

const generateBreadcrumbItems = (pathname: string): BreadcrumbItem[] => {
  const segments = decodeURI(pathname).split('/').filter(Boolean);

  return segments.map((segment, index) => ({
    label: formatToTitleCase(segment.replace(/-/g, ' ')),
    link: `/${segments.slice(0, index + 1).join('/')}`,
  }));
};

export function DashboardBreadcrumb() {
  const pathname = usePathname();
  const items = generateBreadcrumbItems(pathname);

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {items.map((item, index) => (
          <Fragment key={item.label}>
            {index !== 0 && <BreadcrumbSeparator className="hidden md:block" />}

            {index !== items.length - 1 ? (
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink asChild>
                  <Link href={item.link}>{item.label}</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
