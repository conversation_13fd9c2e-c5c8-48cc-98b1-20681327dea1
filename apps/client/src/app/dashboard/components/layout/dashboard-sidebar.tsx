'use client';

import type { ComponentProps } from 'react';

import {
  GalleryVerticalEnd,
  LayoutDashboard,
  Lightbulb,
  // Calendar,
  Dumbbell,
  // ListTodo,
  BookOpen,
  Settings,
  MessageSquare,
} from 'lucide-react';
import { GrCertificate } from 'react-icons/gr';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@ps/ui/components/sidebar';

import useAppContext from '@/providers/app-context';
import { NavMain } from './nav-main';
import { NavProjects } from './nav-projects';
import { NavUser } from './nav-user';
import { TeamSwitcher } from './team-switcher';

// This is sample data.
const data = {
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: LayoutDashboard,
    },
    // {
    //   title: 'Todo',
    //   url: '/dashboard/todo',
    //   icon: ListTodo,
    // },
    {
      title: 'Knowledge Base',
      url: '/dashboard/knowledge-base',
      icon: BookOpen,
    },
    {
      title: 'Planning',
      url: '/dashboard/planning',
      icon: Lightbulb,
    },
    {
      title: 'Courses',
      url: '/dashboard/courses',
      icon: GrCertificate,
    },
    {
      title: 'Training',
      url: '/dashboard/training',
      icon: Dumbbell,
    },
    {
      title: 'AI Chat',
      url: '/dashboard/ai-chat',
      icon: MessageSquare,
    },
    // {
    //   title: 'Contents',
    //   url: '/dashboard/contents',
    //   icon: AlignLeft,
    //   items: [
    //     {
    //       title: 'General',
    //       url: '#',
    //     },
    //     {
    //       title: 'Team',
    //       url: '#',
    //     },
    //     {
    //       title: 'Billing',
    //       url: '#',
    //     },
    //     {
    //       title: 'Limits',
    //       url: '#',
    //     },
    //   ],
    // },
    // {
    //   title: 'Calendar',
    //   url: '/dashboard/calendar',
    //   icon: Calendar,
    // },
    // {
    //   title: 'Analytics',
    //   url: '/dashboard/analytics',
    //   icon: ChartLine,
    // },
    // {
    //   title: 'Engagement',
    //   url: '/dashboard/engagement',
    //   icon: MessagesSquare,
    // },
  ],
  projects: [
    // {
    //   name: 'Library',
    //   url: '/dashboard/library',
    //   icon: LibraryBig,
    // },
    {
      name: 'Profile',
      url: '/dashboard/profile',
      icon: Settings,
    },
    // {
    //   name: 'Hosting',
    //   url: '/dashboard/hosting',
    //   icon: Server,
    // },
  ],
};

export function DashboardSidebar({ ...props }: ComponentProps<typeof Sidebar>) {
  const { user } = useAppContext();
  const teams = [
    {
      name: user?.team?.name || 'My Team',
      logo: GalleryVerticalEnd,
      plan: user?.team?.subscriptionPlan || 'Free',
    },
  ];

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
