'use client';

import { <PERSON><PERSON>he<PERSON><PERSON>UpDown, <PERSON>Log<PERSON>ut, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/lu';
import { useTheme } from 'next-themes';
import { signOut } from 'next-auth/react';
import Link from 'next/link';

import {
  DropdownMenuSeparator,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenu,
  useSidebar,
} from '@ps/ui/components/sidebar';
import { AvatarFallback, AvatarImage, Avatar } from '@ps/ui/components/avatar';
import { getInitials } from '@ps/common/utils/string';
import { Switch } from '@ps/ui/components/switch';
import useAppContext from '@/providers/app-context';

export function NavUser() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { isMobile } = useSidebar();

  const currentTheme = theme === 'system' ? resolvedTheme : theme;
  const isDark = currentTheme === 'dark';

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <UserInfo />
              <LuChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <UserInfo />
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* <DropdownMenuGroup>
              <DropdownMenuItem>
                <Sparkles />
                Upgrade to Pro
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator /> */}
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/profile" className="cursor-pointer">
                  <LuUser />
                  Profile Settings
                </Link>
              </DropdownMenuItem>
              {/* <DropdownMenuItem>
                <CreditCard />
                Billing
              </DropdownMenuItem> */}
              {/* <DropdownMenuItem>
                <Bell />
                Notifications
              </DropdownMenuItem> */}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                className="flex items-center justify-between gap-2"
                onClick={(e) => e.preventDefault()}
              >
                <div className="flex items-center gap-2">
                  {isDark ? <LuMoon className="size-4" /> : <LuSun className="size-4" />}
                  <span>Dark mode</span>
                </div>
                <Switch
                  onCheckedChange={() => setTheme(isDark ? 'light' : 'dark')}
                  className="cursor-pointer"
                  checked={isDark}
                />
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => signOut()}>
              <LuLogOut />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

const UserInfo = () => {
  const { user } = useAppContext();

  return (
    <>
      <Avatar className="size-8 rounded-lg">
        <AvatarImage src={user?.profilePicture} alt={user?.name} />
        <AvatarFallback className="rounded-lg">{getInitials(user?.name)}</AvatarFallback>
      </Avatar>
      <div className="grid flex-1 text-left text-sm leading-tight">
        <span className="truncate font-medium">{user?.name}</span>
        <span className="truncate text-xs">{user?.email}</span>
      </div>
    </>
  );
};
