import { Progress } from '@ps/ui/components/progress';
import { cn } from '@ps/ui/lib/utils';
import { Sparkle } from 'lucide-react';
import React from 'react';

interface UploadProgressProps {
  progress: number;
  stage?: string;
  message?: string;
  className?: string;
}

const UploadProgress = ({
  progress,
  stage,
  message = 'This will take a couple of minutes. You can go back to your dashboard.',
  className,
}: UploadProgressProps) => {
  return (
    <div
      className={cn(
        'mx-auto flex h-[66vh] flex-col items-center justify-center space-y-2',
        className,
      )}
    >
      <Sparkle className="size-8 animate-pulse text-cyan-400" />
      <h6 className="font-semibold">
        {stage && `${stage} `}
        {progress}%
      </h6>
      <Progress value={progress} />
      <p className="text-muted-foreground text-sm">{message}</p>
    </div>
  );
};

export default UploadProgress;
