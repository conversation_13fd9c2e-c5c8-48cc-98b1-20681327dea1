// components/ui/smart-pagination.tsx
import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from './pagination';

interface SmartPaginationProps {
  currentPage: number;
  totalPages: number;
  pageNumbers: number[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  baseUrl: string;
  className?: string;
}

export const SmartPagination: React.FC<SmartPaginationProps> = ({
  currentPage,
  totalPages,
  pageNumbers,
  hasNextPage,
  hasPreviousPage,
  baseUrl,
  className,
}) => {
  // Don't render if there's only one page or no pages
  if (totalPages <= 1) return null;

  const buildUrl = (page: number) => `${baseUrl}?page=${page}`;

  // Define variables to avoid undefined access errors
  const firstPageNumber = pageNumbers.length > 0 ? pageNumbers[0] : undefined;
  const lastPageNumber = pageNumbers.length > 0 ? pageNumbers[pageNumbers.length - 1] : undefined;

  return (
    <Pagination className={className}>
      <PaginationContent>
        {/* Previous button */}
        <PaginationItem>
          <PaginationPrevious
            href={hasPreviousPage ? buildUrl(currentPage - 1) : '#'}
            aria-disabled={!hasPreviousPage}
            className={!hasPreviousPage ? 'pointer-events-none opacity-50' : ''}
          />
        </PaginationItem>

        {/* First page + ellipsis if current page is far from start */}
        {firstPageNumber !== undefined && firstPageNumber > 1 && (
          <>
            <PaginationItem>
              <PaginationLink href={buildUrl(1)}>1</PaginationLink>
            </PaginationItem>
            {firstPageNumber > 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
          </>
        )}

        {/* Page numbers */}
        {pageNumbers.map((pageNum) => (
          <PaginationItem key={pageNum}>
            <PaginationLink href={buildUrl(pageNum)} isActive={pageNum === currentPage}>
              {pageNum}
            </PaginationLink>
          </PaginationItem>
        ))}

        {/* Last page + ellipsis if current page is far from end */}
        {lastPageNumber !== undefined && lastPageNumber < totalPages && (
          <>
            {lastPageNumber < totalPages - 1 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            <PaginationItem>
              <PaginationLink href={buildUrl(totalPages)}>{totalPages}</PaginationLink>
            </PaginationItem>
          </>
        )}

        {/* Next button */}
        <PaginationItem>
          <PaginationNext
            href={hasNextPage ? buildUrl(currentPage + 1) : '#'}
            aria-disabled={!hasNextPage}
            className={!hasNextPage ? 'pointer-events-none opacity-50' : ''}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};
