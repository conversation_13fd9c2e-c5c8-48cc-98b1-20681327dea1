import { useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

interface UsePaginationProps {
  limit?: number;
  total?: number;
}

interface UsePaginationReturn {
  page: number;
  limit: number;
  offset: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageNumbers: number[];
}

export const usePagination = ({
  limit = 10,
  total = 0,
}: UsePaginationProps): UsePaginationReturn => {
  const searchParams = useSearchParams();
  const page = Number(searchParams.get('page') ?? 1);
  const offset = (page - 1) * limit;

  const totalPages = total ? Math.ceil(total / limit) : 1;
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  const pageNumbers = useMemo(() => {
    const numbers = [];
    const showPages = 5; // Show 5 page numbers at most

    if (totalPages <= showPages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        numbers.push(i);
      }
    } else {
      // Show pages around current page
      let start = Math.max(1, page - 2);
      let end = Math.min(totalPages, page + 2);

      // Adjust if we're near the beginning or end
      if (page <= 3) {
        end = Math.min(5, totalPages);
      } else if (page >= totalPages - 2) {
        start = Math.max(1, totalPages - 4);
      }

      for (let i = start; i <= end; i++) {
        numbers.push(i);
      }
    }

    return numbers;
  }, [page, totalPages]);

  return {
    page,
    limit,
    offset,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    pageNumbers,
  };
};
