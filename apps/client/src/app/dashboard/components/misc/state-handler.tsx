import { Al<PERSON><PERSON>ir<PERSON>, Loader2, Smile } from 'lucide-react';

import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import Link from 'next/link';

interface StateHandlerProps {
  loading?: boolean;
  error?: string | null;
  isEmpty?: boolean;
  emptyMsg?: string;
  actionLabel?: string;
  actionUrl?: string;
  className?: string;
  icon?: React.ComponentType;
}

const StateHandler = ({
  loading = false,
  error = null,
  isEmpty = false,
  emptyMsg = 'No data available',
  actionLabel = 'Create New',
  actionUrl,
  className = '',
  icon: Icon = Smile,
}: StateHandlerProps) => {
  if (loading) {
    return (
      <div
        className={`flex min-h-[65vh] flex-col items-center justify-center gap-4 p-5 ${className} `}
      >
        <Loader2 className={`m-1.5 animate-spin`} />
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`flex min-h-[65vh] flex-col items-center justify-center gap-4 p-5 ${className} `}
      >
        <div className="text-red/50 text-6xl">
          <AlertCircle />
        </div>
        <div className="text-red/60 text-center text-lg">{error}</div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div
        className={cn(
          `flex min-h-[65vh] flex-col items-center justify-center gap-4 p-5`,
          className,
        )}
      >
        <div className="text-6xl text-gray-500">
          <Icon />
        </div>
        <div className="text-center text-lg">{emptyMsg}</div>
        {actionUrl && (
          <Link href={actionUrl}>
            <Button>{actionLabel}</Button>
          </Link>
        )}
      </div>
    );
  }

  return null;
};

export default StateHandler;
