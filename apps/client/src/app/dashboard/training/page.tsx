'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';

import { Card<PERSON>ontent, CardFooter, Card } from '@ps/ui/components/card';
import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { Button } from '@ps/ui/components/button';

type CardData = {
  image: string;
  badge: string;
  badgeVariant: 'secondary' | 'default';
  title: string;
  description: string;
  time: string;
};

const onBoardingCards: CardData[] = [
  {
    image: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg',
    badge: 'On-Boarding',
    badgeVariant: 'secondary',
    title: 'Join one million Google Professional Certificate graduates',
    description:
      'Create a structured 5E lesson plan with engaging, exploratory, explanatory, and evaluative learning experiences.',
    time: '15hr : 25min',
  },
  {
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80',
    badge: 'On-Boarding',
    badgeVariant: 'secondary',
    title: 'Applied Software Engineering Fundamentals Specialization',
    description:
      'Create a structured 5E lesson plan with engaging, exploratory, explanatory, and evaluative learning experiences.',
    time: '15hr : 25min',
  },
  {
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80',
    badge: 'On-Boarding',
    badgeVariant: 'secondary',
    title: 'Applied Software Engineering Fundamentals Specialization',
    description:
      'Create a structured 5E lesson plan with engaging, exploratory, explanatory, and evaluative learning experiences.',
    time: '15hr : 25min',
  },
  {
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80',
    badge: 'On-Boarding',
    badgeVariant: 'secondary',
    title: 'Applied Software Engineering Fundamentals Specialization',
    description:
      'Create a structured 5E lesson plan with engaging, exploratory, explanatory, and evaluative learning experiences.',
    time: '15hr : 25min',
  },
];

const trainingCards: CardData[] = [
  {
    image: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg',
    badge: 'Training',
    badgeVariant: 'secondary',
    title: 'Advanced AI Training Program',
    description:
      'Master advanced AI concepts and practical applications with hands-on projects and expert guidance.',
    time: '20hr : 10min',
  },
  {
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80',
    badge: 'Training',
    badgeVariant: 'secondary',
    title: 'Full Stack Development Bootcamp',
    description:
      'Deep dive into full stack development with real-world projects and collaborative learning.',
    time: '18hr : 45min',
  },
  {
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80',
    badge: 'Training',
    badgeVariant: 'secondary',
    title: 'Cloud Computing Essentials',
    description: 'Learn the essentials of cloud computing and how to deploy scalable applications.',
    time: '12hr : 30min',
  },
  {
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80',
    badge: 'Training',
    badgeVariant: 'secondary',
    title: 'Cloud Computing Essentials',
    description: 'Learn the essentials of cloud computing and how to deploy scalable applications.',
    time: '12hr : 30min',
  },
];

function CardGrid({ cards }: { cards: CardData[] }) {
  const router = useRouter();

  return (
    <div className="mt-4 flex flex-wrap gap-x-3 gap-y-4">
      {cards.map((card, idx) => {
        // Generate a slug from the card title
        const slug = card.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

        return (
          <Card
            key={idx}
            className="bg-background border-border flex h-full w-[315px] flex-col justify-between gap-0 rounded-[10px] border p-0 py-0 shadow-sm"
          >
            <div className="mx-auto mt-3 aspect-[16/9] w-[291px] overflow-hidden rounded-[6px] bg-gray-100">
              <Image
                src={card.image}
                alt={card.title}
                className="h-full w-full object-cover"
                height={169}
                width={291}
                unoptimized
              />
            </div>
            <CardContent className="flex flex-1 flex-col px-4 pb-0 pt-4">
              <div className="mb-2 flex items-center justify-between">
                <span
                  className={
                    'inline-flex items-center rounded-[8px] px-2 py-0.5 text-xs font-semibold ' +
                    (card.badge === 'On-Boarding'
                      ? 'bg-green-600 text-white'
                      : 'bg-sky-600 text-white')
                  }
                  style={{ minWidth: card.badge === 'On-Boarding' ? 91 : 64, height: 20 }}
                >
                  {card.badge}
                </span>
                <span className="text-muted-foreground text-xs font-normal">{card.time}</span>
              </div>
              <div className="text-foreground mb-1 line-clamp-2 font-semibold leading-6">
                {card.title}
              </div>
              <div className="text-foreground mb-2 line-clamp-2 text-sm font-normal leading-5">
                {card.description}
              </div>
            </CardContent>
            <CardFooter className="px-4 pb-4 pt-0">
              <Button
                className="w-full"
                variant="outline"
                style={{ minHeight: 36, marginTop: 12 }}
                onClick={() => router.push(`/dashboard/training/${slug}`)}
              >
                Start Now
              </Button>
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
}

function Section({
  title,
  description,
  cards,
}: {
  title: string;
  description: string;
  cards: CardData[];
}) {
  return (
    <div className="mb-10">
      <h2 className="mb-2 text-xl font-semibold">{title}</h2>
      <p className="text-muted-foreground mb-4">{description}</p>
      <CardGrid cards={cards} />
    </div>
  );
}

const Training = () => {
  return (
    <DashboardContainer
      className=""
      title="Learning Dashboard"
      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam"
    >
      <Section
        title="On-Boarding"
        description="Get started with our onboarding programs to quickly ramp up your skills."
        cards={onBoardingCards}
      />
      <Section
        title="Training"
        description="Explore advanced training programs to enhance your skills and knowledge."
        cards={trainingCards}
      />
    </DashboardContainer>
  );
};

export default Training;
