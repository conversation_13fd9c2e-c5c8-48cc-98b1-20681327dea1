'use client';

import { BookOpenText, CircleDashed, CircleChe<PERSON>, Download, Circle } from 'lucide-react';
import { useState } from 'react';
import Image from 'next/image';

import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import { useParams } from 'next/navigation';

const VIDEO_IMG =
  'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80';

const steps = [
  {
    title: 'Introduction video about Google',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.',
    content: (
      <Image
        src={VIDEO_IMG}
        alt="Google Video"
        className="mb-3 w-full rounded-xl sm:h-[506px] sm:w-[900px]"
        style={{ minHeight: 120, background: '#f3f4f6' }}
        height={506}
        width={900}
        unoptimized
      />
    ),
    status: 'finished',
    badge: (
      <Button variant="outline" className="text-[#0f766e]">
        {' '}
        <CircleCheck className="mr-2 h-4 w-4" /> Finished
      </Button>
    ),
  },
  {
    title: 'Company Guideline',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    content: (
      <div className="bg-background border-border mb-3 flex flex-col items-center gap-2 rounded-lg border px-4 py-3 sm:flex-row sm:gap-4">
        <span className="bg-destructive mb-2 inline-flex h-10 w-10 items-center justify-center rounded-lg sm:mb-0">
          <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
            <rect width="20" height="20" rx="4" fill="none" />
            <path
              d="M6 4h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2Z"
              fill="#e11d48"
            />
            <text x="10" y="15" textAnchor="middle" fill="#fff" fontSize="10" fontWeight="bold">
              PDF
            </text>
          </svg>
        </span>
        <div className="min-w-0 flex-1">
          <div className="truncate text-sm font-medium">Guideline for employees.pdf</div>
          <div className="text-muted-foreground text-xs">PDF • 12 Pages</div>
        </div>
        <div className="mt-2 flex gap-2 sm:mt-0">
          <Button size="sm" variant="outline">
            <BookOpenText className="mr-2 h-4 w-4" />
            Open
          </Button>
          <Button size="sm" variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
        </div>
      </div>
    ),
    status: 'current',
    badge: null,
  },
  {
    title: 'Update Your Profile Photo',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    content: (
      <div className="bg-muted flex flex-col items-center justify-between gap-2 rounded-lg border p-4 sm:flex-row">
        <span className="text-muted-foreground text-xs">
          Go to Settings &gt; User Profile &gt; Change Photo
        </span>
        <Button size="sm" variant="outline">
          Go to Settings
        </Button>
      </div>
    ),
    status: 'pending',
    badge: null,
  },
];

const StepIcon = ({ status }: { status: 'finished' | 'current' | 'pending' }) => {
  if (status === 'finished') {
    return (
      <span className="bg-background flex h-8 w-8 items-center justify-center rounded-full border">
        <CircleCheck className="h-4 w-4" />
      </span>
    );
  }
  if (status === 'current') {
    return (
      <span className="bg-background flex h-8 w-8 items-center justify-center rounded-full border">
        <CircleDashed className="h-4 w-4" />
      </span>
    );
  }
  return (
    <span className="border-border bg-background flex h-8 w-8 items-center justify-center rounded-full border-2">
      <Circle className="h-4 w-4" />
    </span>
  );
};

const TrainingDetailPage = () => {
  const [started, setStarted] = useState(false);
  const params = useParams() as { slug: string };

  if (!started) {
    return (
      <div className="flex w-full max-w-2xl flex-col items-start px-4 py-10">
        <Image
          src={VIDEO_IMG}
          alt="Training Visual"
          className="mb-8 w-full overflow-hidden rounded-[10px] object-cover"
          style={{
            width: '100%',
            maxWidth: 318,
            height: 'auto',
            aspectRatio: '16/9',
            minHeight: 120,
            background: '#f3f4f6',
          }}
          height={179}
          width={318}
          unoptimized
        />
        <h1 className="mb-4 w-full text-2xl font-bold capitalize leading-tight sm:text-4xl">
          {params.slug.replace(/-/g, ' ')}
        </h1>
        <p className="text-muted-foreground mb-8 max-w-2xl text-sm sm:text-base">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
          ut labore et dolore magna aliqua. Ut enim ad minim veniam.
        </p>
        <Button className="w-full sm:w-40" onClick={() => setStarted(true)}>
          Let&apos;s Get Started
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full max-w-5xl overflow-x-hidden px-2 py-10 sm:px-4">
      {/* Header Card */}
      <div className="mb-16 flex w-full flex-col gap-4 sm:flex-row sm:gap-6">
        <Image
          src={VIDEO_IMG}
          alt="Training Visual"
          className="mb-4 h-[179px] w-full overflow-hidden rounded-[10px] object-cover sm:mb-0 sm:w-[318px]"
          height={179}
          width={318}
          unoptimized
        />
        <div className="flex-1">
          <h1 className="mb-2 text-2xl font-bold capitalize leading-tight sm:text-4xl">
            {params.slug.replace(/-/g, ' ')}
          </h1>
          <p className="text-muted-foreground mb-4 max-w-2xl text-sm sm:text-base">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
            incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
          </p>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-bold">
              Completed: 0 / 3
            </Badge>
          </div>
        </div>
      </div>
      {/* Stepper */}
      <div className="relative flex w-full">
        {/* Vertical line */}
        <div
          className="bg-border absolute bottom-4 left-4 top-4 z-0 w-px"
          style={{ minHeight: 'calc(100% - 32px)' }}
        />
        <div className="flex w-full flex-col gap-8 sm:gap-12">
          {steps.map((step, idx) => (
            <div key={idx} className="relative flex w-full flex-row items-start">
              {/* Step icon */}
              <div className="relative z-10 flex flex-col items-center" style={{ minWidth: 32 }}>
                <StepIcon status={step.status as 'finished' | 'current' | 'pending'} />
                {idx !== steps.length - 1 && (
                  <div className="bg-border hidden w-px flex-1 sm:flex" style={{ minHeight: 48 }} />
                )}
              </div>
              {/* Step content */}
              <div
                className={`ml-4 flex-1 sm:ml-6 ${step.status === 'pending' ? 'pointer-events-none select-none opacity-50' : ''}`}
              >
                <div className="mb-2 mt-2 flex items-center gap-2">
                  <span className="text-muted-foreground text-xs font-medium">Step {idx + 1}</span>
                </div>
                <h3 className="mb-1 text-base font-semibold sm:text-lg">{step.title}</h3>
                <p className="text-muted-foreground mb-4 text-xs sm:text-sm">{step.description}</p>
                {step.content}
                {step.badge}
                {idx === 1 && <Button className="w-full sm:w-auto">Confirm Finished</Button>}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TrainingDetailPage;
