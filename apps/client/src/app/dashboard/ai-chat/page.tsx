'use client';

import { <PERSON><PERSON> } from '@ps/ui/components/button';
import { Sparkles, ImagePlus, TvMinimal, History, Plus, MessagesSquare, Clock } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@ps/ui/lib/utils';
import { Form } from '@ps/ui/components/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Link from 'next/link';
import { ContentData } from '../planning/utils/type';
import { useMutation } from '@tanstack/react-query';
import API from '@ps/ui/services/api';
import ChatArea from '../components/form/chat-area';
import Conversations from './components/conversations';

const tabs = [
  {
    label: 'AI Agent',
    icon: Sparkles,
    value: 'ai-agent',
    color: 'text-teal-500',
  },
  {
    label: 'Image Generator',
    icon: ImagePlus,
    value: 'image-generator',
    color: 'text-pink-500',
  },
  {
    label: 'Slide Generator',
    icon: TvMinimal,
    value: 'slide-generator',
    color: 'text-orange-500',
  },
];

const chatSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required'),
  fileUrls: z.array(z.string()).optional(),
  urls: z.array(z.string()).optional(),
  webSearch: z.boolean().optional(),
  knowledgeBase: z.boolean().optional(),
  category: z.string().optional(),
  snippetName: z.string().optional(),
  snippetDescription: z.string().optional(),
  grade: z.string().optional(),
  userInstructions: z.string().optional(),
  outputSettings: z.object({
    length: z.string().optional(),
    format: z.string().optional(),
    language: z.string().optional(),
  }),
});
export type ChatFormData = z.infer<typeof chatSchema>;

const AiChat = () => {
  const [activeTab, setActiveTab] = useState('ai-agent');
  const [generatedContent, setGeneratedContent] = useState<ContentData | null>(null);

  const form = useForm<ChatFormData>({
    resolver: zodResolver(chatSchema),
    defaultValues: {
      prompt: '',
      fileUrls: [],
      urls: [],
      webSearch: false,
      knowledgeBase: false,
      category: 'Mathematics',
      snippetName: 'Algebra Fundamentals',
      snippetDescription: 'Introduction to basic algebraic concepts and equations',
      grade: '9th Grade',
      userInstructions: 'Focus on practical examples and include step-by-step solutions',
      outputSettings: {
        length: 'medium',
        format: 'text',
        language: 'English',
      },
    },
  });

  const generateContent = useMutation({
    mutationFn: (data: ChatFormData) => {
      return API.post<ChatFormData, ContentData>(`/content-generation/${'snippet_123'}`, data);
    },
    onSuccess: (data) => {
      setGeneratedContent(data);
    },
    onError: (error) => {
      console.log(error);
    },
  });

  const onSubmit = async (data: ChatFormData) => {
    generateContent.mutate(data);
  };

  return generatedContent ? (
    <Conversations generatedContent={generatedContent} prompt={form.getValues('prompt')} />
  ) : (
    <div className="space-y-10">
      <div className="flex items-center justify-between">
        <div className="bg-muted p-[2px flex gap-1 rounded-md">
          {tabs.map((tab, index) => (
            <Button
              key={tab.value + index}
              variant={activeTab === tab.value ? 'outline' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(tab.value)}
            >
              <tab.icon className={cn(tab.color)} />
              {tab.label}
            </Button>
          ))}
        </div>

        <div className="flex items-center gap-2">
          <Link href="/dashboard/ai-chat/history">
            <Button variant="outline">
              <History /> History
            </Button>
          </Link>
          <Link href="/dashboard/ai-chat">
            <Button>
              <Plus />
              New Chat
            </Button>
          </Link>
        </div>
      </div>

      <div className="mx-auto w-[800px] space-y-10">
        <div>
          <video
            src="/images/circle-gradient-animation.webm"
            className="w-25 h-25 ml-[-25px]"
            autoPlay
            loop
            muted
            playsInline
          />

          <div className="mt-[-10px] flex w-4/5 flex-col gap-3">
            <h1 className="text-3xl font-bold">
              Hello Mayeen! <br /> How can i help you today?
            </h1>
            <p className="text-muted-foreground text-sm">
              AI agent help you find, plan & create. Simply ask what you want to do, attach link of
              files as context, toggle web search if you want to add information from the web.
            </p>
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-xl font-semibold">Recent Chats</h3>

          <div className="grid grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="space-y-6 rounded-lg border p-3">
                <p className="text-sm font-medium">
                  What additional resources can i provide for students who need extra help?
                </p>

                <div className="flex items-center gap-4">
                  <span className="text-muted-foreground flex items-center gap-[6px] text-xs">
                    <MessagesSquare className="size-4" /> 5 Messages
                  </span>
                  <span className="text-muted-foreground flex items-center gap-[6px] text-xs">
                    <Clock className="size-4" /> 1 min ago
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <ChatArea isLoading={generateContent.isPending} form={form} />
            </form>
          </Form>

          <div className="flex justify-center gap-2">
            <Button variant="outline" size="sm" className="text-muted-foreground rounded-full">
              Design a lesson plan?
            </Button>
            <Button variant="outline" size="sm" className="text-muted-foreground rounded-full">
              Design a lesson plan?
            </Button>
            <Button variant="outline" size="sm" className="text-muted-foreground rounded-full">
              Design a lesson plan?
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AiChat;
