'use client';

import { But<PERSON> } from '@ps/ui/components/button';
import { History, Plus, Copy, Save, Download, Languages, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useState } from 'react';
import { Form } from '@ps/ui/components/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import ReactMarkdown from 'react-markdown';
import Link from 'next/link';
import { ContentData } from '../../planning/utils/type';
import ChatArea from '../../components/form/chat-area';

const tabs = [
  {
    label: 'AI Chat',
    value: 'ai-chat',
  },
  {
    label: 'Image Generator',
    value: 'image-generator',
  },
];

const chatSchema = z.object({
  prompt: z.string().optional(),
  fileUrls: z.array(z.string()).optional(),
  urls: z.array(z.string()).optional(),
  webSearch: z.boolean().optional(),
});
type ChatFormData = z.infer<typeof chatSchema>;

type Props = {
  generatedContent: ContentData | null;
  prompt: string;
};

const Conversations = ({ generatedContent, prompt }: Props) => {
  const [activeTab, setActiveTab] = useState('ai-chat');

  const form = useForm<ChatFormData>({
    resolver: zodResolver(chatSchema),
  });

  const onSubmit = async (_: ChatFormData) => {};

  return (
    <div className="space-y-10">
      <div className="flex items-center justify-between">
        <div className="bg-muted flex gap-1 rounded-md p-[2px]">
          {tabs.map((tab, index) => (
            <Button
              key={tab.value + index}
              variant={activeTab === tab.value ? 'outline' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(tab.value)}
            >
              {tab.label}
            </Button>
          ))}
        </div>

        <div className="flex items-center gap-2">
          <Link href="/dashboard/ai-chat/history">
            <Button variant="outline">
              <History /> History
            </Button>
          </Link>
          <Link href="/dashboard/ai-chat">
            <Button>
              <Plus />
              New Chat
            </Button>
          </Link>
        </div>
      </div>

      <div className="mx-auto w-[800px] space-y-10">
        {/* conversations  */}
        <p className="bg-muted ml-auto flex w-fit rounded-lg p-2">{prompt}</p>

        <div className="space-y-3">
          <ReactMarkdown>{generatedContent?.content}</ReactMarkdown>

          <div className="text-muted-foreground flex items-center justify-between">
            <div className="flex gap-6">
              <Copy className="size-4 cursor-pointer" />
              <Save className="size-4 cursor-pointer" />
              <Download className="size-4 cursor-pointer" />
              <Languages className="size-4 cursor-pointer" />
            </div>

            <div className="flex gap-6">
              <ThumbsUp className="size-4 cursor-pointer" />
              <ThumbsDown className="size-4 cursor-pointer" />
            </div>
          </div>
        </div>

        <div className="flex w-fit flex-col gap-2">
          <Button variant="outline" size="sm" className="text-muted-foreground w-fit rounded-full">
            Would you like me to help design activities for a certain grade level?
          </Button>
          <Button variant="outline" size="sm" className="text-muted-foreground w-fit rounded-full">
            Design a lesson plan?
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <ChatArea isLoading={false} form={form} />
          </form>
        </Form>
      </div>
    </div>
  );
};

export default Conversations;
