import Link from 'next/link';

import { MessagesSquare, ChevronsRight, Sparkle, Clock, Plus } from 'lucide-react';

import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { Button } from '@ps/ui/components/button';

const History = () => {
  return (
    <DashboardContainer
      title="History"
      actions={
        <Link href="/dashboard/ai-chat">
          <Button>
            <Plus />
            New Chat
          </Button>
        </Link>
      }
    >
      <div className="space-y-2">
        {Array.from({ length: 10 }).map((_, index) => (
          <div
            key={index}
            className="flex items-center justify-between gap-3 rounded-md border p-3"
          >
            <div className="flex items-center gap-3">
              <Sparkle className="size-8 rounded-full bg-teal-600 p-[6px] text-white" />

              <div className="space-y-1">
                <p className="font-medium">
                  Using a school garden for science lessons is a fantastic way to engage students
                </p>

                <div className="flex items-center gap-4">
                  <span className="text-muted-foreground flex items-center gap-[6px] text-sm">
                    <MessagesSquare className="size-4" /> 5 Messages
                  </span>
                  <span className="text-muted-foreground flex items-center gap-[6px] text-sm">
                    <Clock className="size-4" /> 1 min ago
                  </span>
                </div>
              </div>
            </div>

            <ChevronsRight className="size-7 cursor-pointer p-1" />
          </div>
        ))}
      </div>
    </DashboardContainer>
  );
};

export default History;
