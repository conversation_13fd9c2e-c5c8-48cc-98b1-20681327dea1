'use client';

import { Badge } from '@ps/ui/components/badge';
import { But<PERSON> } from '@ps/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ps/ui/components/card';
import { Separator } from '@ps/ui/components/separator';
import { Activity, Download, Mic, Play, Square, Trash2, Volume2, VolumeX, Zap } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { ConversationHistory } from '../components/voice-agent/conversation-history';
import { useVoiceAgentLive } from '../components/voice-agent/use-voice-agent-live';

export default function VoiceAgentLivePage() {
  const {
    isConnected,
    isLiveActive,
    isStreaming,
    conversationHistory,
    error,
    liveSessionStatus,
    startLiveSession,
    stopLiveSession,
    connect,
    disconnect,
    clearConversation,
    playAudio,
    isAudioEnabled,
    toggleAudio,
    silenceThreshold,
    setSilenceThreshold,
  } = useVoiceAgentLive();

  const [sessionTime, setSessionTime] = useState(0);
  const [selectedModel, setSelectedModel] = useState(
    'gemini-2.5-flash-preview-native-audio-dialog',
  );
  const sessionTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Session timer
  useEffect(() => {
    if (isLiveActive) {
      sessionTimerRef.current = setInterval(() => {
        setSessionTime((prev) => prev + 1);
      }, 1000);
    } else {
      setSessionTime(0);
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    }

    return () => {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    };
  }, [isLiveActive]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleLiveSessionToggle = () => {
    if (isLiveActive) {
      stopLiveSession();
    } else {
      startLiveSession({
        model: selectedModel,
        systemInstruction:
          'You are a helpful AI voice assistant. Respond naturally and conversationally. Keep responses concise but friendly.',
      });
    }
  };

  return (
    <div className="container mx-auto space-y-6 py-6">
      {/* Header */}
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-3">
          <h1 className="text-3xl font-bold tracking-tight">Voice Agent Live</h1>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Zap className="h-3 w-3" />
            <span>Gemini Live API</span>
          </Badge>
        </div>
        <p className="text-muted-foreground">
          Real-time voice conversations with built-in Voice Activity Detection. Just click start and
          begin talking naturally.
        </p>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
              <Badge variant={isConnected ? 'default' : 'destructive'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </Badge>
              {isLiveActive && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <Activity className="h-3 w-3 animate-pulse" />
                  <span>Live Session Active</span>
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={toggleAudio} disabled={!isConnected}>
                {isAudioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>
              {isConnected ? (
                <Button variant="outline" size="sm" onClick={disconnect}>
                  Disconnect
                </Button>
              ) : (
                <Button size="sm" onClick={connect}>
                  Connect
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive text-sm">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Live Session Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mic className="h-5 w-5" />
            <span>Live Voice Session</span>
          </CardTitle>
          <CardDescription>
            Start a live session for real-time conversation with automatic voice detection
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Model Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">AI Model</label>
            <select
              className="w-full rounded-md border p-2"
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              disabled={isLiveActive}
            >
              <option value="gemini-2.5-flash-preview-native-audio-dialog">
                Gemini 2.5 Flash - Native Audio Dialog (Recommended)
              </option>
              <option value="gemini-2.5-flash-exp-native-audio-thinking-dialog">
                Gemini 2.5 Flash - Native Audio with Thinking
              </option>
              <option value="gemini-live-2.5-flash-preview">
                Gemini Live 2.5 Flash Preview (Half-cascade)
              </option>
              <option value="gemini-2.0-flash-live-001">Gemini 2.0 Flash Live (Stable)</option>
            </select>
            <p className="text-muted-foreground text-xs">
              Native audio models provide the best real-time conversation experience
            </p>
          </div>

          {/* Voice Activity Detection Settings */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Voice Activity Detection</label>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Silence Threshold (dB)</span>
                <span className="font-mono text-sm">{silenceThreshold}</span>
              </div>
              <input
                type="range"
                value={silenceThreshold}
                onChange={(e) => setSilenceThreshold(Number(e.target.value))}
                min={-60}
                max={-20}
                step={5}
                disabled={isLiveActive}
                className="h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200 dark:bg-gray-700"
              />
              <p className="text-muted-foreground text-xs">
                Lower values = more sensitive to silence. Recommended: -50dB
              </p>
            </div>
          </div>

          {/* Live Session Control */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Button
                size="lg"
                variant={isLiveActive ? 'destructive' : 'default'}
                className={`h-24 w-24 rounded-full text-lg font-semibold ${
                  isLiveActive
                    ? 'animate-pulse bg-red-500 hover:bg-red-600'
                    : 'bg-green-500 hover:bg-green-600'
                }`}
                onClick={handleLiveSessionToggle}
                disabled={!isConnected}
              >
                {isLiveActive ? <Square className="h-8 w-8" /> : <Play className="h-8 w-8" />}
              </Button>

              {isLiveActive && (
                <div className="absolute -bottom-10 left-1/2 -translate-x-1/2 transform">
                  <Badge variant="secondary" className="flex items-center space-x-1">
                    <Activity className="h-3 w-3 animate-pulse" />
                    <span>{formatTime(sessionTime)}</span>
                  </Badge>
                </div>
              )}
            </div>

            <div className="max-w-md text-center">
              <p className="text-muted-foreground text-sm">
                {isLiveActive ? (
                  <>
                    <span className="mb-2 flex items-center justify-center space-x-2">
                      <span className="h-2 w-2 animate-pulse rounded-full bg-red-500"></span>
                      <span>Live session active - speak naturally</span>
                    </span>
                    Session will automatically end after 2 seconds of silence
                  </>
                ) : (
                  'Click to start a live voice conversation. No need to click again - just start talking!'
                )}
              </p>
            </div>

            {/* Streaming Status */}
            {isStreaming && (
              <div className="text-muted-foreground flex items-center space-x-2 text-sm">
                <div className="flex space-x-1">
                  <div className="h-4 w-1 animate-pulse bg-blue-500"></div>
                  <div
                    className="h-4 w-1 animate-pulse bg-blue-500"
                    style={{ animationDelay: '0.1s' }}
                  ></div>
                  <div
                    className="h-4 w-1 animate-pulse bg-blue-500"
                    style={{ animationDelay: '0.2s' }}
                  ></div>
                </div>
                <span>Streaming audio in real-time</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Session Status */}
      {liveSessionStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Live Session Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Session Active:</span>
                <Badge
                  variant={liveSessionStatus.isLiveSession ? 'default' : 'secondary'}
                  className="ml-2"
                >
                  {liveSessionStatus.isLiveSession ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Stream Active:</span>
                <Badge
                  variant={liveSessionStatus.liveStreamActive ? 'default' : 'secondary'}
                  className="ml-2"
                >
                  {liveSessionStatus.liveStreamActive ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Processing:</span>
                <Badge
                  variant={liveSessionStatus.isProcessing ? 'default' : 'secondary'}
                  className="ml-2"
                >
                  {liveSessionStatus.isProcessing ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Messages:</span>
                <Badge variant="outline" className="ml-2">
                  {liveSessionStatus.conversationHistory || 0}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conversation History */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Live Conversation History</CardTitle>
            <CardDescription>Real-time voice conversation with Gemini Live API</CardDescription>
          </div>
          {conversationHistory.length > 0 && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const data = JSON.stringify(conversationHistory, null, 2);
                  const blob = new Blob([data], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `live-voice-conversation-${new Date().toISOString().split('T')[0]}.json`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={clearConversation}>
                <Trash2 className="mr-2 h-4 w-4" />
                Clear
              </Button>
            </div>
          )}
        </CardHeader>
        <Separator />
        <CardContent className="pt-6">
          <ConversationHistory
            history={conversationHistory}
            onPlayAudio={playAudio}
            isAudioEnabled={isAudioEnabled}
          />
          {conversationHistory.length === 0 && (
            <div className="py-8 text-center">
              <Mic className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <p className="text-muted-foreground">
                No conversation yet. Start a live session to begin talking with the AI.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Performance Benefits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="font-medium">Ultra-Low Latency</p>
                <p className="text-muted-foreground">~1-2 seconds response time</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-green-500" />
              <div>
                <p className="font-medium">Real-time Streaming</p>
                <p className="text-muted-foreground">250ms audio chunks</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Mic className="h-4 w-4 text-blue-500" />
              <div>
                <p className="font-medium">Auto Voice Detection</p>
                <p className="text-muted-foreground">No manual controls needed</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
