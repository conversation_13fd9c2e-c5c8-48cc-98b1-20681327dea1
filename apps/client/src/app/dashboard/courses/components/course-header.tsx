import type { Course } from '@ps/types';

import {
  LuTvMinimalPlay,
  LuShieldCheck,
  LuUsersRound,
  LuHandshake,
  LuCalendar,
  LuAward,
} from 'react-icons/lu';
import { formatDate } from 'date-fns';
import Image from 'next/image';

import { cn } from '@ps/ui/lib/utils';
import useAppContext from '@/providers/app-context';

import { CourseInfoEditDialog } from '../[courseId]/components/course-info-edit-dialog';

export default function CourseHeader({
  course,
  editable = false,
}: {
  course: Course;
  editable: boolean;
}) {
  const { user } = useAppContext();

  return (
    <header
      className={cn(
        'dark:bg-muted flex flex-col items-start justify-between gap-4 rounded-lg p-6 lg:flex-row',
        {
          'bg-green-50': course.type === 'on-boarding',
          'bg-orange-50': course.type === 'tutorial',
          'bg-sky-50': course.type === 'training',
        },
      )}
    >
      <div className="order-2 lg:order-1">
        <Image
          className="rounded-lg"
          src={course.coverImage}
          alt={course.title}
          height={160}
          width={284}
          unoptimized
        />

        <div className="mb-6 mt-4 max-w-2xl space-y-2">
          <h1 className="text-2xl font-bold md:text-4xl">{course.title}</h1>
          <p className="text-sm">{course.description}</p>
          <div
            className={cn(
              'dark:text-muted-foreground flex flex-wrap items-center gap-2 text-sm md:gap-4',
              {
                'text-green-900': course.type === 'on-boarding',
                'text-orange-900': course.type === 'tutorial',
                'text-sky-900': course.type === 'training',
              },
            )}
          >
            {user?.team?.name && (
              <div className="flex items-center gap-1.5">
                <LuShieldCheck size={12} /> {user?.team?.name}
              </div>
            )}
            {course.createdAt && (
              <div className="flex items-center gap-1.5">
                <LuCalendar size={12} /> {formatDate(course.createdAt || '', 'dd MMM yyyy')}
              </div>
            )}
            {course.audience?.length && (
              <div className="flex items-center gap-1.5">
                <LuUsersRound size={12} /> {course.audience?.join(', ')}
              </div>
            )}
          </div>
        </div>

        {editable && <CourseInfoEditDialog />}
      </div>

      <div
        className={cn(
          'order-1 flex w-full items-center justify-end gap-2 whitespace-nowrap text-sm capitalize lg:order-2 lg:w-auto',
          {
            'text-green-600': course.type === 'on-boarding',
            'text-sky-600': course.type === 'training',
            'text-orange-600': course.type === 'tutorial',
          },
        )}
      >
        {course.type === 'on-boarding' ? (
          <>
            <LuHandshake size={16} className="text-green-600" />
            <span className="font-semibold">On-Boarding</span>
          </>
        ) : course.type === 'training' ? (
          <>
            <LuAward size={16} className="text-sky-600" />
            <span className="font-semibold">Training</span>
          </>
        ) : (
          <>
            <LuTvMinimalPlay size={16} className="text-orange-600" />
            <span className="font-semibold">Tutorial</span>
          </>
        )}
      </div>
    </header>
  );
}
