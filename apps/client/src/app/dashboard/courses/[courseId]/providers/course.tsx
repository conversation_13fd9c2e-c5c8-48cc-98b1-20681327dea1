import type { Course, Snippet } from '@ps/types';

import { createContext, useContext, useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { toast } from 'sonner';

import API from '@ps/ui/services/api';

interface CourseContextType {
  course: Course;
  courseLoading: boolean;
  updateCourse: React.Dispatch<React.SetStateAction<Course>>;
  refetchCourse: () => void;

  snippet: Snippet;

  savingCourse: boolean;
  saveCourse: (course?: Partial<Course>) => void;
}

const CourseContext = createContext<CourseContextType | undefined>(undefined);

interface CourseProviderProps {
  children: React.ReactNode | ((props: CourseContextType) => React.ReactNode);
}

const snippetId = '686534ef5d06f685665c09bc';
export function CourseProvider({ children }: CourseProviderProps) {
  const [course, updateCourse] = useState<Course>({} as Course);
  const { courseId } = useParams();

  const {
    data = {} as Course,
    isLoading: courseLoading,
    refetch: refetchCourse,
  } = useQuery<Course>({
    queryKey: ['course', courseId],
    queryFn: () =>
      API.get<Course>(`/courses/${courseId}`).then((res) => {
        updateCourse(res);
        return res;
      }),
    enabled: !!courseId,
  });

  const { data: snippet = {} as Snippet } = useQuery<Snippet>({
    queryKey: ['snippets', snippetId],
    queryFn: () => API.get(`/snippets/${snippetId}?join=category`),
    staleTime: Infinity,
    gcTime: Infinity,
  });

  const { mutate: saveCourse, isPending: savingCourse } = useMutation({
    mutationFn: async (_course?: Partial<Course>) => {
      if (_course) updateCourse((c) => ({ ...c, ..._course }));
      return API.patch<Partial<Course>, Course>(`/courses/${courseId}`, _course || course).then(
        (resp) => {
          toast.success('Course info updated successfully');
          return resp;
        },
      );
    },
  });

  const value = {
    course: course?._id ? course : data,
    courseLoading,
    updateCourse,
    refetchCourse,

    snippet,

    savingCourse,
    saveCourse,
  };

  return (
    <CourseContext.Provider value={value}>
      {typeof children === 'function' ? children(value) : children}
    </CourseContext.Provider>
  );
}

export const useCourse = () => {
  const context = useContext(CourseContext);

  if (!context) {
    throw new Error('useCourse must be used within an CourseProvider');
  }

  return context;
};
