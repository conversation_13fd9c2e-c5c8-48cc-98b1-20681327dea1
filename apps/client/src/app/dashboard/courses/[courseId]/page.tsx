'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Dot } from 'react-icons/lu';
import { useRouter } from 'next/navigation';

import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { Skeleton } from '@ps/ui/components/skeleton';
import { Button } from '@ps/ui/components/button';
import Spinner from '@/components/spinner';

import { CourseProvider } from './providers/course';
import { CourseChapter } from './components/course-chapter';
import CourseGenerating from './components/course-generating';
import CourseHeader from '../components/course-header';

export default function CoursePage() {
  const router = useRouter();

  return (
    <CourseProvider>
      {({ course, courseLoading, savingCourse, saveCourse }) =>
        courseLoading ? (
          <div className="mx-auto flex h-[calc(100vh-10rem)] max-w-2xl flex-col items-center justify-center space-y-4">
            <Spinner />
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-2 w-full" />
            <Skeleton className="h-5 w-4/5" />
          </div>
        ) : course.status === 'generating' ? (
          <CourseGenerating />
        ) : (
          <DashboardContainer
            leftActions={
              <Button variant="secondary" onClick={() => router.back()}>
                <LuReply />
                Go Back
              </Button>
            }
            actions={
              <>
                <Button
                  className="w-28"
                  variant="secondary"
                  loading={savingCourse}
                  onClick={() => {
                    const status = course.status === 'published' ? 'generated' : 'published';
                    saveCourse({ status });
                  }}
                >
                  {course.status === 'published' ? <LuUndoDot /> : <LuSend />}
                  {course.status === 'published' ? 'Unpublish' : 'Publish'}
                </Button>

                <Button className="w-40" loading={savingCourse} onClick={() => saveCourse()}>
                  <LuSave />
                  Save Changes
                </Button>
              </>
            }
          >
            <div className="space-y-6">
              <CourseHeader course={course} editable={true} />

              {course.chapters.map((chapter, i) => (
                <CourseChapter
                  key={i}
                  index={i}
                  chapter={chapter}
                  mode={course.type === 'on-boarding' ? 'Step' : 'Chapter'}
                />
              ))}
            </div>
          </DashboardContainer>
        )
      }
    </CourseProvider>
  );
}
