import type { IconType } from 'react-icons/lib';
import type { Course } from '@ps/types';

import {
  LuOctagonAlert,
  LuBookOpen,
  LuBookText,
  LuCodeXml,
  LuTrash,
  LuImage,
  LuRadio,
  LuVideo,
  LuPlus,
} from 'react-icons/lu';
import { Fragment } from 'react';

import { Separator } from '@ps/ui/components/separator';
import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';

import { CourseChapterItem } from './course-chapter-item';
import { useCourse } from '../providers/course';
import CourseChapterTopicEditDialog from './course-chapter-topic-edit-dialog';
import CourseChapterTopicViewDialog from './course-chapter-topic-view-dialog';

const getTopicIcon = (
  type?: Course['chapters'][number]['topics'][number]['type'],
  content?: string,
): IconType => {
  if (!content) return LuOctagonAlert;
  if (type === 'text') return LuBookText;
  if (type === 'embed') return LuCodeXml;
  if (type === 'image') return LuImage;
  if (type === 'video') return LuVideo;
  if (type === 'audio') return LuRadio;
  if (type === 'document') return LuBookOpen;
  return LuOctagonAlert;
};

export default function CourseChapterTopics({
  topics,
  isEditing = false,
  chapterIndex,
}: {
  topics: Course['chapters'][number]['topics'];
  isEditing?: boolean;
  chapterIndex?: number;
}) {
  const { course, updateCourse } = useCourse();

  const addTopic = () => {
    if (typeof chapterIndex === 'number') {
      course.chapters[chapterIndex]?.topics.push({ title: '', content: '', type: 'text' });
      updateCourse({ ...course, chapters: [...course.chapters] });
    }
  };

  const deleteTopic = (topicIndex: number) => {
    if (typeof chapterIndex === 'number') {
      course.chapters[chapterIndex]?.topics.splice(topicIndex, 1);
      updateCourse({ ...course, chapters: [...course.chapters] });
    }
  };

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Topics</h4>

      {!!topics.length && (
        <div className="border-border rounded-lg border">
          {topics.map((topic, i) => (
            <Fragment key={i}>
              {i !== 0 && <Separator />}
              <CourseChapterItem
                Icon={getTopicIcon(topic.type, topic.content)}
                title={topic.title || 'Untitled Topic'}
                info={
                  <>
                    {topic.content && (
                      <Badge
                        className={cn('h-5 capitalize text-white', {
                          'bg-lime-600': topic.type === 'text',
                          'bg-sky-600': topic.type === 'embed',
                          'bg-purple-600': topic.type === 'image',
                          'bg-yellow-600': topic.type === 'audio',
                          'bg-cyan-600': topic.type === 'video',
                          'bg-pink-600': topic.type === 'document',
                        })}
                      >
                        {topic.type}
                      </Badge>
                    )}
                  </>
                }
                actions={
                  <>
                    {topic.content && (
                      <CourseChapterTopicViewDialog
                        Icon={getTopicIcon(topic.type, topic.content)}
                        topic={topic}
                      />
                    )}
                    {isEditing && (
                      <>
                        <CourseChapterTopicEditDialog
                          topic={topic}
                          chapterIndex={chapterIndex ?? 0}
                          topicIndex={i}
                        />
                        <Button variant="outline" size="icon" onClick={() => deleteTopic(i)}>
                          <LuTrash className="text-destructive" />
                        </Button>
                      </>
                    )}
                  </>
                }
              />
            </Fragment>
          ))}
        </div>
      )}

      {isEditing && (
        <div className="flex gap-2">
          <Button size="sm" variant="outline" onClick={addTopic}>
            <LuPlus /> Add Topic
          </Button>
        </div>
      )}
    </div>
  );
}
