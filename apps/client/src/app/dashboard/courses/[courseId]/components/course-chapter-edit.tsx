import type { Course } from '@ps/types';

import { TiptapEditor } from 'tiptap-editor';
import { zodResolver } from '@hookform/resolvers/zod';
import { LuSave, LuX } from 'react-icons/lu';
import { useForm } from 'react-hook-form';
import z from 'zod';

import {
  FormControl,
  FormMessage,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';

import { useCourse } from '../providers/course';
import CourseChapterAttachments from './course-chapter-attachments';
import CourseChapterTopics from './course-chapter-topics';

const courseChapterSchema = z.object({
  title: z.string().min(1),
  content: z.string().min(1),
});
type CourseChapterSchema = z.infer<typeof courseChapterSchema>;

export default function CourseChapterEdit({
  chapter,
  chapterIndex,
  setIsEditing,
}: {
  chapter: Course['chapters'][number];
  chapterIndex: number;
  setIsEditing: (isEditing: boolean) => void;
}) {
  const { course, saveCourse } = useCourse();

  const form = useForm<CourseChapterSchema>({
    resolver: zodResolver(courseChapterSchema),
    defaultValues: chapter,
  });

  const onSubmit = () => {
    const values = form.getValues();
    course.chapters[chapterIndex] = { ...chapter, ...values };
    console.log(
      'Topic Count:',
      course.chapters[chapterIndex]?.topics.length,
      chapter.topics.length,
    );
    // saveCourse(course);
    setIsEditing(false);
  };

  return (
    <Form {...form}>
      <form className="mt-2 space-y-6">
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Title</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Content</FormLabel>
                <FormControl>
                  <div className="border-border overflow-hidden rounded-lg border">
                    <TiptapEditor {...field} />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {course.type !== 'on-boarding' && (
          <CourseChapterTopics topics={chapter.topics} chapterIndex={chapterIndex} isEditing />
        )}
        <CourseChapterAttachments
          attachments={chapter.attachments}
          chapterIndex={chapterIndex}
          isEditing
        />

        <div className="flex items-center gap-3">
          <Button onClick={() => onSubmit()}>
            <LuSave /> Save Changes
          </Button>
        </div>
      </form>
    </Form>
  );
}
