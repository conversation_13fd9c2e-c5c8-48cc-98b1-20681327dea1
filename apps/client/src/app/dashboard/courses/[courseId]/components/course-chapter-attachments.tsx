import type { IconType } from 'react-icons/lib';
import type { Course } from '@ps/types';

import {
  LuOctagonAlert,
  LuBookOpen,
  LuDownload,
  LuUpload,
  LuImage,
  LuRadio,
  LuTrash,
  LuVideo,
  LuFile,
} from 'react-icons/lu';
import { Fragment } from 'react';

import { Separator } from '@ps/ui/components/separator';
import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';

import { CourseChapterItem } from './course-chapter-item';
// import { useCourse } from '../providers/course';

type AttachmentType = Course['chapters'][number]['attachments'][number]['type'];

// const getFileType = (type: string): AttachmentType => {
//   if (type.includes('image')) return 'image';
//   if (type.includes('video')) return 'video';
//   if (type.includes('audio')) return 'audio';
//   return 'document';
// };

const getFileExtension = (fileName: string): string => {
  return fileName.split('.').pop()?.toUpperCase() || '';
};

const getFileIcon = (type?: AttachmentType, extension?: string): IconType => {
  if (extension === 'PDF') return LuBookOpen;
  if (type === 'image') return LuImage;
  if (type === 'video') return LuVideo;
  if (type === 'audio') return LuRadio;
  if (type === 'document') return LuFile;
  return LuOctagonAlert;
};

export default function CourseChapterAttachments({
  attachments,
  // chapterIndex,
  isEditing = false,
}: {
  attachments: Course['chapters'][number]['attachments'];
  chapterIndex?: number;
  isEditing?: boolean;
}) {
  // const { course, updateCourse } = useCourse();

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Attachments</h4>

      {!!attachments.length && (
        <div className="border-border rounded-lg border">
          {attachments.map((attachment, i) => (
            <Fragment key={i}>
              {i !== 0 && <Separator />}
              <CourseChapterItem
                Icon={getFileIcon(attachment.type, getFileExtension(attachment.name))}
                title={attachment.name}
                info={
                  <Badge className="bg-pink-600 py-[1px] text-white">
                    {getFileExtension(attachment.name)}
                  </Badge>
                }
                actions={
                  <>
                    <Button variant="outline">
                      <LuDownload />
                      Download
                    </Button>
                    <Button variant="outline" size="icon">
                      <LuTrash className="text-destructive" />
                    </Button>
                  </>
                }
              />
            </Fragment>
          ))}
        </div>
      )}

      {isEditing && (
        <div className="flex gap-2">
          <Button className="w-32" size="sm" variant="outline">
            <LuUpload /> Upload Files
          </Button>
        </div>
      )}
    </div>
  );
}
