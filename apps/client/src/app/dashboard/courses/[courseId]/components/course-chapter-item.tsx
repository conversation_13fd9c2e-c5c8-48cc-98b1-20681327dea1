import type { IconType } from 'react-icons/lib';

interface CourseChapterItemProps {
  Icon: IconType;
  title: string;
  info?: React.ReactNode;
  actions?: React.ReactNode;
}

export function CourseChapterItem({ Icon, info, actions, ...item }: CourseChapterItemProps) {
  return (
    <div className="flex min-h-[70px] flex-wrap items-center justify-between gap-4 px-4 py-3">
      <div className="flex items-center gap-4">
        <div className="dark:bg-muted flex size-8 items-center justify-center rounded-full shadow-sm">
          <Icon />
        </div>

        <div>
          <h3 className="font-semibold md:max-w-xl md:truncate">{item.title}</h3>
          <div className="flex items-center gap-1.5">{info}</div>
        </div>
      </div>

      <div className="flex w-full justify-end gap-3 lg:w-auto">{actions}</div>
    </div>
  );
}
