import type { Course } from '@ps/types';

import { VisuallyHidden } from '@radix-ui/react-visually-hidden';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogFooter,
  DialogHeader,
  DialogClose,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { Button } from '@ps/ui/components/button';

export default function CourseChapterTopicViewDialog({
  topic,
  Icon,
}: {
  topic: Course['chapters'][number]['topics'][number];
  Icon: React.ComponentType;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Icon />
          Open
        </Button>
      </DialogTrigger>

      <DialogContent className="lg:min-w-4xl p-6 md:p-14">
        <VisuallyHidden>
          <DialogHeader>
            <DialogTitle>View Topic</DialogTitle>
            <DialogDescription>View the topic details</DialogDescription>
          </DialogHeader>
        </VisuallyHidden>

        <h2 className="text-lg font-semibold">{topic.title}</h2>

        {topic.type === 'embed' ? (
          <div
            className="flex justify-center overflow-hidden rounded-lg border"
            dangerouslySetInnerHTML={{ __html: topic.content }}
          />
        ) : (
          <div
            className="prose prose-sm max-h-[calc(100vh-20rem)] min-w-full overflow-y-auto"
            dangerouslySetInnerHTML={{ __html: topic.content }}
          />
        )}

        <DialogFooter className="md:justify-start">
          <DialogClose asChild>
            <Button>Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
