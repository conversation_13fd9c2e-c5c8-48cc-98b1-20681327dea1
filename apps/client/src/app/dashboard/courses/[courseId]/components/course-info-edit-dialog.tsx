import { LuPencilLine } from 'react-icons/lu';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Image from 'next/image';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogFooter,
  DialogHeader,
  DialogClose,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { FormControl, FormField, FormLabel, FormItem, Form } from '@ps/ui/components/form';
import { FileUploadProvider, FileUpload } from '@ps/ui/custom/file-upload';
import { Textarea } from '@ps/ui/components/textarea';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';

import { useCourse } from '../providers/course';

const courseInfoSchema = z.object({
  coverImage: z.string().optional(),
  title: z.string(),
  description: z.string(),
});
type CourseInfoSchema = z.infer<typeof courseInfoSchema>;

export function CourseInfoEditDialog() {
  const { course, savingCourse, saveCourse } = useCourse();

  const form = useForm<CourseInfoSchema>({
    resolver: zodResolver(courseInfoSchema),
    defaultValues: {
      coverImage: course.coverImage,
      title: course.title,
      description: course.description,
    },
  });

  const submit = async (values: CourseInfoSchema) => {
    saveCourse(values);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <LuPencilLine />
          Edit Course Info
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Course Info</DialogTitle>
          <DialogDescription>
            Update the title, description and cover image of your course.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form className="space-y-6" onSubmit={form.handleSubmit(submit)}>
            <div className="space-y-2">
              <FileUploadProvider type="image" label="Cover Image" folder={`courses/${course._id}`}>
                <FileUpload onUpload={(urls) => form.setValue('coverImage', urls[0])} />
              </FileUploadProvider>
              <Image
                className="rounded-lg"
                src={form.watch('coverImage') || ''}
                alt="Cover Image"
                height={96}
                width={160}
                unoptimized
              />
            </div>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button className="w-full" type="submit" loading={savingCourse}>
                  Save
                </Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
