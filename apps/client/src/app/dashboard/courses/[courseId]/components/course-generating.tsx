import { useInterval } from 'react-use';
import { useState } from 'react';

import { Progress } from '@ps/ui/components/progress';
import Spinner from '@/components/spinner';

import { useCourse } from '../providers/course';

export default function CourseGenerating() {
  const { refetchCourse } = useCourse();
  const [progress, setProgress] = useState(0);

  // Increment the progress every 1 second until 99%
  useInterval(() => {
    setProgress((prev) => Math.min(prev + 1, 99));
  }, 520);

  // Refetch the course every 5 seconds after 90% to check if it's generated
  useInterval(() => {
    if (progress > 90) {
      refetchCourse();
    }
  }, 5 * 1000);

  return (
    <div className="mx-auto flex h-[calc(100vh-10rem)] max-w-2xl flex-col items-center justify-center space-y-4">
      <Spinner />
      <p className="font-semibold">Generating your course... {progress}%</p>
      <Progress value={progress} className="w-full" />
      <p className="text-muted-foreground text-sm">
        This will take a couple of minutes. You can move to other pages if you want.
      </p>
    </div>
  );
}
