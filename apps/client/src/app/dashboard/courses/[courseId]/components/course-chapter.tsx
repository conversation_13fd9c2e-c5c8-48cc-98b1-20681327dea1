import type { Course } from '@ps/types';

import { LuEllipsisVertical, LuPencilLine, LuTrash } from 'react-icons/lu';
import { useState } from 'react';

import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { Button } from '@ps/ui/components/button';

import CourseChapterAttachments from './course-chapter-attachments';
import CourseChapterTopics from './course-chapter-topics';
import CourseChapterEdit from './course-chapter-edit';
import { useCourse } from '../providers/course';

export const CourseChapter = ({
  chapter,
  index,
  mode = 'Step',
}: {
  chapter: Course['chapters'][number];
  index: number;
  mode?: 'Chapter' | 'Step';
}) => {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <section className="border-border rounded-lg border p-6">
      <h5 className="text-muted-foreground text-sm">
        {mode} {index + 1}
      </h5>

      {isEditing ? (
        <CourseChapterEdit chapter={chapter} setIsEditing={setIsEditing} chapterIndex={index} />
      ) : (
        <CourseChapterDetails
          chapter={chapter}
          setIsEditing={setIsEditing}
          chapterIndex={index}
          mode={mode}
        />
      )}
    </section>
  );
};

const CourseChapterDetails = ({
  chapter,
  mode,
  chapterIndex,
  setIsEditing,
}: {
  chapter: Course['chapters'][number];
  mode: 'Chapter' | 'Step';
  chapterIndex: number;
  setIsEditing: (isEditing: boolean) => void;
}) => {
  const { course, updateCourse } = useCourse();

  const deleteChapter = () => {
    course.chapters.splice(chapterIndex, 1);
    updateCourse(course);
  };

  return (
    <div className="mt-2 space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">{chapter.title}</h2>
        <article
          dangerouslySetInnerHTML={{ __html: chapter.content }}
          className="prose prose-sm dark:prose-invert"
        />
      </div>

      {!!chapter.topics.length && <CourseChapterTopics topics={chapter.topics} />}
      {!!chapter.attachments.length && (
        <CourseChapterAttachments attachments={chapter.attachments} />
      )}

      <div className="flex items-center gap-3">
        <Button onClick={() => setIsEditing(true)}>
          <LuPencilLine /> Edit {mode}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <LuEllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => deleteChapter()}>
              <LuTrash className="text-red-600" />
              <span className="font-medium text-red-600">Delete {mode}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
