import type { Course, Snippet } from '@ps/types';

import {
  LuPencilLine,
  LuSparkles,
  LuFilePlus,
  LuFilePen,
  LuVolume2,
  LuCodeXml,
  LuSave,
  LuTrash,
  LuCopy,
} from 'react-icons/lu';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { TiptapEditor } from 'tiptap-editor';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { marked } from 'marked';
import { toast } from 'sonner';
import z from 'zod';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogFooter,
  DialogHeader,
  DialogClose,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import {
  FormMessage,
  FormControl,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import { Textarea } from '@ps/ui/components/textarea';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import ChatArea from '@/app/dashboard/components/form/chat-area';

import { useCourse } from '../providers/course';
import { useMutation } from '@tanstack/react-query';
import API from '@ps/ui/services/api';
import { ContentData, ContentPayload } from '@/app/dashboard/planning/utils/type';

const courseChapterTopicSchema = z.object({
  title: z.string(),
  content: z.string(),
});

type CourseChapterTopicSchema = z.infer<typeof courseChapterTopicSchema>;
type TopicMode = 'write' | 'generate' | 'embed' | 'upload';

const getType = (
  mode: TopicMode,
  mimeType?: string,
): Course['chapters'][number]['topics'][number]['type'] => {
  switch (mode) {
    case 'write':
      return 'text';
    case 'generate':
      return 'text';
    case 'embed':
      return 'embed';
    case 'upload': {
      if (mimeType?.startsWith('image/')) return 'image';
      if (mimeType?.startsWith('video/')) return 'video';
      if (mimeType?.startsWith('audio/')) return 'audio';
      return 'document';
    }
  }
};

export default function CourseChapterTopicEditDialog({
  topic,
  chapterIndex,
  topicIndex,
}: {
  topic: Course['chapters'][number]['topics'][number];
  chapterIndex: number;
  topicIndex: number;
}) {
  const [mode, setMode] = useState<TopicMode>();
  const { course, updateCourse } = useCourse();

  const form = useForm<CourseChapterTopicSchema>({
    resolver: zodResolver(courseChapterTopicSchema),
    defaultValues: {
      title: topic.title,
      content: topic.content,
    },
  });

  const topicTitle = form.watch('title');
  const content = form.watch('content');

  const onSubmit = () => {
    const values = form.getValues();
    if (course.chapters[chapterIndex]?.topics[topicIndex]) {
      course.chapters[chapterIndex].topics[topicIndex] = {
        ...values,
        type: getType(mode ?? 'write'),
      };
      updateCourse(course);
    }
  };

  useEffect(() => {
    if (topic.content) {
      if (topic.type === 'text') {
        setMode('write');
      } else if (topic.type === 'embed') {
        setMode('embed');
      } else {
        setMode('upload');
      }
    }
  }, [topic.type, topic.content]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">
          <LuPencilLine />
          Edit
        </Button>
      </DialogTrigger>

      <DialogContent className="lg:min-w-4xl p-6 md:p-14">
        <VisuallyHidden>
          <DialogHeader>
            <DialogTitle>Edit Topic</DialogTitle>
            <DialogDescription>Edit the topic details</DialogDescription>
          </DialogHeader>
        </VisuallyHidden>

        <Form {...form}>
          <form className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-muted-foreground text-xs font-normal">Topic</FormLabel>
                  <FormControl>
                    <Input
                      className="rounded-none border-0 border-b px-0 shadow-none"
                      placeholder="Untitled Topic"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {mode === 'write' && (
              <>
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <TiptapEditor className="max-h-[calc(100vh-20rem)] min-h-10" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter className="md:justify-start">
                  <DialogClose asChild>
                    <Button onClick={() => onSubmit()}>
                      <LuSave />
                      Save
                    </Button>
                  </DialogClose>
                </DialogFooter>
              </>
            )}

            {mode === 'generate' && (
              <CourseChapterTopicGenerateForm
                chapterIndex={chapterIndex}
                topicIndex={topicIndex}
                topicTitle={topicTitle}
                onGenerate={(content) => {
                  form.setValue('content', content);
                  setMode('write');
                }}
              />
            )}

            {mode === 'embed' && (
              <>
                {!content ? (
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Embed Code</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Insert your embed code here" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ) : (
                  <div>
                    <div
                      dangerouslySetInnerHTML={{ __html: content }}
                      className="flex justify-center overflow-hidden rounded-lg border"
                    />

                    <div className="mt-2 flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(content);
                          toast.success('Embed code copied to clipboard');
                        }}
                      >
                        <LuCopy />
                        Copy Embed Code
                      </Button>
                      <Button
                        className="text-destructive"
                        onClick={() => form.setValue('content', '')}
                        variant="outline"
                      >
                        <LuTrash />
                        Remove
                      </Button>
                    </div>
                  </div>
                )}
                <DialogFooter className="md:justify-start">
                  <DialogClose asChild>
                    <Button onClick={() => onSubmit()}>
                      <LuCodeXml />
                      Embed
                    </Button>
                  </DialogClose>
                </DialogFooter>
              </>
            )}

            {!mode && (
              <div className="mx-auto grid w-full max-w-xl grid-cols-2 justify-center gap-3 p-0 md:p-10 lg:grid-cols-4">
                <Button variant="outline" onClick={() => setMode('write')}>
                  <LuFilePen />
                  Write
                </Button>
                <Button variant="outline" onClick={() => setMode('generate')}>
                  <LuSparkles />
                  Generate
                </Button>
                <Button variant="outline" onClick={() => setMode('embed')}>
                  <LuVolume2 />
                  Embed
                </Button>
                <Button variant="outline" onClick={() => setMode('upload')}>
                  <LuFilePlus />
                  Upload
                </Button>
              </div>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

const contentSchema = z.object({
  prompt: z.string().optional(),
  fileUrls: z.array(z.string()).optional(),
  urls: z.array(z.string()).optional(),
  webSearch: z.boolean().optional(),
  knowledgeBase: z.boolean().optional(),
});
type ContentFormData = z.infer<typeof contentSchema>;
const CourseChapterTopicGenerateForm = ({
  topicTitle,
  chapterIndex,
  topicIndex,
  onGenerate,
}: {
  topicTitle: string;
  chapterIndex: number;
  topicIndex: number;
  onGenerate: (content: string) => void;
}) => {
  const { course, snippet } = useCourse();

  const form = useForm<ContentFormData>({
    resolver: zodResolver(contentSchema),
    defaultValues: {
      prompt: '',
      fileUrls: [],
      urls: [],
      webSearch: false,
      knowledgeBase: course.inputs?.useKnowledgeBase || false,
    },
  });

  const { mutate: generateContent, isPending } = useMutation({
    mutationFn: (data: ContentFormData) => {
      if (!topicTitle) {
        return Promise.reject(new Error('Please enter a topic title'));
      }
      return API.post<ContentPayload, ContentData>(`/content-generation/${course._id}`, {
        ...data,
        userInstructions: data.prompt || '',
        prompt: generatePromptFromTemplate(course, snippet, topicTitle, chapterIndex, topicIndex),
        category: snippet?.category?.name,
        snippetName: snippet?.name,
        snippetDescription: snippet?.description,
        outputSettings: {
          length: 'medium',
          format: 'text',
          language: 'English',
          image: false,
          graph: false,
          chart: false,
          diagram: false,
          citation: false,
        },
      });
    },
    onSuccess: async (data) => {
      onGenerate(await marked.parse(data.content));
    },
    onError: (error) => {
      toast.error('Failed to generate content', {
        description: error.message,
      });
    },
  });

  return (
    <ChatArea
      isLoading={isPending}
      form={form}
      config={{
        placeholder: 'Write something about the topic...',
        submitButtonText: 'Generate',
        submitButtonType: 'button',
        enableVoiceInput: false,
      }}
      onSubmit={() => generateContent(form.getValues())}
    />
  );
};

const generatePromptFromTemplate = (
  course: Course,
  snippet: Snippet,
  topicTitle: string,
  chapterIndex: number,
  topicIndex: number,
) => {
  let prompt = snippet.promptTemplate
    ?.replace('{courseName}', course.title)
    .replace('{courseDescription}', course.description)
    .replaceAll('{audience}', course.audience.join(', '))
    .replace('{courseType}', course.type)
    .replace('{chapterName}', course.chapters[chapterIndex]?.title || '')
    .replace('{chapterDescription}', course.chapters[chapterIndex]?.content || '')
    .replace('{chapterIndex}', String(chapterIndex || 0))
    .replace('{totalChapters}', String(course.chapters.length))
    .replaceAll('{topicTitle}', topicTitle)
    .replace('{topicIndex}', String(topicIndex || 0))
    .replace('{totalTopicsInChapter}', String(course.chapters[chapterIndex]?.topics.length || 0))
    .replace('{contentType}', 'text')
    .replace(
      '{existingTopicTitles}',
      course.chapters[chapterIndex]?.topics.map((t) => t.title).join(', ') || '',
    )
    .replace('{contentLength}', 'moderate');

  if (chapterIndex > 0) {
    prompt = prompt?.replace(
      '{previousChapterTopics}',
      course.chapters[chapterIndex - 1]?.topics.map((t) => t.title).join(', ') || '',
    );
  }

  if (chapterIndex < course.chapters.length - 1) {
    prompt = prompt?.replace(
      '{nextChapterTopics}',
      course.chapters[chapterIndex + 1]?.topics.map((t) => t.title).join(', ') || '',
    );
  }

  return prompt;
};
