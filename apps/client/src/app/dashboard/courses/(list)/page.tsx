'use client';

import type { Course } from '@ps/types';

import { useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Fragment } from 'react';

import { Pagination } from '@ps/ui/custom/pagination';
import { Separator } from '@ps/ui/components/separator';
import API from '@ps/ui/services/api';

// import { SmartPagination } from '../../components/smart-pagination/smart-pagination';
import CourseCardSkeleton from './components/course-card-skeleton';
import CourseCard from './components/course-card';

export default function CourseListPage() {
  const searchParams = useSearchParams();
  const courseType = searchParams.get('type');
  const limit = searchParams.get('limit') || '10';
  const page = searchParams.get('page') || '1';

  const query = new URLSearchParams();
  query.set('limit', limit);
  query.set('page', page);
  if (courseType) query.set('s', `{"$and":[{"type":{"$eq":"${courseType}"}}]}`);
  const queryString = query.toString();

  const {
    data: { data: courses, total } = { data: [], total: 0 },
    isLoading,
    refetch,
  } = useQuery<ListResponse<Course>>({
    queryKey: ['courses', queryString],
    queryFn: () => API.get<ListResponse<Course>>(`/courses?${queryString}`),
  });

  return (
    <>
      <div className="border-border mt-2 rounded-lg border">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, i) => (
            <Fragment key={i}>
              {i !== 0 && <Separator />}
              <CourseCardSkeleton />
            </Fragment>
          ))
        ) : courses.length === 0 ? (
          <div className="my-1 flex h-full min-h-28 items-center justify-center">
            <p className="text-muted-foreground">No courses found</p>
          </div>
        ) : (
          courses?.map((course, i) => (
            <Fragment key={course._id}>
              {i !== 0 && <Separator />}
              <CourseCard key={course._id} course={course} refetch={refetch} />
            </Fragment>
          ))
        )}
      </div>

      <Pagination
        className="mt-4"
        limit={parseInt(limit, 10)}
        page={parseInt(page, 10)}
        total={total}
        onPaging={({ queryString: qs }) => window.history.replaceState({}, '', qs)}
      />
    </>
  );
}
