'use client';

import { Fragment } from 'react';

import { Separator } from '@ps/ui/components/separator';

import CourseCardSkeleton from './components/course-card-skeleton';

export default function CourseListLoading() {
  return (
    <div className="border-border mt-4 rounded-lg border">
      {Array.from({ length: 3 }).map((_, i) => (
        <Fragment key={i}>
          {i !== 0 && <Separator />}
          <CourseCardSkeleton />
        </Fragment>
      ))}
    </div>
  );
}
