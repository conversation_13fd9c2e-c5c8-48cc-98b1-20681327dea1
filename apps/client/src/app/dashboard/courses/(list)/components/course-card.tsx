'use client';

import type { Course } from '@ps/types';

import { LuEllipsisVertical, LuLoaderCircle, LuPencil, LuTrash } from 'react-icons/lu';
import { formatDistanceToNow } from 'date-fns';
import { useMutation } from '@tanstack/react-query';
import Image from 'next/image';
import Link from 'next/link';

import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';
import API from '@ps/ui/services/api';

export default function CourseCard({ course, refetch }: { course: Course; refetch: () => void }) {
  const { mutate: deleteCourse } = useMutation({
    mutationFn: () => API.remove(`/courses/${course._id}`),
    onSuccess: () => refetch(),
  });

  const isGenerating = course.status === 'generating';

  return (
    <div className="flex flex-wrap items-center justify-between gap-4 p-3">
      <div className="flex flex-wrap items-center gap-4 lg:flex-nowrap">
        <Link href={`/dashboard/courses/${course._id}`}>
          <Image
            className="aspect-video w-full min-w-44 flex-shrink-0 rounded-md object-cover object-top lg:h-24"
            src={course.coverImage}
            alt={course.title}
            height={96}
            width={176}
            unoptimized
          />
        </Link>

        <div className="max-w-xl">
          <Link href={`/dashboard/courses/${course._id}`}>
            <h3 className="font-semibold">{course.title}</h3>
            <p className="mt-0.5 text-sm">
              {course.description.slice(0, 165)}
              {course.description.length > 165 && '...'}
            </p>
          </Link>

          <div className="mt-2 flex items-center gap-4">
            <Badge
              className={cn('border-none capitalize text-white', {
                'bg-orange-600': course.type === 'tutorial',
                'bg-green-600': course.type === 'on-boarding',
                'bg-sky-600': course.type === 'training',
              })}
              variant="outline"
            >
              {course.type}
            </Badge>

            <div className="text-muted-foreground text-xs">
              {course.duration && (
                <>
                  <span>
                    Time: {Math.floor(course.duration / 3600)}hr :{' '}
                    {Math.floor((course.duration % 3600) / 60)}min
                  </span>
                  <span className="mx-2">•</span>
                </>
              )}

              <span>
                Last Updated:{' '}
                {formatDistanceToNow(new Date(course.updatedAt ?? ''), { addSuffix: true })}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex w-full flex-shrink-0 justify-end gap-2 lg:w-auto">
        <Button variant="outline" asChild>
          <Link href={`/dashboard/courses/${course._id}`}>
            {isGenerating ? <LuLoaderCircle className="animate-spin" /> : <LuPencil />}
            {isGenerating ? 'Generating...' : 'Edit Course'}
          </Link>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <LuEllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => deleteCourse()}>
              <LuTrash className="text-red-600" />
              <span className="font-medium text-red-600">Delete Course</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
