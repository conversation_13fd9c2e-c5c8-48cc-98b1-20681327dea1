'use client';

import type { Course } from '@ps/types';

import { useSearchParams } from 'next/navigation';
import { LuGraduationCap } from 'react-icons/lu';
import Link from 'next/link';

import { TabsTrigger, TabsContent, TabsList, Tabs } from '@ps/ui/components/tabs';

import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { Button } from '@ps/ui/components/button';

export default function CourseListLayout({ children }: { children: React.ReactNode }) {
  const searchParams = useSearchParams();
  const courseType = (searchParams.get('type') as Course['type'] | 'all') || 'all';

  const switchTab = (value: string) => {
    const path = value === 'all' ? '/dashboard/courses' : `?type=${value}`;
    window.history.replaceState({}, '', path);
  };

  return (
    <DashboardContainer
      title="Courses"
      description="Your course library contains all the course you've created in this system. You can easily find, filter, update, hide, or remove them from this list."
      actions={
        <Button className="w-44" asChild>
          <Link href="/dashboard/courses/create">
            <LuGraduationCap />
            Create a Course
          </Link>
        </Button>
      }
    >
      <Tabs defaultValue={courseType} onValueChange={switchTab}>
        <TabsList className="w-full max-w-96 justify-start gap-1.5 overflow-x-auto [&>button]:cursor-pointer">
          <TabsTrigger value="all">All Courses</TabsTrigger>
          <TabsTrigger value="on-boarding">On-Boarding</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
          <TabsTrigger value="tutorial">Tutorials</TabsTrigger>
        </TabsList>

        <TabsContent value={courseType}>{children}</TabsContent>
      </Tabs>
    </DashboardContainer>
  );
}
