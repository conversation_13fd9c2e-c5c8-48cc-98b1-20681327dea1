'use client';

import type { Course } from '@ps/types';

import { LuTvMinimalPlay, LuHandshake, LuLoader, LuAward } from 'react-icons/lu';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import {
  FormDescription,
  FormControl,
  FormMessage,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { TagInput } from '@ps/ui/components/tag-input';
import { Button } from '@ps/ui/components/button';
import { Switch } from '@ps/ui/components/switch';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';
import API from '@ps/ui/services/api';

import ChatArea from '../../components/form/chat-area';

const courseSchema = z.object({
  type: z.enum(['on-boarding', 'training', 'tutorial']),
  // Topic
  prompt: z
    .string()
    .min(1, 'Prompt is required')
    .max(100, 'Prompt must be less than 100 characters'),
  urls: z.array(z.string()).optional(),
  fileUrls: z.array(z.string()).optional(),
  webSearch: z.boolean().optional(),
  // End Topic
  audience: z.array(z.string()),
  numberOfChapters: z.coerce.number().min(1, 'Number of steps must be at least 1'),
  useKnowledgeBase: z.boolean(),
});
export type CourseSchema = z.infer<typeof courseSchema>;

export default function CreateCoursePage() {
  const router = useRouter();

  const form = useForm<CourseSchema>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      useKnowledgeBase: true,
      prompt: '',
      fileUrls: [],
      urls: [],
    },
  });

  const { mutate: createCourse, isPending } = useMutation({
    mutationFn: async (data: CourseSchema) => {
      const isOnBoarding = data.type === 'on-boarding';
      return API.post<
        CourseSchema & {
          generateChapterImages: boolean;
          generateCoverImage: boolean;
          generateTopics: boolean;
        },
        Course
      >('/courses/generate', {
        ...data,
        generateChapterImages: false,
        generateCoverImage: true,
        generateTopics: !isOnBoarding,
      }).then((resp) => {
        toast.success('Course is being generated...');
        router.push(`/dashboard/courses/${resp._id}`);
        return resp;
      });
    },
  });

  const onSubmit = async (data: CourseSchema) => {
    createCourse(data);
  };

  return (
    <DashboardContainer
      className="max-w-2xl"
      title="Create a New Course"
      description="To create a new course first select the course type. Then simply describe what the course is about and fill the additional input fields."
    >
      <Form {...form}>
        <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Type</FormLabel>
                <CourseTypeSelector selectedType={field.value} onSelect={field.onChange} />
              </FormItem>
            )}
          />

          {form.watch('type') && (
            <>
              <FormItem>
                <FormLabel>Topic</FormLabel>
                <ChatArea
                  isLoading={false}
                  form={form}
                  config={{
                    placeholder: `Write about your ${form.watch('type')}...`,
                    enableVoiceInput: false,
                    hideSubmitButton: true,
                  }}
                />
              </FormItem>

              <FormField
                control={form.control}
                name="audience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Audience</FormLabel>
                    <FormControl>
                      <TagInput
                        value={field.value || []}
                        onChange={(newTags) => field.onChange(newTags)}
                        placeholder="Eg. Administrator, Teacher"
                      />
                    </FormControl>
                    <FormDescription>
                      Use Comma separation to add multiple audience.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="numberOfChapters"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Steps</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="Insert a number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <label className="flex items-start gap-3">
                <FormField
                  control={form.control}
                  name="useKnowledgeBase"
                  render={({ field }) => (
                    <FormItem className="inline">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="mt-[2px]"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="space-y-1">
                  <h6 className="font-medium">Add relevant assets from knowledge base</h6>
                  <p className="text-muted-foreground text-sm">
                    AI will automatically add relevant files from your knowledge base. You would be
                    able to review and manage it as you want.
                  </p>
                </div>
              </label>

              <Button type="submit" className="w-full" disabled={isPending}>
                {isPending ? (
                  <LuLoader className="size-4 animate-spin" />
                ) : (
                  <span>Generate Course</span>
                )}
              </Button>
            </>
          )}
        </form>
      </Form>
    </DashboardContainer>
  );
}

const CourseTypeSelector = ({
  selectedType,
  onSelect,
}: {
  selectedType: CourseSchema['type'];
  onSelect: (type: CourseSchema['type']) => void;
}) => {
  const Button = ({ className, ...props }: React.ComponentProps<'button'>) => (
    <button
      className={cn(
        'border-border flex cursor-pointer flex-col items-center justify-center gap-2 rounded-lg border transition-colors',
        'hover:bg-orange-50 hover:text-black',
        'h-20 text-xs md:h-24 md:text-base',
        className,
      )}
      {...props}
    />
  );

  return (
    <div className="grid w-full grid-cols-3 gap-4">
      <Button
        className={cn(selectedType === 'on-boarding' && 'bg-green-50 text-black')}
        onClick={() => onSelect('on-boarding')}
      >
        <LuHandshake className="text-green-600" size={24} />
        <span className="font-semibold">On-Boarding</span>
      </Button>

      <Button
        className={cn(selectedType === 'training' && 'bg-sky-50 text-black')}
        onClick={() => onSelect('training')}
      >
        <LuAward className="text-sky-600" size={24} />
        <span className="font-semibold">Training</span>
      </Button>

      <Button
        className={cn(selectedType === 'tutorial' && 'bg-orange-50 text-black')}
        onClick={() => onSelect('tutorial')}
      >
        <LuTvMinimalPlay className="text-orange-600" size={24} />
        <span className="font-semibold">Tutorial</span>
      </Button>
    </div>
  );
};
