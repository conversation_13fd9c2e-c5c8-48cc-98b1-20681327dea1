import type { KnowledgeBaseFileType } from '@ps/types/resources/knowledge-base.type';

import { FileText, FileType, Volume2, Image, Video, File, Link } from 'lucide-react';

export type KnowledgeBaseItemStyle = {
  icon: React.ElementType;
  color: {
    bg: string;
    border: string;
    icon: string;
  };
};

export const knowledgeBaseItemStyles: Record<
  KnowledgeBaseFileType | 'text',
  KnowledgeBaseItemStyle
> = {
  video: {
    icon: Video,
    color: {
      bg: 'bg-sky-50',
      border: 'border-sky-200',
      icon: 'bg-sky-200 text-sky-900',
    },
  },
  audio: {
    icon: Volume2,
    color: {
      bg: 'bg-emerald-50',
      border: 'border-emerald-200',
      icon: 'bg-emerald-200 text-emerald-900',
    },
  },
  image: {
    icon: Image,
    color: {
      bg: 'bg-lime-50',
      border: 'border-lime-200',
      icon: 'bg-lime-200 text-lime-900',
    },
  },
  pdf: {
    icon: FileType,
    color: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'bg-orange-200 text-orange-900',
    },
  },
  doc: {
    icon: FileText,
    color: {
      bg: 'bg-pink-50',
      border: 'border-pink-200',
      icon: 'bg-pink-200 text-pink-900',
    },
  },
  url: {
    icon: Link,
    color: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'bg-purple-200 text-purple-900',
    },
  },
  text: {
    icon: FileText,
    color: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'bg-blue-200 text-blue-900',
    },
  },
  unknown: {
    icon: File,
    color: {
      bg: 'bg-gray-50',
      border: 'border-gray-200',
      icon: 'bg-gray-200 text-gray-900',
    },
  },
};
