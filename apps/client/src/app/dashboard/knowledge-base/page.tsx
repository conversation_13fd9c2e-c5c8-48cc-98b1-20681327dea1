'use client';

import { useMutation, useQuery } from '@tanstack/react-query';
import { CloudUpload, EllipsisVertical, Link as LinkIcon, Trash } from 'lucide-react';
import NextLink from 'next/link';

import { KnowledgeBaseFileType, KnowledgeBaseItem } from '@ps/types/resources/knowledge-base.type';
import { Button } from '@ps/ui/components/button';
import { Separator } from '@ps/ui/components/separator';
import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import API from '@ps/ui/services/api';

import StateHandler from '../components/misc/state-handler';
import { SmartPagination } from '../components/smart-pagination/smart-pagination';
import { usePagination } from '../components/smart-pagination/usePagination';
import { KnowledgeBaseItemStyle, knowledgeBaseItemStyles } from './utils/constants';
import { Popover, PopoverContent, PopoverTrigger } from '@ps/ui/components/popover';
import { toast } from 'sonner';

const KnowledgeBase = () => {
  const ITEMS_PER_PAGE = 10;

  const { page, limit, offset } = usePagination({
    limit: ITEMS_PER_PAGE,
    total: 0,
  });

  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['knowledge-base', page],
    queryFn: () =>
      API.get<ListResponse<KnowledgeBaseItem>>(`/knowledge-base?limit=${limit}&offset=${offset}`),
  });

  // Fetch knowledge base stats
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['knowledge-base-stats'],
    queryFn: () =>
      API.get<{
        stats: Array<{ type: string; count: number; displayName: string }>;
        total: number;
      }>('/knowledge-base/stats'),
  });

  const deleteKnowledgeBaseItem = useMutation({
    mutationFn: (id: string) => API.remove(`/knowledge-base/${id}`),
    onSuccess: () => {
      toast.success('Knowledge base item deleted');
      refetch();
    },
    onError: () => {
      toast.error('Failed to delete knowledge base item');
    },
  });

  const paginationData = usePagination({
    limit: ITEMS_PER_PAGE,
    total: data?.total || 0,
  });

  return (
    <DashboardContainer
      title="Knowledge Base"
      description="LMS AI will be trained based on your knowledge base content. To let your AI learn better please add tags & descriptions on your content."
      actions={
        <>
          <NextLink href="/dashboard/knowledge-base/add-link">
            <Button variant="outline">
              <LinkIcon /> Add Link
            </Button>
          </NextLink>
          <NextLink href="/dashboard/knowledge-base/upload-file">
            <Button>
              <CloudUpload /> Upload File
            </Button>
          </NextLink>
        </>
      }
    >
      {isLoading || isError || !data?.items?.length ? (
        <StateHandler
          loading={isLoading}
          error={error?.message}
          isEmpty={!data?.items?.length}
          emptyMsg="No knowledge base items found"
        />
      ) : (
        <>
          {' '}
          {/* Stats items */}
          <div className="grid grid-cols-6 gap-6">
            {statsLoading
              ? // Loading skeleton
                Array.from({ length: 6 }).map((_, index) => (
                  <div
                    key={index}
                    className="flex h-fit animate-pulse items-center gap-3 rounded-xl border bg-gray-100 p-3"
                  >
                    <div className="h-10 w-10 rounded-full bg-gray-300" />
                    <div className="flex-1">
                      <div className="mb-1 h-5 rounded bg-gray-300" />
                      <div className="h-3 w-1/2 rounded bg-gray-300" />
                    </div>
                  </div>
                ))
              : statsData?.stats?.map((item) => {
                  const style = knowledgeBaseItemStyles[item.type as KnowledgeBaseFileType];
                  const IconComponent = style?.icon;

                  if (!style || !IconComponent || item.type === 'text') return null;

                  return (
                    <div
                      key={item.type}
                      className={`flex h-fit items-center gap-3 rounded-xl border p-3 ${style.color.border} ${style.color.bg}`}
                    >
                      <IconComponent className={`h-10 w-10 ${style.color.icon} rounded-full p-2`} />
                      <div>
                        <h6 className="font-semibold">{item.displayName}</h6>
                        <p className="text-xs">{item.count}</p>
                      </div>
                    </div>
                  );
                })}
          </div>
          {/* Recent files */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Recent Files</h3>

            <div className="divide-y divide-gray-200 rounded-xl border">
              {data?.items?.map((item) => {
                const { fileType, fileSizeFormatted } = item.metadata || {};

                // Map type to file type for styling
                let displayFileType: KnowledgeBaseFileType | 'text';
                switch (item.type) {
                  case 'video':
                    displayFileType = 'video';
                    break;
                  case 'audio':
                    displayFileType = 'audio';
                    break;
                  case 'image':
                    displayFileType = 'image';
                    break;
                  case 'pdf':
                    displayFileType = 'pdf';
                    break;
                  case 'doc':
                    displayFileType = 'doc';
                    break;
                  case 'url':
                    displayFileType = 'url';
                    break;
                  case 'text':
                    displayFileType = 'text';
                    break;
                  case 'upload':
                  default:
                    displayFileType = fileType || 'unknown';
                    break;
                }

                const style = knowledgeBaseItemStyles[displayFileType] as KnowledgeBaseItemStyle;
                const IconComponent = style.icon;

                return (
                  <div key={item.id} className="flex items-center justify-between gap-20 px-4 py-3">
                    <div className="flex items-center gap-6">
                      <IconComponent className={`h-10 w-12 ${style.color.icon} rounded-full p-2`} />

                      <div className="space-y-[2px]">
                        <h6 className="font-semibold">
                          {item.name || item.metadata?.filename || 'Untitled'}
                        </h6>
                        <p className="line-clamp-1 text-sm">{item.text}</p>
                        <p className="text-muted-foreground text-sm">
                          {item.type && `Type: ${item.type}`}
                          {fileSizeFormatted && `• Size: ${fileSizeFormatted}`}
                        </p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          window.open(item.metadata.url, '_blank');
                        }}
                      >
                        Open File
                      </Button>

                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" size="icon">
                            <EllipsisVertical />
                          </Button>
                        </PopoverTrigger>

                        <PopoverContent className="w-fit">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => deleteKnowledgeBaseItem.mutate(item.id)}
                            disabled={deleteKnowledgeBaseItem.isPending}
                            loading={deleteKnowledgeBaseItem.isPending}
                          >
                            <Trash />
                            Delete
                          </Button>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                );
              })}
            </div>

            <SmartPagination
              currentPage={paginationData.page}
              totalPages={paginationData.totalPages}
              pageNumbers={paginationData.pageNumbers}
              hasNextPage={paginationData.hasNextPage}
              hasPreviousPage={paginationData.hasPreviousPage}
              baseUrl="/dashboard/knowledge-base"
            />
          </div>
        </>
      )}
      <Separator />
    </DashboardContainer>
  );
};

export default KnowledgeBase;
