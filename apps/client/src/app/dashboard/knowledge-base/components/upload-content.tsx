'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Link } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDebounce } from 'react-use';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@ps/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@ps/ui/components/form';
import { Input } from '@ps/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ps/ui/components/select';
import { TagInput } from '@ps/ui/components/tag-input';
import { Textarea } from '@ps/ui/components/textarea';
import { FileState, FileUpload, FileUploadProvider } from '@ps/ui/custom/file-upload';
import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import API from '@ps/ui/services/api';

import UploadProgress from '../../components/upload-progress';
import { knowledgeBaseItems } from '../utils/data';
import LinkPreview from './link-preview';

const formSchema = z.object({
  url: z.string().url({ message: 'Please enter a valid URL' }).min(1, 'URL is required'),
  type: z.enum(['video', 'audio', 'image', 'pdf', 'doc', 'url'] as const, {
    required_error: 'Please select a file type',
    invalid_type_error: 'Please select a valid file type',
  }),
  tags: z.array(z.string()).optional(),
  description: z.string().optional(),
  fileName: z.string().optional(),
});

export type UrlMeta = {
  title: string;
  description: string;
  image?: string;
  duration?: string;
  durationSeconds?: number;
};

type UploadContentType = 'link' | 'file';

const UploadContent = ({ uploadContentType }: { uploadContentType: UploadContentType }) => {
  const [urlMeta, setUrlMeta] = useState<UrlMeta | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStage, setUploadStage] = useState<string>('');
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      url: '',
      type: undefined,
      tags: [],
      description: '',
      fileName: '',
    },
  });

  const addToKnowledgeBase = useMutation({
    mutationFn: async (values: z.infer<typeof formSchema>) => {
      // Simulate staged progress
      setUploadStage('Analyzing content..');
      setUploadProgress(25);

      await new Promise((resolve) => setTimeout(resolve, 500));

      setUploadStage('Analyzing content..');
      setUploadProgress(50);

      // Transform the payload based on type
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const payload: any = {
        ...values,
        type: values.type,
        tags: values.tags,
        description: values.description,
      };

      // For file types, send fileUrl instead of url
      if (values.type !== 'url') {
        payload.fileUrl = values.url;
      } else {
        payload.url = values.url;
      }

      const result = await API.post('/knowledge-base', payload);

      setUploadStage('Adding to knowledge base..');
      setUploadProgress(75);

      await new Promise((resolve) => setTimeout(resolve, 400));

      setUploadProgress(100);
      return result;
    },
  });

  const getUrlMeta = useMutation({
    mutationFn: async (url: string) => API.post<{ url: string }, UrlMeta>('/url/meta', { url }),
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    setUploadProgress(0);
    setUploadStage('');
    addToKnowledgeBase
      .mutateAsync(values)
      .then(async (data) => {
        console.log('data', data);
        router.push('/dashboard/knowledge-base');
        toast.success('Added to knowledge base');
      })
      .catch((error) => {
        console.log('error', error);
        toast.error('Something went wrong');
        setUploadProgress(0);
        setUploadStage('');
      });
  }

  const onURLInput = async (url: string) => {
    form.trigger('url');
    if (form.formState.errors.url) return;
    try {
      const meta = await getUrlMeta.mutateAsync(url);
      form.setValue('fileName', meta.title);
      setUrlMeta(meta);
    } catch (error) {
      console.log('error', error);
      setUrlMeta(null);
    }
  };

  const url = form.watch('url');
  useDebounce(() => url && onURLInput(url), 500, [url]);

  if (addToKnowledgeBase.isPending) {
    return <UploadProgress progress={uploadProgress} stage={uploadStage} className="w-[600px]" />;
  }

  return (
    <DashboardContainer
      title="Add Link"
      description="LMS AI will be trained on your uploaded content. To let your AI learn better please add tags & descriptions on your content."
      className="w-[640px]"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {uploadContentType === 'link' ? (
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem className="rounded-lg border p-1">
                  <div className="relative">
                    <Link className="absolute left-3 top-2 block size-5" />
                    <FormControl>
                      <Input placeholder="Insert your link here" className="pl-10" {...field} />
                    </FormControl>
                  </div>
                  <LinkPreview
                    loading={getUrlMeta.isPending}
                    urlMeta={urlMeta}
                    urlError={form.formState.errors.url}
                    fieldValue={field.value}
                  />
                </FormItem>
              )}
            />
          ) : (
            <>
              <FileUploadProvider>
                <FileUpload
                  label="Upload File"
                  onUpload={(urls) => form.setValue('url', urls[0] || '')}
                  onFilesChange={(files: FileState[]) => {
                    form.setValue('fileName', files[0]?.file.name || '');
                  }}
                />
                <p className="text-sm text-red-500">
                  {form.formState.errors.url?.message && 'Please upload a file'}
                </p>
              </FileUploadProvider>
            </>
          )}

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>File Type</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select file type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {knowledgeBaseItems.map((item) => (
                      <SelectItem key={item.id} value={item.type}>
                        <span className="font-medium">{item.title}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tags"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tags (Optional)</FormLabel>
                <FormControl>
                  <TagInput
                    value={field.value || []}
                    onChange={(newTags) => field.onChange(newTags)}
                    placeholder="Use comma separation to add multiple tags"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Type a short description about the uploaded files"
                    className="h-[150px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full">
            Add to Knowledge Base
          </Button>
        </form>
      </Form>
    </DashboardContainer>
  );
};

export default UploadContent;
