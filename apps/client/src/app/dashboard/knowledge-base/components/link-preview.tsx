import { FieldError } from 'react-hook-form';
import { UrlMeta } from './upload-content';

type LinkPreviewProps = {
  loading: boolean;
  urlMeta?: UrlMeta | null;
  urlError?: FieldError | null;
  fieldValue: string;
};

const LinkPreview = ({ loading, urlMeta, urlError, fieldValue }: LinkPreviewProps) => {
  return (
    <div className="flex h-[150px] items-center justify-center">
      {urlError ? (
        <p className="text-sm text-red-600">{urlError.message}</p>
      ) : loading ? (
        <p className="text-muted-foreground text-sm">Fetching meta data...</p>
      ) : urlMeta && fieldValue.length > 0 ? (
        <div className="flex gap-4">
          {urlMeta?.image && (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              alt="url-meta"
              className="xs:min-w-[166px] max-h-[96px] w-[130px] rounded-md object-cover drop-shadow"
              src={urlMeta?.image}
            />
          )}

          <div className="flex flex-col overflow-hidden">
            <h1 className="ellipsis r1 text-md font-semibold lg:text-base">
              {urlMeta?.title || `Sorry, couldn't fetch title. 🥺`}
            </h1>

            {urlMeta?.duration && (
              <div className="mt-1 text-sm font-medium lg:text-sm">{urlMeta?.duration} Min</div>
            )}

            <div className="ellipsis r2 mt-2 text-sm">{urlMeta?.description}</div>
          </div>
        </div>
      ) : urlError ? (
        <p className="text-sm text-red-600">Please check if the URL is valid and try again.</p>
      ) : (
        <p className="text-muted-foreground text-sm">Link Preview</p>
      )}
    </div>
  );
};

export default LinkPreview;
