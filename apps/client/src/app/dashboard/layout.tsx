import { SidebarProvider, SidebarTrigger, SidebarInset } from '@ps/ui/components/sidebar';
import { DashboardSidebar } from '@/app/dashboard/components/layout/dashboard-sidebar';
import { DashboardBreadcrumb } from '@/app/dashboard/components/layout/breadcrumb';
import { Separator } from '@ps/ui/components/separator';
import { Sonner } from '@ps/ui/components/sonner';

export default function Page({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <DashboardSidebar />
      <SidebarInset>
        <header className="supports-[backdrop-filter]:bg-background/60 group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b backdrop-blur transition-[width,height] ease-linear">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <DashboardBreadcrumb />
          </div>
        </header>

        <div className="p-6">{children}</div>
      </SidebarInset>

      <Sonner />
    </SidebarProvider>
  );
}
