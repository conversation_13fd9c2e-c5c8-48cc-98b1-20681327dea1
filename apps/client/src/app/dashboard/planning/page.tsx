'use client';

import type { Category, Snippet } from '@ps/types';

import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

import { CardDescription, CardContent, CardTitle, Card } from '@ps/ui/components/card';
import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { Separator } from '@ps/ui/components/separator';
import { cn } from '@ps/ui/lib/utils';
import API from '@ps/ui/services/api';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@ps/ui/components/form';
import ChatArea from '../components/form/chat-area';
import StateHandler from '../components/misc/state-handler';

const planSchema = z.object({
  prompt: z
    .string()
    .min(1, 'Prompt is required')
    .max(100, 'Prompt must be less than 100 characters'),
  fileUrls: z.array(z.string()).optional(),
  urls: z.array(z.string()).optional(),
});
type PlanFormData = z.infer<typeof planSchema>;

const Planning = () => {
  const [selectedCategory, setSelectedCategory] = useState<Category>();
  const {
    data: { data: categories } = { data: [] },
    isLoading: isLoadingCategories,
    error: errorCategories,
  } = useQuery<ListResponse<Category>>({
    queryKey: ['categories'],
    queryFn: () =>
      API.get<ListResponse<Category>>(
        '/categories?s={"$and":[{"type":{"$eq":"lesson"}}]}&sort=sequence:asc',
      ).then((r) => {
        setSelectedCategory(r.data[0]!);
        return r;
      }),
  });

  const {
    data: { data: snippets } = { data: [] },
    isLoading: isLoadingSnippets,
    error: errorSnippets,
  } = useQuery<ListResponse<Snippet>>({
    queryKey: ['snippets', selectedCategory?._id],
    queryFn: () =>
      API.get(`/snippets?s={"$and":[{"category":{"$eq":"${selectedCategory?._id}"}}]}`),
    enabled: !!selectedCategory,
  });

  const form = useForm<PlanFormData>({
    resolver: zodResolver(planSchema),
  });

  const onSubmit = async (data: PlanFormData) => {
    console.log(data);
  };

  return (
    <DashboardContainer title="Plan & Create your lesson materials..">
      {/* text area and buttons */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-3/5 space-y-6">
          <ChatArea
            isLoading={false}
            form={form}
            config={{
              enableVoiceInput: false,
              enableWebSearch: false,
              submitButtonText: 'Start Planning',
            }}
          />
        </form>
      </Form>

      <Separator />

      {isLoadingCategories || errorCategories || categories.length === 0 ? (
        <StateHandler
          loading={isLoadingCategories}
          error={errorCategories && 'Something went wrong'}
          isEmpty={categories.length === 0}
          emptyMsg="No categories found"
          className="min-h-[50vh]"
        />
      ) : (
        <>
          {/* categories */}
          <div className="bg-muted flex w-fit flex-wrap rounded-md p-[2px]">
            {categories.map((category) => (
              <p
                key={category._id}
                onClick={() => setSelectedCategory(category)}
                className={cn('cursor-pointer rounded-md px-3 py-[6px] text-sm font-medium', {
                  'bg-primary-foreground text-primary shadow-sm':
                    category._id === selectedCategory?._id,
                })}
              >
                {category.name}
              </p>
            ))}
          </div>

          {/* snippets */}
          {isLoadingSnippets || errorSnippets || snippets.length === 0 ? (
            <StateHandler
              loading={isLoadingSnippets}
              error={errorSnippets && 'Something went wrong'}
              isEmpty={snippets.length === 0}
              emptyMsg="No snippets found"
              className="min-h-[40vh]"
            />
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {snippets.map((snippet) => (
                <SnippetCard key={snippet._id} snippet={snippet} />
              ))}
            </div>
          )}
        </>
      )}
    </DashboardContainer>
  );
};

const SnippetCard = ({ snippet }: { snippet: Snippet }) => {
  return (
    <Link href={`/dashboard/planning/${snippet._id}`} key={snippet._id}>
      <Card className="p-0">
        <CardContent className="p-3">
          <Image
            className="size-9 rounded-lg"
            src={snippet.icon}
            alt={snippet.name}
            unoptimized
            height={36}
            width={36}
          />
          <CardTitle className="mt-2">{snippet.name}</CardTitle>
          <CardDescription className="mt-1">{snippet.description}</CardDescription>
        </CardContent>
      </Card>
    </Link>
  );
};

export default Planning;
