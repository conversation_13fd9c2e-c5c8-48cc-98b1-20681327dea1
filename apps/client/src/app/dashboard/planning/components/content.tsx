'use client';

import type { Snippet } from '@ps/types';
import remarkGfm from 'remark-gfm';

import {
  ChevronsRight,
  ChevronDown,
  Languages,
  Download,
  Ellipsis,
  Sparkles,
  Volume2,
  Pencil,
  Share2,
  Undo2,
  Copy,
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';

import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { Separator } from '@ps/ui/components/separator';
import { Textarea } from '@ps/ui/components/textarea';
import { Button } from '@ps/ui/components/button';

import { ContentData } from '../utils/type';

type Props = {
  generatedContent: ContentData | null;
  clearGeneratedContent: () => void;
  snippet: Snippet;
};

const Content = ({ generatedContent, clearGeneratedContent, snippet }: Props) => {
  return (
    <DashboardContainer>
      <div className="flex items-center justify-between">
        <Button variant="secondary" onClick={clearGeneratedContent}>
          <Undo2 />
          Go Back
        </Button>

        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Share2 />
            Share
          </Button>

          <Button variant="outline">
            <Download />
            Download
          </Button>
        </div>
      </div>

      <div className="mx-20 space-y-6">
        <div className="space-y-6 rounded-lg border p-6">
          <div className="space-y-[6px]">
            <p className="text-muted-foreground text-sm">Planning . {snippet.name}</p>
            <h2 className="text-2xl font-bold">Create a lesson plan for Mitosis</h2> {/*  prompt */}
            <Separator />
            <div className="flex items-center justify-between">
              <div className="mt-3 flex items-center gap-2">
                <Button variant="outline">
                  <Volume2 /> Listen
                </Button>
                <Button variant="outline">
                  <Languages />
                  Translate
                </Button>
                <Button variant="outline">
                  <Copy />
                  Copy
                </Button>
                <Button variant="outline">
                  <Pencil />
                  Edit
                </Button>
                <Button variant="outline" size="icon">
                  <Ellipsis />
                </Button>
              </div>

              <Button variant="outline" size="icon">
                <ChevronDown />
              </Button>
            </div>
          </div>

          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {generatedContent?.content || ''}
            </ReactMarkdown>
          </div>
        </div>

        <div className="space-y-1">
          <div className="flex items-center justify-between gap-3 rounded-md border p-4">
            <p className="text-sm font-semibold">
              What additional resources can i provide for students who need extra help?
            </p>
            <ChevronsRight className="border-muted-foreground size-7 cursor-pointer rounded-full border p-1" />
          </div>

          <div className="flex items-center justify-between gap-3 rounded-md border p-4">
            <p className="text-sm font-semibold">
              What additional resources can i provide for students who need extra help?
            </p>
            <ChevronsRight className="border-muted-foreground size-7 cursor-pointer rounded-full border p-1" />
          </div>

          <div className="relative">
            <Textarea
              placeholder="Short description about the topic"
              className="h-[110px] resize-none"
            />

            <div className="absolute bottom-4 left-0 flex w-full items-center justify-end px-4">
              <Button>
                <Sparkles />
                Research
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DashboardContainer>
  );
};

export default Content;
