// response data type
type BasePlaceholder = {
  id: string;
  placeholder: string;
  title: string;
};

type ChartPlaceholder = BasePlaceholder & { type: 'chart' };
type GraphPlaceholder = BasePlaceholder & { type: 'graph' };
type DiagramPlaceholder = BasePlaceholder & { type: 'diagram' };
type ImagePlaceholder = BasePlaceholder & { type: 'image' };

type CitationPlaceholder = {
  id: string;
  type: 'citation';
  placeholder: string;
  format: 'APA' | 'MLA' | 'Chicago' | string; // Extend as needed
};

type Placeholders = {
  charts: ChartPlaceholder[];
  graphs: GraphPlaceholder[];
  diagrams: DiagramPlaceholder[];
  images: ImagePlaceholder[];
  citations: CitationPlaceholder;
};

export type ContentData = {
  content: string;
  length: number;
  format: 'text' | 'markdown' | 'html';
  language: string;
  image: boolean;
  graph: boolean;
  chart: boolean;
  diagram: boolean;
  citation: boolean;
  placeholders: Placeholders;
  totalCost: number;
};

// payload type
type CustomProperty = {
  key: string;
  value: string;
};

type OutputSettings = {
  length: 'short' | 'medium' | 'long';
  format: 'text' | 'markdown' | 'html';
  language: string;
  image: boolean;
  graph: boolean;
  chart: boolean;
  diagram: boolean;
  citation: boolean;
};

export type ContentPayload = {
  userInstructions: string;
  prompt?: string;
  fileUrls?: string[];
  urls?: string[];
  webSearch?: boolean;
  knowledgeBase?: boolean;

  category: string;
  snippetName: string;
  snippetDescription: string;

  grade?: string;
  customProperties?: CustomProperty[];
  outputSettings: OutputSettings;
};
