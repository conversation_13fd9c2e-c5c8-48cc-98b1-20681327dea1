'use client';

import { DynamicForm, type DynamicInputField } from '@ps/ui/custom/dynamic-form';
import type { Snippet } from '@ps/types';

import { useMutation, useQuery } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
import { z } from 'zod';

import {
  FormMessage,
  FormControl,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import { DashboardContainer } from '@ps/ui/layout/dashboard-container';
import { toCamelCase } from '@ps/common/utils/string';
import { Button } from '@ps/ui/components/button';
import { Switch } from '@ps/ui/components/switch';
import API from '@ps/ui/services/api';

import { ContentData } from '../utils/type';
import ChatArea from '../../components/form/chat-area';
import Content from '../components/content';
import StateHandler from '../../components/misc/state-handler';

const gradeItems = [
  { label: 'Pre-K', value: 'pre-k' },
  { label: 'Kindergarten', value: 'kindergarten' },
  { label: '1st Grade', value: '1st-grade' },
  { label: '2nd Grade', value: '2nd-grade' },
  { label: '3rd Grade', value: '3rd-grade' },
  { label: '4th Grade', value: '4th-grade' },
  { label: '5th Grade', value: '5th-grade' },
  { label: '6th Grade', value: '6th-grade' },
  { label: '7th Grade', value: '7th-grade' },
  { label: '8th Grade', value: '8th-grade' },
  { label: '9th Grade', value: '9th-grade' },
  { label: '10th Grade', value: '10th-grade' },
  { label: '11th Grade', value: '11th-grade' },
  { label: '12th Grade', value: '12th-grade' },
  { label: 'University', value: 'university' },
  { label: 'Professional Staff', value: 'professional-staff' },
  { label: 'Year 1', value: 'year-1' },
  { label: 'Year 2', value: 'year-2' },
  { label: 'Year 3', value: 'year-3' },
  { label: 'Year 4', value: 'year-4' },
  { label: 'Year 5', value: 'year-5' },
  { label: 'Year 6', value: 'year-6' },
  { label: 'Year 7', value: 'year-7' },
  { label: 'Year 8', value: 'year-8' },
];

const contentSchema = z.object({
  prompt: z.string().optional(),
  fileUrls: z.array(z.string()).optional(),
  urls: z.array(z.string()).optional(),
  webSearch: z.boolean().optional(),
  knowledgeBase: z.boolean().optional(),
  grade: z.string().optional(),
  customProperties: z
    .array(
      z.object({
        key: z.string(),
        value: z.string(),
      }),
    )
    .optional(),
});
type ContentFormData = z.infer<typeof contentSchema>;

type ContentPayload = ContentFormData & {
  category: string;
  snippetName: string;
  snippetDescription: string;
  userInstructions: string;
  outputSettings: {
    length: string;
    format: string;
    language: string;
  };
};

const Snippet = () => {
  const [generatedContent, setGeneratedContent] = useState<ContentData | null>(null);
  const snippetId = useParams().snippet as string;

  const {
    data: snippet,
    isLoading,
    error,
  } = useQuery<Snippet>({
    queryKey: ['snippets', snippetId],
    queryFn: () => API.get(`/snippets/${snippetId}?join=category`),
  });

  const form = useForm<ContentFormData>({
    resolver: zodResolver(contentSchema),
    defaultValues: {
      prompt: '',
      fileUrls: [],
      urls: [],
      webSearch: false,
      knowledgeBase: true,
      grade: '',
    },
  });

  const dynamicFormSchema = z.object(
    (snippet?.inputFields as unknown as DynamicInputField[])?.reduce(
      (acc, field) => {
        acc[field.name || toCamelCase(field.label)] = z.string();
        return acc;
      },
      {} as Record<string, z.ZodType<string>>,
    ),
  );
  type DynamicFormSchema = z.infer<typeof dynamicFormSchema>;
  const dynamicForm = useForm<DynamicFormSchema>({
    resolver: zodResolver(dynamicFormSchema),
    defaultValues: {},
  });

  const generateContent = useMutation({
    mutationFn: (data: ContentFormData) => {
      return API.post<ContentPayload, ContentData>(`/content-generation/${snippetId}`, {
        ...data,
        userInstructions: data.prompt || '',
        prompt: snippet?.promptTemplate || '',
        category: snippet?.category?.name || '',
        snippetName: snippet?.name || '',
        snippetDescription: snippet?.description || '',
        outputSettings: {
          length: 'medium',
          format: 'text',
          language: 'English',
        },
      });
    },
    onSuccess: (data) => {
      setGeneratedContent(data);
    },
    onError: (error) => {
      console.log(error);
    },
  });

  const clearGeneratedContent = () => {
    setGeneratedContent(null);
    form.reset();
  };

  const onSubmit = async (data: ContentFormData) => {
    const dynamicFormValues = dynamicForm.getValues() || {};
    data.customProperties = Object.keys(dynamicFormValues)
      .filter((key) => dynamicFormValues[key])
      .map((key) => ({
        key,
        value: dynamicForm.getValues()[key] || '',
      }));
    generateContent.mutate(data);
  };

  if (isLoading || error || !snippet) {
    return (
      <StateHandler
        loading={isLoading}
        error={error && 'Something went wrong'}
        isEmpty={!snippet}
        emptyMsg="Snippet not found"
      />
    );
  }
  return generatedContent ? (
    <Content
      generatedContent={generatedContent}
      clearGeneratedContent={clearGeneratedContent}
      snippet={snippet}
    />
  ) : (
    <DashboardContainer
      label="Planning"
      title={snippet.name}
      description={snippet.description}
      className="lg:w-2xl"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {snippet.enableChatBox && (
            <ChatArea
              isLoading={generateContent.isPending}
              form={form}
              config={{
                hideSubmitButton: true,
                enableVoiceInput: false,
                placeholder: snippet.chatboxPlaceholder,
              }}
            />
          )}

          {snippet.enableGradeField && (
            <FormField
              control={form.control}
              name="grade"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Grade Level</FormLabel>
                  <Select onValueChange={field.onChange}>
                    <FormControl className="w-full">
                      <SelectTrigger>
                        <SelectValue placeholder="Select grade level" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {gradeItems.map((item) => (
                        <SelectItem key={item.value} value={item.value}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <DynamicForm
            inputFields={(snippet?.inputFields as unknown as DynamicInputField[]) || []}
            form={dynamicForm}
          />

          <div className="flex items-start gap-3">
            <FormField
              control={form.control}
              name="knowledgeBase"
              render={({ field }) => (
                <FormItem className="inline">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="mt-[2px]"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="space-y-1">
              <h6 className="font-medium">Knowledge Base</h6>
              <p className="text-muted-foreground text-sm">
                When turned on, AI would prioritize contents from your knowledge base over contents
                available on the web.
              </p>
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={generateContent.isPending}>
            {generateContent.isPending ? (
              <Loader2 className="size-4 animate-spin" />
            ) : (
              <span>Generate</span>
            )}
          </Button>
        </form>
      </Form>
    </DashboardContainer>
  );
};

export default Snippet;
