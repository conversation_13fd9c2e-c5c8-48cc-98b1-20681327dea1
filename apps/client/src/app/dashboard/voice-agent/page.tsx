'use client';

import { Badge } from '@ps/ui/components/badge';
import { But<PERSON> } from '@ps/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ps/ui/components/card';
import { Separator } from '@ps/ui/components/separator';
import { Download, Mic, MicOff, Trash2, Volume2, VolumeX } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { ConversationHistory } from '../components/voice-agent/conversation-history';
import { ProcessingStatus } from '../components/voice-agent/processing-status';
import { useVoiceAgent } from '../components/voice-agent/use-voice-agent';

export default function VoiceAgentPage() {
  const {
    isConnected,
    isRecording,
    isProcessing,
    processingStatus,
    conversationHistory,
    error,
    startRecording,
    stopRecording,
    clearConversation,
    connect,
    disconnect,
    playAudio,
    isAudioEnabled,
    toggleAudio,
  } = useVoiceAgent();

  const [recordingTime, setRecordingTime] = useState(0);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Recording timer
  useEffect(() => {
    if (isRecording) {
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else {
      setRecordingTime(0);
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    }

    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, [isRecording]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleRecordingToggle = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  return (
    <div className="container mx-auto space-y-6 py-6">
      {/* Header */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Voice Agent</h1>
        <p className="text-muted-foreground">
          Interact with AI using voice commands. Speak naturally and get responses in real-time.
        </p>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
              <Badge variant={isConnected ? 'default' : 'destructive'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={toggleAudio} disabled={!isConnected}>
                {isAudioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>
              {isConnected ? (
                <Button variant="outline" size="sm" onClick={disconnect}>
                  Disconnect
                </Button>
              ) : (
                <Button size="sm" onClick={connect}>
                  Connect
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive text-sm">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Recording Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Voice Recording</CardTitle>
          <CardDescription>
            Click the microphone button to start recording your voice message
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Button
                size="lg"
                variant={isRecording ? 'destructive' : 'default'}
                className={`h-20 w-20 rounded-full ${
                  isRecording ? 'animate-pulse bg-red-500 hover:bg-red-600' : ''
                }`}
                onClick={handleRecordingToggle}
                disabled={!isConnected || isProcessing}
              >
                {isRecording ? <MicOff className="h-8 w-8" /> : <Mic className="h-8 w-8" />}
              </Button>
              {isRecording && (
                <div className="absolute -bottom-8 left-1/2 -translate-x-1/2 transform">
                  <Badge variant="secondary">{formatTime(recordingTime)}</Badge>
                </div>
              )}
            </div>

            <div className="text-center">
              <p className="text-muted-foreground text-sm">
                {isRecording
                  ? 'Recording... Click to stop'
                  : isProcessing
                    ? 'Processing your message...'
                    : 'Click to start recording'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Processing Status */}
      {isProcessing && <ProcessingStatus status={processingStatus} />}

      {/* Conversation History */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Conversation History</CardTitle>
            <CardDescription>Your voice conversation with the AI assistant</CardDescription>
          </div>
          {conversationHistory.length > 0 && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const data = JSON.stringify(conversationHistory, null, 2);
                  const blob = new Blob([data], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `voice-conversation-${new Date().toISOString().split('T')[0]}.json`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={clearConversation}>
                <Trash2 className="mr-2 h-4 w-4" />
                Clear
              </Button>
            </div>
          )}
        </CardHeader>
        <Separator />
        <CardContent className="pt-6">
          <ConversationHistory
            history={conversationHistory}
            onPlayAudio={playAudio}
            isAudioEnabled={isAudioEnabled}
          />
        </CardContent>
      </Card>
    </div>
  );
}
