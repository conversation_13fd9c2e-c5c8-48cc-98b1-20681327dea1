'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

import { FileUploadProvider, FileUpload } from '@ps/ui/custom/file-upload';
import {
  FormControl,
  FormMessage,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import { CardDescription, CardContent, CardHeader, CardTitle, Card } from '@ps/ui/components/card';
import { AvatarFallback, AvatarImage, Avatar } from '@ps/ui/components/avatar';
import { getInitials } from '@ps/common/utils/string';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import useAppContext from '@/providers/app-context';
import API from '@ps/ui/services/api';

const profileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  profilePicture: z.string().optional(),
});
type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfilePage() {
  const [isLoading, setIsLoading] = useState(false);
  const { user, fetchUser } = useAppContext();

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user?.name || '',
      profilePicture: user?.profilePicture || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    if (!user) return;

    setIsLoading(true);
    try {
      await API.patch<ProfileFormData, typeof user>('/me', data);
      toast.success('Profile updated successfully');
      await fetchUser();
    } catch (error) {
      toast.error('Failed to update profile');
      console.error('Profile update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Profile Settings</h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>Update your profile information and profile picture.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Avatar className="size-20">
                    <AvatarImage
                      src={form.watch('profilePicture') || user.profilePicture}
                      alt={user.name}
                    />
                    <AvatarFallback className="text-lg">{getInitials(user.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">Profile Picture</h3>
                    <p className="text-muted-foreground text-sm">
                      Upload a new profile picture or drag and drop
                    </p>
                  </div>
                </div>

                <FileUploadProvider type="image" maxSize={5}>
                  <FileUpload
                    label="Upload New Profile Picture"
                    onUpload={(urls) => form.setValue('profilePicture', urls[0])}
                    message="Drag & drop your image or browse from your storage"
                    fileSizeAndTypeMessage="Max file size: 5MB. Supported formats: PNG, JPG, GIF, WebP"
                    buttonLabel="Select Image"
                  />
                </FileUploadProvider>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    disabled={isLoading}
                    onClick={() => form.reset()}
                  >
                    Reset
                  </Button>
                  <Button className="w-40" type="submit" disabled={isLoading}>
                    {isLoading ? 'Updating...' : 'Update Profile'}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        <Card className="md:col-span-2 lg:col-span-3">
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>Your account details and status.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Email Address</p>
              <p className="text-muted-foreground text-sm">{user.email}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Account Status</p>
              <div className="flex items-center space-x-2">
                <div
                  className={`h-2 w-2 rounded-full ${user.verified ? 'bg-green-500' : 'bg-yellow-500'}`}
                />
                <p className="text-muted-foreground text-sm">
                  {user.verified ? 'Verified' : 'Pending Verification'}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Role</p>
              <p className="text-muted-foreground text-sm capitalize">{user.role}</p>
            </div>
            {user.lastLoginAt && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Last Login</p>
                <p className="text-muted-foreground text-sm">
                  {new Date(user.lastLoginAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
