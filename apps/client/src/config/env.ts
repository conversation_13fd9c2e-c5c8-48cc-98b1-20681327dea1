import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  isServer: typeof window === 'undefined',
  server: {},
  client: {
    NEXT_PUBLIC_RELEASE: z.enum(['production', 'staging', 'development']),
    NEXT_PUBLIC_API_URL: z.string().url(),
    NEXT_PUBLIC_GOOGLE_CLIENT_ID: z.string(),
  },
  runtimeEnv: {
    NEXT_PUBLIC_RELEASE: process.env.NEXT_PUBLIC_RELEASE || 'development',
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || '',
    NEXT_PUBLIC_GOOGLE_CLIENT_ID: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
  },
});
