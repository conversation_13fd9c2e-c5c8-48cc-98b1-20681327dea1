'use client';

import type { User } from '@ps/types';

import { createContext, useContext, useState } from 'react';

import API from '@ps/ui/services/api';

export interface AppContextType {
  isAuthenticated: boolean;

  user?: User;
  fetchUser: () => Promise<void>;
}

const AppContext = createContext<AppContextType>({
  isAuthenticated: false,

  fetchUser: async () => {},
});

export const AppContextProvider = ({
  children,
  value: initialValue,
}: {
  children: React.ReactNode;
  value: Omit<AppContextType, 'fetchUser'>;
}) => {
  const [user, setUser] = useState<User | undefined>(initialValue.user);

  const fetchUser = async () => {
    const user = await API.get<User>('/me');
    setUser(user);
  };

  const value = {
    ...initialValue,
    user: user || initialValue.user,
    fetchUser,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

const useAppContext = () => {
  const context = useContext(AppContext);

  if (context === undefined) {
    throw new Error('useAppContext must be used within a AppContextProvider');
  }

  return context;
};

export default useAppContext;
