'use server';

import type { AppContextType } from '.';

import { cache } from 'react';

import { auth } from '@/auth';

export const getAppContext: () => Promise<Omit<AppContextType, 'fetchUser'>> = cache(async () => {
  const session = await auth();
  const isAuthenticated = session && session.user ? true : false;
  const user = session?.user;

  return { isAuthenticated, user } satisfies Omit<AppContextType, 'fetchUser'>;
});
