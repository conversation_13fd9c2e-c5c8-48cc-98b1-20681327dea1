{"name": "@ps/client", "version": "0.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000 --experimental-https", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0 --fix", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@ps/common": "workspace:*", "@ps/ui": "workspace:*", "@radix-ui/react-visually-hidden": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.80.10", "@types/dom-mediacapture-record": "^1.0.22", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.516.0", "marked": "^16.0.0", "next": "^15.3.3", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-use": "^17.6.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tiptap-editor": "workspace:*", "zod": "^3.25.67"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/types": "workspace:*", "@ps/typescript-config": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "typescript": "^5.8.3"}}