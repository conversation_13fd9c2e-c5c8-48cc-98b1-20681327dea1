import type { Config } from 'jest';

import { pathsToModuleNameMapper } from 'ts-jest';
import path from 'path';

const config: Config = {
  verbose: true,
  moduleNameMapper: {
    ...pathsToModuleNameMapper({
      '@/*': ['<rootDir>/src/*'],
    }),
    '^@ps/common$': '<rootDir>/../../packages/common/src',
    '^@ps/common/(.*)$': '<rootDir>/../../packages/common/src/$1',
  },
  moduleFileExtensions: ['js', 'json', 'ts'],
  roots: ['<rootDir>/src'],
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  testTimeout: 30000,
  globalSetup: path.join(__dirname, 'jest.setup.ts'),
};

export default config;
