import { seedTestDatabase } from './src/test/database.seeder';

// This function will be called once before all tests are run
export default async (): Promise<void> => {
  try {
    console.log('\n\r\n\rSetting up test environment...');
    await seedTestDatabase();
    console.log('Test environment setup completed.\n\r');
  } catch (error) {
    console.error('Failed to set up test environment:', error);
    throw error;
  }
};
