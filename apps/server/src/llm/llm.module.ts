import { Module } from '@nestjs/common';
import { AiSdk } from './sdk/AiSdk';

@Module({
  providers: [
    {
      provide: AiSdk,
      useFactory: () => {
        return new AiSdk({
          provider: 'openai',
          model: 'gpt-4.1-mini',
          identifierName: 'default',
          identifierValue: 'default',
        });
      },
    },
  ],
  exports: [AiSdk],
})
export class LlmModule {}
