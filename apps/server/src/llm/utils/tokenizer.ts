import { get_encoding } from 'tiktoken';
import type { Message } from '../types.js';

const encoder = get_encoding('cl100k_base');

function getTokensCount(text: string) {
  return encoder.encode(text).length;
}

const tokensPerMessage = 3;

// ref: https://cookbook.openai.com/examples/how_to_count_tokens_with_tiktoken#6-counting-tokens-for-chat-completions-api-calls
export function estimateInputTokens(messages: Message[]) {
  let total = 0;
  messages.forEach((message) => {
    total += tokensPerMessage;
    total += getTokensCount(message.role);
    total += getTokensCount(message.content);
  });

  total += 3; // reply from AI model has prefix <|start|>assistant<|message|>
  return total;
}

export function estimateOutputTokens(text: string) {
  return getTokensCount(text);
}
