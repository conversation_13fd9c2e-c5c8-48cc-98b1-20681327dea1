import { Logger } from '@nestjs/common';
import { LLMModels, ModelInfo } from '../llm.models';
import { ProviderType } from '../sdk/types';

const logger = new Logger('CostCalculator');

/**
 * Model pricing information organized by provider and model name
 * for easy lookup in cost calculation
 */
export const ModelPricing: Record<string, Record<string, ModelInfo>> = {
  openai: LLMModels.OPENAI.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
  anthropic: LLMModels.ANTHROPIC.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
  google: LLMModels.GOOGLE.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
  perplexity: LLMModels.PERPLEXITY.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
};

/**
 * Calculate the estimated cost of a request based on tokens used
 */
export const calculateCost = (
  provider: ProviderType,
  model: string,
  inputTokens: number,
  outputTokens: number,
): number => {
  const pricing = ModelPricing[provider]?.[model];

  if (!pricing) {
    logger.warn(`No pricing information available for ${provider}/${model}`);
    return 0;
  }

  const inputCost = inputTokens * pricing.pricing.inputTokensCost;
  const outputCost = outputTokens * pricing.pricing.outputTokensCost;

  return inputCost + outputCost;
};
