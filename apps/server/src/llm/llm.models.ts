/**
 * Model capabilities enumeration
 */
export enum ModelCapability {
  TEXT = 'text',
  IMAGES = 'images',
  AUDIO = 'audio',
  VIDEO = 'video',
  CODE = 'code',
  FUNCTION_CALLING = 'function-calling',
  REASONING = 'reasoning',
}

/**
 * Detailed model information including pricing, context window, and capabilities
 */
export interface ModelInfo {
  name: string;
  pricing: {
    inputTokensCost: number; // Cost per token in USD
    outputTokensCost: number; // Cost per token in USD
  };
  contextLength?: number;
  capabilities?: ModelCapability[];
  alias?: readonly string[];
}

/**
 * All available LLM models organized by provider
 */
// Image generation models
export const OPENAI_IMAGE_MODELS = {
  DALLE_2: 'dall-e-2',
  DALLE_3: 'dall-e-3',
  GPT_IMAGE_1: 'gpt-image-1',
};

export const GOOGLE_IMAGE_MODELS = {
  GEMINI_IMAGE_GENERATION: 'gemini-2.0-flash-preview-image-generation',
  IMAGEN_3: 'imagen-3.0-generate-002',
};

export const IMAGE_MODELS = {
  ...OPENAI_IMAGE_MODELS,
  ...GOOGLE_IMAGE_MODELS,
};

export const IMAGE_SIZES = {
  SQUARE_1024: '1024x1024',
  PORTRAIT_1024_1792: '1024x1792',
  LANDSCAPE_1792_1024: '1792x1024',
  SQUARE_512: '512x512',
  SQUARE_256: '256x256',
  ASPECT_SQUARE: '1:1',
  ASPECT_CLASSIC_PORTRAIT: '3:4',
  ASPECT_CLASSIC: '4:3',
  ASPECT_PORTRAIT: '9:16',
  ASPECT_LANDSCAPE: '16:9',
};

export const IMAGE_QUALITIES = {
  STANDARD: 'standard',
  HD: 'hd',
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
};

export type ImageGenerationModel = (typeof OPENAI_IMAGE_MODELS)[keyof typeof OPENAI_IMAGE_MODELS];

export type ImageSize = (typeof IMAGE_SIZES)[keyof typeof IMAGE_SIZES];
export type ImageQuality = (typeof IMAGE_QUALITIES)[keyof typeof IMAGE_QUALITIES];

// Image model pricing information
export const IMAGE_MODEL_PRICING = {
  'gpt-image-1': {
    '1024x1024': {
      low: 0.011,
      medium: 0.042,
      high: 0.167,
    },
    '1024x1536': {
      low: 0.016,
      medium: 0.063,
      high: 0.25,
    },
    '1536x1024': {
      low: 0.016,
      medium: 0.063,
      high: 0.25,
    },
  },
  'dall-e-2': {
    '256x256': { standard: 0.016 },
    '512x512': { standard: 0.018 },
    '1024x1024': { standard: 0.02 },
  },
  'dall-e-3': {
    '1024x1024': {
      standard: 0.04,
      hd: 0.08,
    },
    '1024x1792': {
      standard: 0.08,
      hd: 0.12,
    },
    '1792x1024': {
      standard: 0.08,
      hd: 0.12,
    },
  },
};

export const LLMModels = {
  OPENAI: [
    {
      name: 'gpt-4.1-2025-04-14',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['gpt-4.1'] as const,
    },
    {
      name: 'gpt-4.1-mini-2025-04-14',
      pricing: {
        inputTokensCost: 0.4 / 1000000,
        outputTokensCost: 1.6 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['gpt-4.1-mini'] as const,
    },
    {
      name: 'gpt-4.1-nano-2025-04-14',
      pricing: {
        inputTokensCost: 0.1 / 1000000,
        outputTokensCost: 0.4 / 1000000,
      },
      contextLength: 128000,
      capabilities: [ModelCapability.TEXT, ModelCapability.CODE, ModelCapability.FUNCTION_CALLING],
      alias: ['gpt-4.1-nano'] as const,
    },
    {
      name: 'gpt-4.5-preview-2025-02-27',
      pricing: {
        inputTokensCost: 75.0 / 1000000,
        outputTokensCost: 150.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['gpt-4.5-preview'] as const,
    },
    {
      name: 'gpt-4o-2024-08-06',
      pricing: {
        inputTokensCost: 2.5 / 1000000,
        outputTokensCost: 10.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o'] as const,
    },
    {
      name: 'gpt-4o-mini-2024-07-18',
      pricing: {
        inputTokensCost: 0.15 / 1000000,
        outputTokensCost: 0.6 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o-mini'] as const,
    },
    {
      name: 'gpt-4o-audio-preview-2024-12-17',
      pricing: {
        inputTokensCost: 2.5 / 1000000,
        outputTokensCost: 10.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.AUDIO,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o-audio-preview'] as const,
    },
    {
      name: 'gpt-4o-realtime-preview-2024-12-17',
      pricing: {
        inputTokensCost: 5.0 / 1000000,
        outputTokensCost: 20.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o-realtime-preview'] as const,
    },
    {
      name: 'o3-mini',
      pricing: {
        inputTokensCost: 0.25 / 1000000,
        outputTokensCost: 1.25 / 1000000,
      },
      contextLength: 100000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.REASONING,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['o3-mini'] as const,
    },
    {
      name: 'o3',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.REASONING,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['o3'] as const,
    },
    {
      name: 'o4-mini',
      pricing: {
        inputTokensCost: 1.1 / 1000000,
        outputTokensCost: 4.4 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.REASONING,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['o4-mini'] as const,
    },
    // Image Models
    {
      name: 'gpt-image-1',
      pricing: {
        inputTokensCost: 0,
        outputTokensCost: 0,
      },
      contextLength: 0,
      capabilities: [ModelCapability.IMAGES],
      alias: ['gpt-image-1'] as const,
    },
    {
      name: 'dall-e-3',
      pricing: {
        inputTokensCost: 0.0,
        outputTokensCost: 0.0,
      },
      contextLength: 0,
      capabilities: [ModelCapability.IMAGES],
      alias: ['dall-e-3'] as const,
    },
    {
      name: 'dall-e-2',
      pricing: {
        inputTokensCost: 0.0,
        outputTokensCost: 0.0,
      },
      contextLength: 0,
      capabilities: [ModelCapability.IMAGES],
      alias: ['dall-e-2'] as const,
    },
  ],
  ANTHROPIC: [
    {
      name: 'claude-opus-4-20250514' as const,
      pricing: {
        inputTokensCost: 15.0 / 1000000,
        outputTokensCost: 75.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-opus-4'] as const,
    },
    {
      name: 'claude-sonnet-4-20250514' as const,
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-sonnet-4'] as const,
    },
    {
      name: 'claude-3-5-sonnet-20240620' as const,
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-sonnet-3.5'] as const,
    },
    {
      name: 'claude-3-5-haiku-20241022' as const,
      pricing: {
        inputTokensCost: 0.8 / 1000000,
        outputTokensCost: 4.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-haiku-3.5'] as const,
    },
    {
      name: 'claude-3-7-sonnet-20250219' as const,
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-sonnet-3.7'] as const,
    },
  ],
  GOOGLE: [
    {
      name: 'gemini-1.5-flash',
      pricing: {
        inputTokensCost: 0.35 / 1000000,
        outputTokensCost: 1.05 / 1000000,
      },
      contextLength: 1000000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gemini-flash-2'] as const,
    },
    {
      name: 'gemini-2.0-flash',
      pricing: {
        inputTokensCost: 0.1 / 1000000,
        outputTokensCost: 0.4 / 1000000,
      },
      contextLength: 1048576,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.AUDIO,
        ModelCapability.VIDEO,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gemini-2.0-flash'] as const,
    },
    {
      name: 'gemini-2.0-flash-lite',
      pricing: {
        inputTokensCost: 0.075 / 1000000,
        outputTokensCost: 0.3 / 1000000,
      },
      contextLength: 1048576,
      capabilities: [ModelCapability.TEXT],
      alias: ['gemini-2.0-flash-lite'] as const,
    },
    {
      name: 'imagen-3.0-generate-002',
      pricing: {
        inputTokensCost: 0.0,
        outputTokensCost: 0.0,
      },
      contextLength: 0,
      capabilities: [ModelCapability.IMAGES],
      alias: ['imagen-3.0-generate-002'] as const,
    },
    {
      name: 'gemini-2.0-flash-preview-image-generation',
      pricing: {
        inputTokensCost: 0.0,
        outputTokensCost: 0.0,
      },
      contextLength: 0,
      capabilities: [ModelCapability.IMAGES],
      alias: ['imagen-3.0-generate-001'] as const,
    },
  ],
  PERPLEXITY: [
    {
      name: 'sonar-deep-research',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT, ModelCapability.REASONING],
      alias: ['sonar-deep-research'] as const,
    },
    {
      name: 'sonar-reasoning-pro',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT, ModelCapability.REASONING],
      alias: ['sonar-reasoning-pro'] as const,
    },
    {
      name: 'sonar-reasoning',
      pricing: {
        inputTokensCost: 1.0 / 1000000,
        outputTokensCost: 5.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT, ModelCapability.REASONING],
      alias: ['sonar-reasoning'] as const,
    },
    {
      name: 'sonar-pro',
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT],
      alias: ['sonar-pro'] as const,
    },
    {
      name: 'sonar',
      pricing: {
        inputTokensCost: 1.0 / 1000000,
        outputTokensCost: 1.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT],
      alias: ['sonar'] as const,
    },
    {
      name: 'r1-1776',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT],
      alias: ['r1-1776'] as const,
    },
  ],
};

export type LLMModel =
  | (typeof LLMModels.OPENAI)[number]
  | (typeof LLMModels.ANTHROPIC)[number]
  | (typeof LLMModels.GOOGLE)[number]
  | (typeof LLMModels.PERPLEXITY)[number];

export const LLMProviders = ['OPENAI', 'ANTHROPIC', 'GOOGLE', 'PERPLEXITY'] as const;

export const getProviderName = (modelName: string): string => {
  for (const provider of LLMProviders) {
    const models = LLMModels[provider];
    for (const model of models) {
      if (model.name === modelName || model.alias == (modelName as any)) {
        return provider;
      }
    }
  }
  throw new Error(`No provider found for model ${modelName} not found`);
};

export const DEFAULT_MODEL = 'gpt-4.1';

/**
 * Embedding model information
 */
export interface EmbeddingModelInfo {
  name: string;
  pricing: {
    tokensCost: number; // Cost per token in USD
  };
  dimensions: number; // Embedding vector dimensions
  maxTokens: number; // Maximum tokens per input
}

/**
 * All available embedding models organized by provider
 */
export const EMBEDDING_MODELS = {
  OPENAI: [
    {
      name: 'text-embedding-3-small',
      pricing: {
        tokensCost: 0.02 / 1000000, // $0.02 per 1M tokens
      },
      dimensions: 1536,
      maxTokens: 8192,
    },
    {
      name: 'text-embedding-3-large',
      pricing: {
        tokensCost: 0.13 / 1000000, // $0.13 per 1M tokens
      },
      dimensions: 3072,
      maxTokens: 8192,
    },
  ],
};

export type EmbeddingModelName = (typeof EMBEDDING_MODELS.OPENAI)[number]['name'];
