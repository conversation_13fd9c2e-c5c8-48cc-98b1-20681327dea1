import {
  embed,
  experimental_generateImage as generateImage,
  generateObject,
  generateText,
  streamText,
  ToolSet,
} from 'ai';

import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';
import { createVertex } from '@ai-sdk/google-vertex';
import { openai } from '@ai-sdk/openai';
import { perplexity } from '@ai-sdk/perplexity';
import { z } from 'zod';

import config from '@/common/configs/config';

import { Logger } from '@nestjs/common';
import { ImageGenerationModel, ImageQuality, ImageSize } from '../llm.models';
import { calculateCost } from '../utils/costCalculator';
import { TokenUsage } from './aiSdk.types';
import { EntityId, ProviderOptions, ProviderType, UsageLogEntry } from './types';

const logger = new Logger('AiSdk');
/**
 * AI SDK Client for unified interactions with multiple LLM providers
 */
export class AiSdk {
  private options: ProviderOptions;
  private usageLogs: UsageLogEntry[] = [];

  constructor(
    options: ProviderOptions = {
      provider: 'openai',
      model: 'gpt-4o-mini',
      identifierName: 'default',
      identifierValue: 'default',
    },
  ) {
    this.validateEntityId(options);
    this.options = options;
  }

  /**
   * Validate that entity identification is provided
   */
  private validateEntityId(options: Partial<EntityId>): void {
    if (!options.identifierName || !options.identifierValue) {
      throw new Error(
        'Both identifierName and identifierValue must be provided for tracking and logging purposes',
      );
    }
  }

  /**
   * Get a configured model based on provider and model name
   */
  getProvider(options: ProviderOptions) {
    const { provider, model } = options || this.options;

    switch (provider) {
      case 'openai':
        return openai(model);
      case 'anthropic':
        return anthropic(model);
      case 'google':
        return google(model);
      case 'perplexity':
        return perplexity(model);
      default:
        logger.error(`Unsupported provider: ${provider}, using OpenAI`);
        return openai('gpt-4o');
    }
  }

  /**
   * Stream text with specified model and tools
   */
  /**
   * Stream text with messages format (supports multimodal content including audio)
   */
  async streamTextWithMessages(options: {
    messages: Array<{
      role: 'user' | 'assistant' | 'system';
      content: Array<
        { type: 'text'; text: string } | { type: 'file'; data: Buffer; mimeType: string }
      >;
    }>;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
    providerOptions?: {
      google?: {
        responseModalities?: string[];
        audioTimestamp?: boolean;
      };
    };
  }) {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    const streamParams: any = {
      model,
      messages: options.messages,
    };

    // Add provider-specific options if provided
    if (options.providerOptions) {
      streamParams.providerOptions = options.providerOptions;
    }

    try {
      const result = await streamText(streamParams);

      // Log usage after streaming completion
      result.usage
        .then((usage) => {
          if (usage) {
            const inputTokens = usage.promptTokens ?? 0;
            const outputTokens = usage.completionTokens ?? 0;
            const totalTokens = usage.totalTokens ?? 0;

            this.logUsage({
              provider: providerOptions.provider,
              model: providerOptions.model,
              identifierName: providerOptions.identifierName!,
              identifierValue: providerOptions.identifierValue!,
              operationType: 'stream-text-messages',
              usage: {
                inputTokens,
                outputTokens,
                totalTokens,
              },
              estimatedCost: calculateCost(
                providerOptions.provider,
                providerOptions.model,
                inputTokens,
                outputTokens,
              ),
            });
          }
        })
        .catch((error) => {
          logger.error(`Error logging stream usage: ${error}`);
        });

      return result;
    } catch (error) {
      logger.error(`Error in streamTextWithMessages: ${error}`);
      throw error;
    }
  }

  async streamText(options: {
    prompt: string;
    systemPrompt?: string;
    tools?: ToolSet;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
  }) {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    const result = await streamText({
      model: model,
      prompt: options.prompt,
      system: options.systemPrompt,
      tools: options.tools,
    });

    // Log usage for streaming operations when complete
    if (result.usage) {
      const usage = (await result.usage) as TokenUsage;
      const inputTokens = usage.promptTokens ?? 0;
      const outputTokens = usage.completionTokens ?? 0;
      const totalTokens = usage.totalTokens ?? 0;

      this.logUsage({
        provider: providerOptions.provider,
        model: providerOptions.model,
        identifierName: providerOptions.identifierName!,
        identifierValue: providerOptions.identifierValue!,
        operationType: 'stream-text',
        usage: {
          inputTokens,
          outputTokens,
          totalTokens,
        },
        estimatedCost: calculateCost(
          providerOptions.provider,
          providerOptions.model,
          inputTokens,
          outputTokens,
        ),
      });
    }

    return result;
  }

  /**
   * Generate text with messages format (supports multimodal content including audio)
   * This method is optimized for Gemini's audio input capabilities using file content
   */
  async generateTextWithMessages(options: {
    messages: Array<{
      role: 'user' | 'assistant' | 'system';
      content: Array<
        { type: 'text'; text: string } | { type: 'file'; data: Buffer; mimeType: string }
      >;
    }>;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
    providerOptions?: {
      google?: {
        responseModalities?: string[];
        audioTimestamp?: boolean;
      };
    };
  }) {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    const generateParams: any = {
      model,
      messages: options.messages,
    };

    // Add provider-specific options if provided
    if (options.providerOptions) {
      generateParams.providerOptions = options.providerOptions;
    }

    try {
      const result = await generateText(generateParams);

      // Log usage after text generation
      if (result.usage) {
        const usage = (await result.usage) as TokenUsage;
        const inputTokens = usage.promptTokens ?? 0;
        const outputTokens = usage.completionTokens ?? 0;
        const totalTokens = usage.totalTokens ?? 0;

        this.logUsage({
          provider: providerOptions.provider,
          model: providerOptions.model,
          identifierName: providerOptions.identifierName!,
          identifierValue: providerOptions.identifierValue!,
          operationType: 'generate-text-messages',
          usage: {
            inputTokens,
            outputTokens,
            totalTokens,
          },
          estimatedCost: calculateCost(
            providerOptions.provider,
            providerOptions.model,
            inputTokens,
            outputTokens,
          ),
        });
      }

      return result;
    } catch (error) {
      logger.error(`Error in generateTextWithMessages: ${error}`);
      throw error;
    }
  }

  /**
   * Generate text with specified model and tools
   */
  async generateText(options: {
    prompt: string;
    systemPrompt?: string;
    tools?: ToolSet | any[];
    tool_choice?: any;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
    searchMode?: string; // Added for Perplexity web search
    maxSourceCount?: number; // Added for Perplexity source control
  }) {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    // Process tools into consistent format
    const toolsOption = options.tools; // this.normalizeTools(options.tools);

    // Create the base parameters
    const generateParams: any = {
      model,
      prompt: options.prompt,
      system: options.systemPrompt,
    };

    // Only add tools if they exist and are properly formatted
    if (toolsOption) {
      generateParams.tools = toolsOption;
    }

    // Add tool_choice if provided
    if (options.tool_choice) {
      generateParams.toolChoice = options.tool_choice;
    }

    // Add Perplexity-specific options
    if (providerOptions.provider === 'perplexity') {
      // Add searchMode parameter for web search
      if (options.searchMode) {
        generateParams.search_mode = options.searchMode;
      }

      // Add max source count for limiting sources
      if (options.maxSourceCount) {
        generateParams.max_source_count = options.maxSourceCount;
      }
    }

    try {
      const result = await generateText(generateParams);

      // Log usage after text generation
      if (result.usage) {
        const usage = (await result.usage) as TokenUsage;
        const inputTokens = usage.promptTokens ?? 0;
        const outputTokens = usage.completionTokens ?? 0;
        const totalTokens = usage.totalTokens ?? 0;

        this.logUsage({
          provider: providerOptions.provider,
          model: providerOptions.model,
          identifierName: providerOptions.identifierName!,
          identifierValue: providerOptions.identifierValue!,
          operationType: 'generate-text',
          usage: {
            inputTokens,
            outputTokens,
            totalTokens,
          },
          estimatedCost: calculateCost(
            providerOptions.provider,
            providerOptions.model,
            inputTokens,
            outputTokens,
          ),
        });
      }

      return result;
    } catch (error) {
      logger.error(`Error in generateText: ${error}`);
      throw error;
    }
  }

  /**
   * Generate structured data using the AI SDK's generateObject function
   */
  async generateObject<T = any>(options: {
    prompt: string;
    systemPrompt?: string;
    schema: z.ZodType<T>;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
  }): Promise<{ object: T }> {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    try {
      const result = await generateObject({
        model: model,
        prompt: options.prompt,
        system: options.systemPrompt,
        schema: options.schema,
      });

      // Log usage
      if (result.usage) {
        const usage = (await result.usage) as TokenUsage;
        const inputTokens = usage.promptTokens ?? 0;
        const outputTokens = usage.completionTokens ?? 0;
        const totalTokens = usage.totalTokens ?? 0;

        this.logUsage({
          provider: providerOptions.provider,
          model: providerOptions.model,
          identifierName: providerOptions.identifierName!,
          identifierValue: providerOptions.identifierValue!,
          operationType: 'generate-object',
          usage: {
            inputTokens,
            outputTokens,
            totalTokens,
          },
          estimatedCost: calculateCost(
            providerOptions.provider,
            providerOptions.model,
            inputTokens,
            outputTokens,
          ),
        });
      }

      return result;
    } catch (error) {
      logger.error(`Error in generateObject: ${error}`);
      throw error;
    }
  }

  async embedText(options: {
    text: string;
    model?: string;
    provider?: ProviderType;
    identifierName?: string;
    identifierValue?: string;
  }): Promise<{ embedding: number[]; usage: { totalTokens: number } }> {
    const providerOptions = {
      provider: options.provider || 'openai',
      model: options.model || 'text-embedding-3-small',
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);

    try {
      // Currently only OpenAI embeddings are supported
      if (providerOptions.provider !== 'openai') {
        throw new Error(`Embedding not implemented for provider: ${providerOptions.provider}`);
      }

      const embeddingModel = openai.embedding(providerOptions.model);

      const result = await embed({
        model: embeddingModel,
        value: options.text,
      });

      // Log usage for embedding operations
      if (result.usage) {
        const usage = result.usage;
        const totalTokens = usage.tokens;

        this.logUsage({
          provider: providerOptions.provider,
          model: providerOptions.model,
          identifierName: providerOptions.identifierName!,
          identifierValue: providerOptions.identifierValue!,
          operationType: 'embed-text',
          usage: {
            inputTokens: totalTokens,
            outputTokens: 0,
            totalTokens,
          },
          estimatedCost: calculateCost(
            providerOptions.provider,
            providerOptions.model,
            totalTokens,
            0,
          ),
        });
      }

      return {
        embedding: result.embedding,
        usage: {
          totalTokens: result.usage?.tokens ?? 0,
        },
      };
    } catch (error) {
      logger.error(`Error in embedText: ${error}`);
      throw error;
    }
  }

  /**
   * Log usage data for analytics and billing purposes
   */
  private logUsage(data: Omit<UsageLogEntry, 'timestamp'>): void {
    const logEntry: UsageLogEntry = {
      ...data,
      timestamp: new Date(),
    };

    // Add to in-memory log
    this.usageLogs.push(logEntry);

    // Log to console
    logger.debug(
      `LLM Usage: ${logEntry.identifierName}:${logEntry.identifierValue} | ${logEntry.operationType} | ${logEntry.provider}/${logEntry.model} | Tokens: ${logEntry.usage.totalTokens} | Cost: ${logEntry.estimatedCost}`,
    );
  }

  /**
   * Get all logged usage data
   */
  getUsageLogs(): UsageLogEntry[] {
    return [...this.usageLogs];
  }

  async generateImage(options: {
    prompt: string;
    provider: ProviderType;
    model: ImageGenerationModel;
    quality?: ImageQuality;
    style?: string;
    size?: ImageSize;
    n?: number;
    identifierName?: string;
    identifierValue?: string;
  }): Promise<{ images: string[] }> {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };
    this.validateEntityId(providerOptions);

    let images: string[] = [];
    let estimatedCost: number;

    try {
      if (providerOptions.provider === 'openai') {
        const result = await this.generateImageWithOpenAI(options);
        images = result.images;
        estimatedCost = result.estimatedCost;
      } else if (providerOptions.provider === 'google') {
        const result = await this.generateImageWithGoogle(options);
        images = result.images;
        estimatedCost = result.estimatedCost;
      } else {
        throw new Error(
          `Image generation not implemented for provider: ${providerOptions.provider}`,
        );
      }

      if (!images.length) {
        throw new Error('No images were generated');
      }

      // Log usage for Google image generation
      this.logUsage({
        provider: providerOptions.provider,
        model: options.model,
        identifierName: providerOptions.identifierName!,
        identifierValue: providerOptions.identifierValue!,
        operationType: 'generate-image',
        usage: {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
        },
        estimatedCost,
      });

      return { images };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(
        `Error generating image with ${providerOptions.provider}/${providerOptions.model}: ${errorMessage}`,
      );
      throw error;
    }
  }

  private async generateImageWithOpenAI(options: {
    prompt: string;
    model: ImageGenerationModel;
    quality?: ImageQuality;
    style?: string;
    size?: ImageSize;
    n?: number;
  }): Promise<{ images: string[]; estimatedCost: number }> {
    const { size = '1024x1024', style, model, n = 1 } = options;

    const providerOptions: Record<'openai', Record<string, string>> = {
      openai: {},
    };

    let { quality = 'standard' } = options;
    if (model === 'gpt-image-1') {
      const GPTImage1QualityMap = {
        standard: 'low',
        hd: 'medium',
      };
      quality = GPTImage1QualityMap[quality] || quality;
      providerOptions.openai.quality = quality;
    } else if (model === 'dall-e-3') {
      providerOptions.openai.quality = quality;
    }

    if (style) providerOptions.openai.style = style;

    const sdkModel = openai.image(model);
    const batchSize = sdkModel.modelId === 'dall-e-3' ? 1 : Math.min(n, 10); // DALL-E 3 can only generate 1 image at a time, DALL-E 2 up to 10
    const batches = Math.ceil(n / batchSize); // For multiple images, we'll need to make multiple calls
    const images: string[] = [];

    for (let i = 0; i < batches; i++) {
      const currentBatchSize = Math.min(batchSize, n - i * batchSize);

      const result = await generateImage({
        model: sdkModel,
        prompt: options.prompt,
        size: size as `${number}x${number}`,
        providerOptions,
      });

      if (currentBatchSize === 1) {
        images.push(result.image.base64);
      } else if (result.images) {
        result.images.forEach((img) => images.push(img.base64));
      }
    }

    return {
      images,
      estimatedCost: this.calculateImageCost('openai', model, size, quality, n),
    };
  }

  private async generateImageWithGoogle(options: {
    prompt: string;
    model: ImageGenerationModel;
    size?: ImageSize;
    n?: number;
  }): Promise<{ images: string[]; estimatedCost: number }> {
    const { size = '1:1', model, n = 1 } = options;

    const images: string[] = [];
    if (model === 'imagen-3.0-generate-002') {
      const vertex = createVertex({
        googleAuthOptions: {
          credentials: {
            client_email: config().google.vertex?.clientEmail || '',
            private_key: config().google.vertex?.privateKey || '',
          },
        },
      });
      const sdkModel = vertex.image(model);

      const result = await generateImage({
        model: sdkModel,
        prompt: options.prompt,
        aspectRatio: size as `${number}:${number}`,
        n,
      });

      if (n > 1 && result.images?.length) {
        result.images.forEach((img) => images.push(img.base64));
      } else if (result.image) {
        images.push(result.image.base64);
      }
    } else if (model === 'gemini-2.0-flash-preview-image-generation') {
      const sdkModel = google(model);
      const result = await generateText({
        model: sdkModel,
        prompt: options.prompt,
        providerOptions: {
          google: { responseModalities: ['TEXT', 'IMAGE'] },
        },
      });

      if (result.files?.length) {
        result.files.forEach((img) => images.push(img.base64));
      }
    } else {
      throw new Error(`Image generation not implemented for model: ${model}`);
    }

    return {
      images,
      estimatedCost: this.calculateImageCost('google', model, size, 'standard', n),
    };
  }

  private calculateImageCost(
    provider: ProviderType,
    model: string,
    size: string,
    quality?: string,
    n = 1,
  ): number {
    let cost = 0;

    if (provider === 'openai') {
      // Pricing based on OpenAI's pricing structure
      switch (model) {
        case 'dall-e-2':
          // DALL-E 2 pricing
          if (size === '256x256') cost = 0.016;
          else if (size === '512x512') cost = 0.018;
          else if (size === '1024x1024') cost = 0.02;
          break;
        case 'dall-e-3':
          // DALL-E 3 pricing
          if (size === '1024x1024') {
            cost = quality === 'hd' ? 0.08 : 0.04;
          } else if (size === '1024x1792' || size === '1792x1024') {
            cost = quality === 'hd' ? 0.12 : 0.08;
          }
          break;
        case 'gpt-image-1':
          // GPT Image 1 pricing
          if (size === '1024x1024') {
            if (quality === 'low') cost = 0.011;
            else if (quality === 'medium') cost = 0.042;
            else if (quality === 'high') cost = 0.167;
          } else if (size === '1024x1536' || size === '1536x1024') {
            if (quality === 'low') cost = 0.016;
            else if (quality === 'medium') cost = 0.063;
            else if (quality === 'high') cost = 0.25;
          }
          break;
      }
    } else if (provider === 'google') {
      // Google Gemini and Imagen pricing (based on documentation)
      switch (model) {
        case 'gemini-2.0-flash-preview-image-generation':
          // Free for now at limited preview (as per Google's documentation)
          cost = 0;
          break;
        case 'imagen-3.0-generate-002':
          cost = 0.04;
          break;
        case 'imagen-3':
          // Legacy Imagen pricing
          cost = 0.04;
          break;
      }
    }

    // Multiply by the number of images
    return cost * n;
  }
}

export * from './types';
