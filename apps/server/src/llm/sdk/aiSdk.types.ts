/**
 * Type definitions for the ai library
 * Used to augment the existing types for better compatibility
 */

/**
 * Token usage for language models
 * This matches the structure from OpenAI, Anthropic, and Google
 */
export interface TokenUsage {
  /**
   * Number of tokens in the prompt/input
   */
  prompt?: number;

  /**
   * Number of tokens in the completion/output
   */
  completion?: number;

  /**
   * Total tokens used
   */
  totalTokens?: number;

  /**
   * Alternative field name for prompt tokens
   */
  promptTokens?: number;

  /**
   * Alternative field name for completion tokens
   */
  completionTokens?: number;
}

/**
 * Source information returned by Perplexity
 */
export interface PerplexitySource {
  /**
   * Title of the source
   */
  title?: string;

  /**
   * URL of the source
   */
  url?: string;
}

/**
 * Extended response interface with Perplexity-specific fields
 */
export interface PerplexityExtendedResult {
  /**
   * The generated text
   */
  text: string;

  /**
   * Sources from Perplexity web search
   */
  sources?: PerplexitySource[];

  /**
   * Tool calls if any
   */
  toolCalls?: any[];

  /**
   * Usage information
   */
  usage?: TokenUsage;
}
