import { GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable, Logger } from '@nestjs/common';

import config from '@/common/configs/config';
import { sanitizeFilename } from '@/common/utils/file.utils';

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly bucket: string;

  constructor() {
    const awsConfig = config().aws;

    if (!awsConfig) {
      throw new Error('AWS S3 configuration is missing');
    }

    this.s3Client = new S3Client({
      region: awsConfig.region,
      credentials: {
        accessKeyId: awsConfig.accessKeyId,
        secretAccessKey: awsConfig.secretAccessKey,
      },
    });

    this.bucket = awsConfig.buckets.main;
  }

  async uploadFile(
    key: string,
    buffer: Buffer,
    contentType: string,
    metadata?: Record<string, string>,
  ): Promise<string> {
    try {
      this.logger.log(`Uploading file to S3: ${key}`);

      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        Metadata: metadata,
        ACL: 'public-read',
      });

      await this.s3Client.send(command);

      // Properly encode the URL to handle special characters in the path/filename
      const encodedKey = encodeURIComponent(key).replace(/%2F/g, '/'); // Keep forward slashes for path structure
      const fileUrl = `https://${this.bucket}.s3.${config().aws?.region}.amazonaws.com/${encodedKey}`;
      this.logger.log(`File uploaded successfully: ${fileUrl}`);

      return fileUrl;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to upload file to S3: ${errorMessage}`);
      throw new Error(`S3 upload failed: ${errorMessage}`);
    }
  }

  async uploadBase64File(
    key: string,
    base64Data: string,
    contentType: string,
    metadata?: Record<string, string>,
  ): Promise<string> {
    try {
      // Remove data URL prefix while preserving optional filename parameter
      // Handle both: data:type;base64,content and data:type;name=filename;base64,content
      const base64Content = base64Data.replace(/^data:[^;]+(?:;[^;]*)*;base64,/, '');

      // Convert base64 to buffer
      const buffer = Buffer.from(base64Content, 'base64');

      return this.uploadFile(key, buffer, contentType, metadata);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to upload base64 file to S3: ${errorMessage}`);
      throw new Error(`S3 base64 upload failed: ${errorMessage}`);
    }
  }

  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to generate signed URL: ${errorMessage}`);
      throw new Error(`Signed URL generation failed: ${errorMessage}`);
    }
  }

  generateKey(filename: string, knowledgeBaseId: string, type: string): string {
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const sanitizedFilename = sanitizeFilename(filename);

    // Ensure filename has proper extension
    const finalFilename = this.ensureFileExtension(sanitizedFilename, type);

    return `knowledge-base/${timestamp}/${knowledgeBaseId}/${type}/${finalFilename}`;
  }

  // Add a method for content generation visual elements
  generateContentKey(filename: string, type: string): string {
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const sanitizedFilename = sanitizeFilename(filename);
    const finalFilename = this.ensureFileExtension(sanitizedFilename, 'png');

    return `content-generation/${timestamp}/${type}/${finalFilename}`;
  }

  private ensureFileExtension(filename: string, type: string): string {
    // Check if filename already has an extension
    const hasExtension = /\.[a-zA-Z0-9]+$/.test(filename);

    if (hasExtension) {
      return filename;
    }

    // Add appropriate extension based on type
    const extensionMap: Record<string, string> = {
      pdf: '.pdf',
      doc: '.docx',
      image: '.jpg',
      video: '.mp4',
      audio: '.mp3',
      png: '.png',
    };

    const extension = extensionMap[type] || '.bin';
    return `${filename}${extension}`;
  }

  async verifyFileAccess(key: string): Promise<boolean> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`File access verified for key: ${key}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`File access verification failed for key: ${key}: ${errorMessage}`);
      return false;
    }
  }
}
