import { Injectable, Logger } from '@nestjs/common';
import { load } from 'cheerio';
import axios from 'axios';

type UrlMeta = {
  image: string;
  title: string;
  description: string;
  duration: string;
};

const isTikTokUrl = (url: string) => {
  return /^(?:https?:\/\/)?(?:www\.)?(?:tiktok\.com\/)(?:@[^/]+\/video\/|v\/)([0-9]+)/.test(url);
};

@Injectable()
export class UrlService {
  private readonly logger = new Logger(UrlService.name);

  async getUrlMeta(url: string): Promise<UrlMeta> {
    if (isTikTokUrl(url)) {
      return await this.getTikTokMeta(url);
    } else {
      const meta = await this.getAnyUrlMeta(url);
      if (meta.title === ' - YouTube') {
        meta.title = '';
      }
      return meta;
    }
  }

  private async getAnyUrlMeta(url: string): Promise<UrlMeta> {
    let image = '',
      title = '',
      description = '',
      duration = '';

    try {
      const response = await axios.get<string>(url, { timeout: 3000 });
      const html = response.data;

      const $ = load(html);

      const titleOGMeta = $('meta[property="og:title"]').first().attr('content');
      const titleTag = $('title').first().text();
      title = titleOGMeta || titleTag || '';

      const descriptionOGMeta = $('meta[property="og:description"]').first().attr('content');
      const descriptionMeta = $('meta[name="description"]').first().attr('content');
      description = descriptionOGMeta || descriptionMeta || '';

      const imageOGMeta = $('meta[property="og:image"]').first().attr('content');
      image = imageOGMeta || '';

      const durationMeta = $('meta[itemprop="duration"]').first().attr('content');
      if (durationMeta) {
        duration = durationMeta || '';
      } else {
        try {
          const jsonLDString = $('script[type="application/ld+json"]').first().text();
          if (jsonLDString) {
            const jsonLD = JSON.parse(jsonLDString) as
              | { duration?: string }
              | { duration?: string }[];
            duration = ((jsonLD as { duration?: string })?.duration ||
              (jsonLD as { duration?: string }[])?.[0]?.duration) as string;
          }
        } catch (e) {
          this.logger.log('Error with fetching/parsing JSON LD:', e);
        }
      }
    } catch (error) {
      this.logger.log('Error Fetching URL Meta:', error);
    }

    return { image, title, description, duration };
  }

  private async getTikTokMeta(url: string): Promise<UrlMeta> {
    type TikTokMeta = { title: string; thumbnail_url: string; author_name: string };
    try {
      const endPoint = 'https://www.tiktok.com/oembed';
      const response = await axios.get<TikTokMeta>(`${endPoint}?url=${url}`).then((r) => r.data);

      return {
        title: `Video by ${response.author_name}`,
        image: response.thumbnail_url,
        description: response.title,
        duration: '',
      };
    } catch (e) {
      this.logger.log('Error Fetching TikTok Meta:', e);
    }

    return { image: '', title: '', description: '', duration: '' };
  }
}
