import type { Config } from './config.types';

import getAppConfig from '@ps/common/constants/app';

const getEnv = () => (process.env.NODE_ENV as Config['environment']) || 'development';

const getRedisConfig = () => {
  let redisUrl = process.env.REDIS_URL;

  if (!redisUrl) {
    throw new Error('REDIS_URL environment variable is missing.');
  }

  if (!redisUrl.includes('@')) {
    redisUrl = redisUrl.replace('redis://', 'redis://:@');
  }
  const redisUrlSplit = redisUrl.split(':');

  return {
    url: redisUrl,
    host: redisUrlSplit[2]?.split('@')[1] ?? 'localhost',
    port: parseInt(redisUrlSplit[3] || '6379', 10),
    username: redisUrlSplit[1]?.replace('//', '') || undefined,
    password: redisUrlSplit[2]?.split('@')[0] || undefined,
  };
};

const app = getAppConfig(getEnv());
const config: Config = {
  app,
  nest: {
    https: app.https,
    port: parseInt(process.env.PORT || '3333', 10) || 3333,
    cors: {
      enabled: true,
    },
  },

  // Environment
  environment: getEnv() || 'development',
  isStaging: getEnv() === 'staging',
  isProd: getEnv() === 'production',
  isTest: getEnv() === 'test',
  isDev: getEnv() === 'development',

  // Data Sources
  mongo: {
    url: process.env.MONGO_URL || 'mongodb://localhost:27017/base',
  },
  redis: getRedisConfig(),

  // Tools
  swagger: {
    enabled: true,
    favIcon: `${app.client.url}/favicon.ico`,
    docs: [
      {
        title: `${app.name} APIs`,
        description: 'API Documentation',
        version: '1.0',
        path: 'docs',
        pageTitle: `${app.name} API`,
        username: process.env.OPENAPI_USERNAME || '',
        password: process.env.OPENAPI_PASSWORD || '',
      },
    ],
  },
  bullBoard: {
    username: process.env.BULL_BOARD_USERNAME || '',
    password: process.env.BULL_BOARD_PASSWORD || '',
  },

  // Integrations
  aws: {
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    region: process.env.AWS_REGION || '',
    buckets: {
      images: process.env.AWS_BUCKET_NAME_IMAGES || '',
      main: process.env.AWS_BUCKET_NAME || '',
    },
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    apiKey: process.env.GEMINI_API_KEY || '',
    vertex: {
      clientEmail: process.env.GOOGLE_VERTEX_CLIENT_EMAIL || '',
      privateKey: process.env.GOOGLE_VERTEX_PRIVATE_KEY || '',
    },
  },
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY || '',
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  },
  slack: { botToken: process.env.SLACK_BOT_TOKEN || '' },
  mailchimp: { apiKey: process.env.MAILCHIMP_API_KEY || '' },
  turbopuffer: { apiKey: process.env.TURBOPUFFER_API_KEY || '' },
  blogifyApi: {
    apiKey: process.env.BLOGIFY_API_KEY || '',
    textExtraction: {
      url: `${process.env.BLOGIFY_HOST}/text-extractor`,
    },
  },

  // Security
  jwt: {
    secret: process.env.JWT_SECRET || '',
    expiresIn: '14d',
    refreshIn: '30d',
    bcryptSaltOrRound: 10,
  },
  whitelistedDomains: ['@blogify.ai', '@vlogify.ai'],
  blockedIPAddresses: (process.env.BLOCKED_IP_ADDRESSES || '').split(','),
  blockedCountries: (process.env.BLOCKED_COUNTRIES || '').split(','),
  blockedDomains: [
    '@bikerider.com',
    '@counsellor.com',
    '@gardener.com',
    '@engineer.com',
    '@doglover.com',
    '@saxpads.xyz',
    '@artlover.com',
    '@comic.com',
    '@hilarious.com',
    '@minister.com',
    '@politician.com',
    '@mail.com',
    '@gmx.com',
  ],
};

export default (): Config => config;
