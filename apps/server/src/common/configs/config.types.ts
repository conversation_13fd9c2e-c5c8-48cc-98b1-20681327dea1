import type { AppInfo } from '@ps/common/constants/app';

export interface Config {
  app: AppInfo;
  nest: {
    https: boolean;
    port: number;
    cors: {
      enabled: boolean;
    };
  };

  // Environment
  environment: 'production' | 'staging' | 'development' | 'test';
  isProd: boolean;
  isStaging: boolean;
  isTest: boolean;
  isDev: boolean;

  // Data Sources
  mongo: {
    url: string;
  };
  redis: {
    url: string;
    host: string;
    port: number;
    username?: string;
    password?: string;
  };

  // Tools
  swagger: SwaggerConfig;
  bullBoard: {
    username: string;
    password: string;
  };

  // Integrations
  aws: {
    secretAccessKey: string;
    accessKeyId: string;
    region: string;
    buckets: {
      images: string;
      main: string;
    };
  };
  google: Integration;
  stripe: {
    secretKey: string;
    publishableKey: string;
    webhookSecret: string;
  };
  slack: { botToken: string };
  mailchimp: { apiKey: string };
  turbopuffer?: {
    apiKey: string;
  };
  blogifyApi?: {
    apiKey?: string;
    textExtraction: {
      url: string;
    };
  };

  // Security
  jwt: {
    secret: string;
    expiresIn: string;
    refreshIn: string;
    bcryptSaltOrRound: number;
  };
  whitelistedDomains: string[];
  blockedIPAddresses: string[];
  blockedCountries: string[];
  blockedDomains: string[];
}

interface Integration {
  clientId: string;
  clientSecret: string;
  apiKey?: string;
  vertex?: {
    clientEmail: string;
    privateKey: string;
  };
}

interface SwaggerConfig {
  enabled: boolean;
  favIcon: string;
  docs: {
    title: string;
    description: string;
    version: string;
    path: string;
    pageTitle: string;
    pathNameFilter?: string;
    username: string;
    password: string;
  }[];
}
