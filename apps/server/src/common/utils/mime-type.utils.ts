import { FileType } from './file.utils';

/**
 * Get MIME type from FileType enum
 * @param fileType - FileType enum value
 * @returns MIME type string
 */
export function getMimeType(fileType: FileType): string {
  switch (fileType) {
    case FileType.VIDEO:
      return 'video/mp4';
    case FileType.AUDIO:
      return 'audio/mpeg';
    case FileType.IMAGE:
      return 'image/jpeg';
    case FileType.DOC:
      return 'application/msword';
    case FileType.PDF:
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
}

/**
 * Get MIME type from file extension
 * @param extension - File extension (without dot)
 * @returns MIME type string
 */
export function getMimeTypeFromExtension(extension: string): string {
  const ext = extension.toLowerCase();

  // Video types
  const videoMimeTypes: Record<string, string> = {
    mp4: 'video/mp4',
    avi: 'video/x-msvideo',
    mov: 'video/quicktime',
    wmv: 'video/x-ms-wmv',
    flv: 'video/x-flv',
    webm: 'video/webm',
    mkv: 'video/x-matroska',
    m4v: 'video/x-m4v',
  };

  // Audio types
  const audioMimeTypes: Record<string, string> = {
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    flac: 'audio/flac',
    aac: 'audio/aac',
    ogg: 'audio/ogg',
    wma: 'audio/x-ms-wma',
    m4a: 'audio/mp4',
    opus: 'audio/opus',
  };

  // Image types
  const imageMimeTypes: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    bmp: 'image/bmp',
    svg: 'image/svg+xml',
    webp: 'image/webp',
    tiff: 'image/tiff',
    ico: 'image/x-icon',
  };

  // Document types
  const docMimeTypes: Record<string, string> = {
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    txt: 'text/plain',
    rtf: 'application/rtf',
    odt: 'application/vnd.oasis.opendocument.text',
    md: 'text/markdown',
    csv: 'text/csv',
    pdf: 'application/pdf',
  };

  return (
    videoMimeTypes[ext] ||
    audioMimeTypes[ext] ||
    imageMimeTypes[ext] ||
    docMimeTypes[ext] ||
    'application/octet-stream'
  );
}

/**
 * Get file extension from MIME type
 * @param mimeType - MIME type string
 * @returns File extension (without dot)
 */
export function getExtensionFromMimeType(mimeType: string): string {
  const mimeToExtension: Record<string, string> = {
    // Video
    'video/mp4': 'mp4',
    'video/x-msvideo': 'avi',
    'video/quicktime': 'mov',
    'video/x-ms-wmv': 'wmv',
    'video/x-flv': 'flv',
    'video/webm': 'webm',
    'video/x-matroska': 'mkv',

    // Audio
    'audio/mpeg': 'mp3',
    'audio/wav': 'wav',
    'audio/flac': 'flac',
    'audio/aac': 'aac',
    'audio/ogg': 'ogg',
    'audio/x-ms-wma': 'wma',
    'audio/mp4': 'm4a',
    'audio/opus': 'opus',

    // Image
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/bmp': 'bmp',
    'image/svg+xml': 'svg',
    'image/webp': 'webp',
    'image/tiff': 'tiff',
    'image/x-icon': 'ico',

    // Document
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'text/plain': 'txt',
    'application/rtf': 'rtf',
    'application/vnd.oasis.opendocument.text': 'odt',
    'text/markdown': 'md',
    'text/csv': 'csv',
    'application/pdf': 'pdf',
  };

  return mimeToExtension[mimeType] || '';
}

/**
 * Check if MIME type is supported for processing
 * @param mimeType - MIME type to check
 * @returns True if MIME type is supported
 */
export function isSupportedMimeType(mimeType: string): boolean {
  const supportedTypes = [
    // Video
    'video/mp4',
    'video/x-msvideo',
    'video/quicktime',
    'video/x-ms-wmv',
    'video/x-flv',
    'video/webm',
    'video/x-matroska',

    // Audio
    'audio/mpeg',
    'audio/wav',
    'audio/flac',
    'audio/aac',
    'audio/ogg',
    'audio/x-ms-wma',
    'audio/mp4',
    'audio/opus',

    // Image
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/svg+xml',
    'image/webp',
    'image/tiff',

    // Document
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/rtf',
    'application/vnd.oasis.opendocument.text',
    'text/markdown',
    'text/csv',
    'application/pdf',
  ];

  return supportedTypes.includes(mimeType);
}
