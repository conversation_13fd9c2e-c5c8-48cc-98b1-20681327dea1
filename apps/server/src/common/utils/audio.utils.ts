/**
 * Audio processing utilities for handling WAV files and PCM data
 */

export interface AudioChunkInfo {
  buffer: Buffer;
  mimeType: string;
  sampleRate?: number;
  channels?: number;
  bitDepth?: number;
}

export class AudioUtils {
  /**
   * Merge multiple audio chunks into a single buffer with proper WAV header handling
   * This replaces simple Buffer.concat() to prevent choppy audio
   */
  static mergeAudioChunks(chunks: AudioChunkInfo[]): Buffer {
    if (chunks.length === 0) {
      throw new Error('No audio chunks to merge');
    }

    if (chunks.length === 1) {
      return chunks[0]?.buffer || Buffer.from([]);
    }

    // Check if all chunks have the same format
    const firstChunk = chunks[0];
    const mimeType = firstChunk?.mimeType || '';

    // For PCM audio, we can directly concatenate the raw audio data
    if (mimeType.includes('pcm')) {
      return this.mergePCMChunks(chunks);
    }

    // For WAV files, we need to merge properly with header handling
    if (mimeType.includes('wav')) {
      return this.mergeWAVChunks(chunks);
    }

    // For other formats (webm, mp3, etc.), fallback to simple concatenation
    // Note: This may not work perfectly for all formats, but it's better than nothing
    console.warn(`Unsupported audio format for proper merging: ${mimeType}, using fallback`);
    return Buffer.concat(chunks.map((chunk) => chunk.buffer));
  }

  /**
   * Merge PCM audio chunks (raw audio data)
   */
  private static mergePCMChunks(chunks: AudioChunkInfo[]): Buffer {
    // PCM data can be directly concatenated since it's raw audio samples
    const buffers = chunks.map((chunk) => chunk.buffer);
    return Buffer.concat(buffers);
  }

  /**
   * Merge WAV audio chunks with proper header handling
   */
  private static mergeWAVChunks(chunks: AudioChunkInfo[]): Buffer {
    try {
      // Extract audio data from each WAV file (skip headers)
      const audioDataChunks: Buffer[] = [];
      let totalAudioLength = 0;
      let wavHeader: Buffer | null = null;

      for (const chunk of chunks) {
        const wavData = this.parseWAVFile(chunk.buffer);

        if (!wavHeader) {
          wavHeader = wavData.header;
        }

        audioDataChunks.push(wavData.audioData);
        totalAudioLength += wavData.audioData.length;
      }

      if (!wavHeader) {
        throw new Error('No valid WAV header found');
      }

      // Create new WAV file with merged audio data
      return this.createWAVFile(wavHeader, Buffer.concat(audioDataChunks), totalAudioLength);
    } catch (error) {
      console.error('Error merging WAV chunks, falling back to simple concatenation:', error);
      return Buffer.concat(chunks.map((chunk) => chunk.buffer));
    }
  }

  /**
   * Parse a WAV file to extract header and audio data
   */
  private static parseWAVFile(buffer: Buffer): { header: Buffer; audioData: Buffer } {
    if (buffer.length < 44) {
      throw new Error('Buffer too small to be a valid WAV file');
    }

    // Check WAV signature
    if (buffer.toString('ascii', 0, 4) !== 'RIFF') {
      throw new Error('Not a valid WAV file (missing RIFF header)');
    }

    if (buffer.toString('ascii', 8, 12) !== 'WAVE') {
      throw new Error('Not a valid WAV file (missing WAVE identifier)');
    }

    // Find the data chunk
    let offset = 12;
    let dataOffset = -1;
    let dataSize = 0;

    while (offset < buffer.length - 8) {
      const chunkId = buffer.toString('ascii', offset, offset + 4);
      const chunkSize = buffer.readUInt32LE(offset + 4);

      if (chunkId === 'data') {
        dataOffset = offset + 8;
        dataSize = chunkSize;
        break;
      }

      offset += 8 + chunkSize;
    }

    if (dataOffset === -1) {
      throw new Error('No data chunk found in WAV file');
    }

    const header = buffer.subarray(0, dataOffset);
    const audioData = buffer.subarray(dataOffset, dataOffset + dataSize);

    return { header, audioData };
  }

  /**
   * Create a new WAV file with given header template and audio data
   */
  private static createWAVFile(
    headerTemplate: Buffer,
    audioData: Buffer,
    audioDataLength: number,
  ): Buffer {
    // Clone the header template
    const header = Buffer.from(headerTemplate);

    // Update the file size in the RIFF header (total file size - 8 bytes)
    const totalFileSize = header.length + audioDataLength - 8;
    header.writeUInt32LE(totalFileSize, 4);

    // Update the data chunk size (find and update the data chunk size)
    let offset = 12;
    while (offset < header.length - 8) {
      const chunkId = header.toString('ascii', offset, offset + 4);
      if (chunkId === 'data') {
        header.writeUInt32LE(audioDataLength, offset + 4);
        break;
      }
      const chunkSize = header.readUInt32LE(offset + 4);
      offset += 8 + chunkSize;
    }

    return Buffer.concat([header, audioData]);
  }

  /**
   * Convert audio buffer to standardized format for processing
   */
  static standardizeAudioChunk(buffer: Buffer, mimeType: string): AudioChunkInfo {
    // Extract audio parameters from mime type
    const sampleRateMatch = mimeType.match(/rate=(\d+)/);
    const sampleRate = sampleRateMatch ? parseInt(sampleRateMatch[1]!) : 16000;

    return {
      buffer,
      mimeType,
      sampleRate,
      channels: 1, // Assume mono for voice
      bitDepth: 16, // Assume 16-bit
    };
  }

  /**
   * Optimize audio chunks for better streaming
   * Removes silence and normalizes volume
   */
  static optimizeAudioChunk(chunk: AudioChunkInfo): AudioChunkInfo {
    // For now, return as-is
    // TODO: Implement silence detection and volume normalization
    return chunk;
  }
}
