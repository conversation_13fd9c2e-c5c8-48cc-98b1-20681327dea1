export enum FileType {
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
  DOC = 'doc',
  PDF = 'pdf',
}

/**
 * Format file size from bytes to human-readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size (e.g., "1.2mb", "4.5kb")
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(1));
  const unit = sizes[i] || 'B'; // Fallback to 'B' if index is out of bounds
  return `${size}${unit.toLowerCase()}`;
}

/**
 * Extract file extension from filename
 * @param filename - The filename to extract extension from
 * @returns File extension in lowercase (without dot)
 */
export function getFileExtension(filename: string): string {
  if (!filename) return '';
  const parts = filename.split('.');
  return parts.length > 1 ? parts[parts.length - 1]?.toLowerCase() || '' : '';
}

/**
 * Get file type based on file extension
 * @param extension - File extension (without dot)
 * @returns FileType enum value or null if not recognized
 */
export function getFileTypeFromExtension(extension: string): FileType | null {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'];
  const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'];
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'ico'];
  const docExtensions = ['doc', 'docx', 'txt', 'rtf', 'odt', 'md', 'csv'];
  const pdfExtensions = ['pdf'];

  const ext = extension.toLowerCase();

  if (videoExtensions.includes(ext)) return FileType.VIDEO;
  if (audioExtensions.includes(ext)) return FileType.AUDIO;
  if (imageExtensions.includes(ext)) return FileType.IMAGE;
  if (docExtensions.includes(ext)) return FileType.DOC;
  if (pdfExtensions.includes(ext)) return FileType.PDF;

  return null;
}

/**
 * Get file type from filename
 * @param filename - The filename to analyze
 * @returns FileType enum value or null if not recognized
 */
export function getFileTypeFromFilename(filename: string): FileType | null {
  const extension = getFileExtension(filename);
  return getFileTypeFromExtension(extension);
}

/**
 * Check if file is an image based on extension
 * @param filename - The filename to check
 * @returns True if file is an image
 */
export function isImageFile(filename: string): boolean {
  return getFileTypeFromFilename(filename) === FileType.IMAGE;
}

/**
 * Check if file is a video based on extension
 * @param filename - The filename to check
 * @returns True if file is a video
 */
export function isVideoFile(filename: string): boolean {
  return getFileTypeFromFilename(filename) === FileType.VIDEO;
}

/**
 * Check if file is an audio file based on extension
 * @param filename - The filename to check
 * @returns True if file is an audio file
 */
export function isAudioFile(filename: string): boolean {
  return getFileTypeFromFilename(filename) === FileType.AUDIO;
}

/**
 * Check if file is a document based on extension
 * @param filename - The filename to check
 * @returns True if file is a document
 */
export function isDocumentFile(filename: string): boolean {
  const fileType = getFileTypeFromFilename(filename);
  return fileType === FileType.DOC || fileType === FileType.PDF;
}

/**
 * Sanitize filename for safe storage
 * @param filename - Original filename
 * @returns Sanitized filename
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_+|_+$/g, ''); // Remove leading/trailing underscores
}
