/**
 * Generate a unique ID with a prefix
 * @param prefix - Prefix for the ID (e.g., 'ingestion', 'user', 'doc')
 * @returns Unique ID string
 */
export function generateId(prefix: string = 'id'): string {
  const timestamp = Date.now();
  const randomPart = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${randomPart}`;
}

/**
 * Generate a unique ingestion ID
 * @returns Unique ingestion ID
 */
export function generateIngestionId(): string {
  return generateId('ingestion');
}

/**
 * Generate a unique knowledge base ID
 * @returns Unique knowledge base ID
 */
export function generateKnowledgeBaseId(): string {
  return generateId('kb');
}

/**
 * Generate a unique document ID
 * @returns Unique document ID
 */
export function generateDocumentId(): string {
  return generateId('doc');
}

/**
 * Generate a unique vector ID
 * @returns Unique vector ID
 */
export function generateVectorId(): string {
  return generateId('vector');
}

/**
 * Generate a short random string
 * @param length - Length of the random string (default: 8)
 * @returns Random string
 */
export function generateRandomString(length: number = 8): string {
  return Math.random().toString(36).substr(2, length);
}

/**
 * Generate a UUID-like string (not RFC compliant, but unique enough for most uses)
 * @returns UUID-like string
 */
export function generateUuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Check if a string looks like an ID generated by our system
 * @param id - ID string to validate
 * @param prefix - Expected prefix (optional)
 * @returns True if ID matches our format
 */
export function isValidGeneratedId(id: string, prefix?: string): boolean {
  if (!id) return false;

  const pattern = prefix ? new RegExp(`^${prefix}_\\d+_[a-z0-9]+$`) : /^[a-z]+_\d+_[a-z0-9]+$/;

  return pattern.test(id);
}
