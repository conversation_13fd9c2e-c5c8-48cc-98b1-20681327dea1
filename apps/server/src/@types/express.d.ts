import type { Request as ExpressRequest } from 'express';
import type { User } from '@/resources/user/user.model';

declare global {
  export interface Request extends ExpressRequest {
    clientCountry: string | null;
    clientCity: string | null;
    clientIp: string | null;
    rawBody?: Buffer;
    user?: User;
    body?: Record<string, unknown> & {
      email?: string;
      password?: string;
    };
  }
}

export {};
