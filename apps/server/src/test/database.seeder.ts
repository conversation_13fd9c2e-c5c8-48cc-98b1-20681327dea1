import { hash } from 'bcrypt';
import mongoose from 'mongoose';

import { teams, users } from './seeds';

// Connect to the test database
const connectToDatabase = async (): Promise<mongoose.Connection> => {
  try {
    const mongoUrl = process.env.MONGO_URL;
    if (!mongoUrl) {
      throw new Error('MONGO_URL is not defined in .env.test');
    }

    await mongoose.connect(mongoUrl);
    console.log('Connected to MongoDB for seeding test data.');
    return mongoose.connection;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    throw error;
  }
};

// Check if the database already has seed data
const isDatabaseSeeded = async (connection: mongoose.Connection): Promise<boolean> => {
  const userCollection = connection.collection('users');
  const userCount = await userCollection.countDocuments();
  return userCount >= 4;
};

// Seed the database with test data
const seedDatabase = async (connection: mongoose.Connection): Promise<void> => {
  try {
    // Insert teams
    const teamCollection = connection.collection('teams');
    await teamCollection.insertMany(teams);
    console.log('Teams seeded successfully');

    // Hash passwords and insert users
    const userCollection = connection.collection('users');
    const usersWithHashedPasswords = await Promise.all(
      users.map(async (user) => ({
        ...user,
        password: await hash(user.password, 10),
      })),
    );
    await userCollection.insertMany(usersWithHashedPasswords);
    console.log('Users seeded successfully');
  } catch (error) {
    console.error('Failed to seed database:', error);
    throw error;
  }
};

// Main function to seed the database if it's not already seeded
export const seedTestDatabase = async (): Promise<void> => {
  let connection: mongoose.Connection | null = null;
  try {
    connection = await connectToDatabase();
    const seeded = await isDatabaseSeeded(connection);

    if (seeded) {
      console.log('Database already seeded, skipping seed operation.');
    } else {
      console.log('Seeding database with test data...');
      await seedDatabase(connection);
      console.log('Database seeded successfully.');
    }
  } catch (error) {
    console.error('Error during database seeding:', error);
    throw error;
  } finally {
    if (connection) {
      await mongoose.disconnect();
      console.log('Disconnected from MongoDB.');
    }
  }
};

// If this file is run directly (not imported), seed the database
if (require.main === module) {
  seedTestDatabase()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
