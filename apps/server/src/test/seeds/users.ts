import { ObjectId } from 'mongodb';

import { UserStatus, UserRole } from '../../resources/user/user.enums';
import { Teams } from './teams';

export const Users = {
  SuperAdmin: new ObjectId('60d0fe4f5311236168a109da'),
  Owner: new ObjectId('60d0fe4f5311236168a109db'),
  Admin: new ObjectId('60d0fe4f5311236168a109dc'),
  User: new ObjectId('60d0fe4f5311236168a109dd'),
};

export const users = [
  {
    _id: Users.SuperAdmin,
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: UserRole.SuperAdmin,
    status: UserStatus.Active,
    verified: true,
    team: Teams.Omega,
    lastLoginAt: new Date(),
  },
  {
    _id: Users.Owner,
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: UserRole.Owner,
    status: UserStatus.Active,
    verified: true,
    team: Teams.Alpha,
    lastLoginAt: new Date(),
  },
  {
    _id: Users.Admin,
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: UserRole.Admin,
    status: UserStatus.Active,
    verified: true,
    team: Teams.Beta,
    lastLoginAt: new Date(),
  },
  {
    _id: Users.User,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    password: 'password123',
    role: UserRole.User,
    status: UserStatus.Active,
    verified: true,
    team: Teams.Gamma,
    lastLoginAt: new Date(),
  },
];
