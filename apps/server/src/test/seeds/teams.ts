import { ObjectId } from 'mongodb';

export const Teams = {
  Omega: new ObjectId('60d0fe4f5311236168a109ca'),
  Alpha: new ObjectId('60d0fe4f5311236168a109cb'),
  Beta: new ObjectId('60d0fe4f5311236168a109cc'),
  Gamma: new ObjectId('60d0fe4f5311236168a109cd'),
};

export const teams = [
  {
    _id: Teams.Omega,
    name: 'Team Omega',
  },
  {
    _id: Teams.Alpha,
    name: 'Team Alpha',
  },
  {
    _id: Teams.Beta,
    name: 'Team Beta',
  },
  {
    _id: Teams.Gamma,
    name: 'Team Gamma',
  },
];
