import type { User } from '@/resources/user/user.model';

import { UseG<PERSON><PERSON>, Controller, Patch, Body, Get } from '@nestjs/common';

import { UserService } from '@/resources/user/user.service';
import { ReqUser } from '@/common/decorators/req-user.decorator';
import { Auth } from '@/auth/guards/auth.guard';

@Controller('me')
export class MeController {
  constructor(private readonly userService: UserService) {}

  @UseGuards(Auth)
  @Get()
  getAuthenticatedUser(@ReqUser() user: User) {
    return user;
  }

  @UseGuards(Auth)
  @Patch()
  async updateProfile(
    @ReqUser() user: User,
    @Body() updateData: Partial<Pick<User, 'name' | 'profilePicture'>>,
  ) {
    return this.userService.update(user._id, updateData);
  }
}
