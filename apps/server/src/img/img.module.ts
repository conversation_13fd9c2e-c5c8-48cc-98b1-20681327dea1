import { Module } from '@nestjs/common';
import { CommonServicesModule } from '../common/services/common-services.module';
import { LlmModule } from '../llm/llm.module';
import { VectorStoreModule } from '../modules/vector-store/vector-store.module';
import { ImgController } from './img.controller';
import { ImageGenerationService, ImageSearchService, VisionService } from './services';

@Module({
  imports: [CommonServicesModule, LlmModule, VectorStoreModule],
  controllers: [ImgController],
  providers: [ImageGenerationService, VisionService, ImageSearchService],
  exports: [ImageGenerationService, VisionService, ImageSearchService],
})
export class ImgModule {}
