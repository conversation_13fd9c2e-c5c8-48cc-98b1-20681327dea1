import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { ImageVisionDto } from '../dto/image-vision.dto';

export interface ImageAnalysisResult {
  title: string;
  description: string;
}

@Injectable()
export class VisionService {
  private readonly logger = new Logger(VisionService.name);
  private readonly openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async analyzeImage(dto: ImageVisionDto): Promise<ImageAnalysisResult> {
    try {
      this.logger.debug(`Starting image analysis for: ${dto.imageUrl}`);

      const prompt = this.buildVisionPrompt(dto.context);

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              {
                type: 'image_url',
                image_url: {
                  url: dto.imageUrl,
                },
              },
            ],
          },
        ],
        max_tokens: 1000,
      });

      const result = this.parseVisionResponse(response.choices[0]?.message?.content || '');

      this.logger.debug(`Image analysis completed successfully`);
      return result;
    } catch (error) {
      this.logger.error('Failed to analyze image', {
        error: error instanceof Error ? error.message : String(error),
        imageUrl: dto.imageUrl,
      });

      // Return fallback result
      return {
        title: 'Image Analysis Failed',
        description: 'Unable to analyze the provided image',
      };
    }
  }

  async extractTextFromImage(imageUrl: string): Promise<string> {
    try {
      this.logger.debug(`Extracting text from image: ${imageUrl}`);

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'Please extract all text content from this image. Return only the extracted text without any additional formatting or commentary.',
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl,
                },
              },
            ],
          },
        ],
        max_tokens: 500,
      });

      return response.choices[0]?.message?.content || '';
    } catch (error) {
      this.logger.error('Failed to extract text from image', {
        error: error instanceof Error ? error.message : String(error),
        imageUrl,
      });
      return '';
    }
  }

  async generateImageTitle(imageUrl: string, context?: string): Promise<string> {
    try {
      this.logger.debug(`Generating title for image: ${imageUrl}`);

      const contextInfo = context ? `Context: ${context}\n\n` : '';
      const prompt = `${contextInfo}Please generate a concise, descriptive title for this image. The title should be educational and capture the main subject or concept shown. Return only the title without any additional text.`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl,
                },
              },
            ],
          },
        ],
        max_tokens: 50,
      });

      return response.choices[0]?.message?.content || 'Untitled Image';
    } catch (error) {
      this.logger.error('Failed to generate image title', {
        error: error instanceof Error ? error.message : String(error),
        imageUrl,
      });
      return 'Untitled Image';
    }
  }

  private buildVisionPrompt(context?: string): string {
    const contextSection = context ? `Context: ${context}\n\n` : '';

    return `${contextSection}Please analyze this image and provide a comprehensive analysis in the following JSON format:

    {
      "title": "A concise, descriptive title for the image in maximum 6 to 8 words",
      "description": "A detailed description of what the image shows, including visual elements, composition, and key features",
    }

    Focus on retrieving all necessary information from the image.`;
  }

  private parseVisionResponse(content: string): ImageAnalysisResult {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          title: parsed.title || 'Untitled Image',
          description: parsed.description || 'No description available',
        };
      }

      // Fallback parsing if JSON is not properly formatted
      return {
        title: this.extractField(content, 'title') || 'Untitled Image',
        description: this.extractField(content, 'description') || content.substring(0, 200),
      };
    } catch (error) {
      this.logger.warn('Failed to parse vision response, using fallback', {
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        title: 'Image Analysis',
        description: content.substring(0, 200),
      };
    }
  }

  private extractField(content: string, field: string): string | undefined {
    const regex = new RegExp(`"${field}":\\s*"([^"]*)"`, 'i');
    const match = content.match(regex);
    return match ? match[1] : undefined;
  }
}
