import { S3Service } from '@/common/services/s3.service';
import { openai } from '@ai-sdk/openai';
import { Injectable, Logger } from '@nestjs/common';
import { experimental_generateImage as generateImage } from 'ai';
import pRetry from 'p-retry';
import { IMAGE_MODEL_PRICING } from '../../llm/llm.models';

export interface ImageArtifact {
  id: string;
  type: 'image';
  title: string;
  description: string;
  prompt: string;
  model: string;
  size: string;
  quality: string;
  cost: number;
  placeholder: string; // e.g., "{{IMAGE_1}}"
  imageUrl?: string; // S3 URL of the generated image
  s3Key?: string; // S3 key for the image
}

export enum ImageSize {
  SQUARE = '1024x1024',
  LANDSCAPE = '1536x1024',
  PORTRAIT = '1024x1536',
}

export enum ImageQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

@Injectable()
export class ImageGenerationService {
  private readonly logger = new Logger(ImageGenerationService.name);
  private imageCounter = 0;

  constructor(private readonly s3Service: S3Service) {}

  async generateImage(
    heading: string,
    imageDescription: string,
    summary: string,
    gradeLevel: string,
    size: ImageSize = ImageSize.LANDSCAPE,
    quality: ImageQuality = ImageQuality.LOW,
  ): Promise<ImageArtifact> {
    const imageId = `image_${Date.now()}_${++this.imageCounter}`;
    const placeholder = `{{IMAGE_${this.imageCounter}}}`;

    try {
      this.logger.debug(`Starting image generation with retry for: ${heading}`);

      // Use p-retry for the entire image generation process
      const result = await pRetry(
        async (attemptNumber) => {
          this.logger.debug(`Image generation attempt ${attemptNumber} for: ${heading}`);

          // Handle empty or invalid description
          if (!imageDescription) {
            throw new Error('No image description provided');
          }

          // Enhance the prompt for educational context
          // const enhancedPrompt = this.buildEducationalImagePrompt(
          //   imageDescription,
          //   sectionHeading,
          //   content,
          //   gradeLevel,
          // );
          const prompt = `${imageDescription}\nAdding more context below which would be helpful for the image generation.\n${heading}\n${summary}`;

          // Generate the actual image using OpenAI
          const { imageBuffer, cost } = await this.generateImageWithOpenAI(prompt);

          if (!imageBuffer || imageBuffer.length === 0) {
            throw new Error('Generated image buffer is empty');
          }

          // Upload to S3 with retry
          const s3Key = this.generateS3Key(imageId, 'image');
          const imageUrl = await this.s3Service.uploadFile(s3Key, imageBuffer, 'image/png', {
            type: 'image',
            imageId,
            sectionHeading: this.sanitizeMetadata(heading),
            gradeLevel: this.sanitizeMetadata(gradeLevel),
            description: this.sanitizeMetadata(imageDescription),
          });

          return {
            imageUrl,
            s3Key,
            cost,
            enhancedPrompt: prompt,
          };
        },
        {
          retries: 3,
          minTimeout: 1000,
          maxTimeout: 5000,
          factor: 2,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Image generation attempt ${error.attemptNumber} failed for ${heading}:`,
              {
                error: error.message,
                attemptsLeft: error.retriesLeft,
              },
            );
          },
        },
      );

      const ImageArtifact: ImageArtifact = {
        id: imageId,
        type: 'image',
        title: `Image for ${heading}`,
        description: imageDescription,
        prompt: result.enhancedPrompt,
        model: 'gpt-image-1',
        size,
        quality,
        cost: result.cost,
        placeholder,
        imageUrl: result.imageUrl,
        s3Key: result.s3Key,
      };

      this.logger.debug(
        `Image generated successfully with retry: ${placeholder}, cost: $${result.cost.toFixed(6)}`,
      );
      return ImageArtifact;
    } catch (error) {
      this.logger.error('Failed to generate image after all retries', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        sectionHeading: heading,
        imageDescription,
        imageId,
      });
      return this.createEmptyPlaceholder();
    }
  }

  private async generateImageWithOpenAI(
    prompt: string,
    size: ImageSize = ImageSize.LANDSCAPE,
    quality: ImageQuality = ImageQuality.MEDIUM,
  ): Promise<{ imageBuffer: Buffer; cost: number }> {
    try {
      // Generate image using OpenAI's gpt-image-1 model
      const result = await generateImage({
        model: openai.image('gpt-image-1'),
        prompt,
        size,
        providerOptions: {
          openai: {
            quality,
          },
        },
      });

      if (!result.image?.base64) {
        throw new Error('No image returned from OpenAI');
      }

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(result.image.base64, 'base64');

      // Calculate cost for gpt-image-1
      const cost = this.calculateImageCost('gpt-image-1', size, quality);

      return { imageBuffer, cost };
    } catch (error) {
      this.logger.error('Failed to generate image with OpenAI', {
        error: error instanceof Error ? error.message : String(error),
        prompt: prompt.substring(0, 100) + '...',
      });
      throw error; // Re-throw to be handled by p-retry
    }
  }

  private generateS3Key(imageId: string, type: string): string {
    return this.s3Service.generateContentKey(`${imageId}.png`, type);
  }

  private calculateImageCost(model: string, size: string, quality: string): number {
    // Use centralized pricing from llm.models.ts
    const modelPricing = IMAGE_MODEL_PRICING[model as keyof typeof IMAGE_MODEL_PRICING];
    if (modelPricing) {
      const sizePricing = modelPricing[size as keyof typeof modelPricing];
      if (sizePricing) {
        const cost = sizePricing[quality as keyof typeof sizePricing];
        if (typeof cost === 'number') {
          return cost;
        }
      }
    }
    // Default fallback to gpt-image-1 medium quality 1024x1024 cost
    return 0.042;
  }

  private createEmptyPlaceholder(): ImageArtifact {
    this.imageCounter++;
    return {
      id: `empty_image_${Date.now()}`,
      type: 'image',
      title: 'Image',
      description: 'No image description available',
      prompt: 'Educational placeholder image',
      model: 'gpt-image-1',
      size: '1024x1024',
      quality: 'medium',
      cost: 0,
      placeholder: `{{IMAGE_${this.imageCounter}}}`,
    };
  }

  async generateImageFromKeywords(
    keywords: string[],
    context: string,
    gradeLevel: string,
  ): Promise<ImageArtifact> {
    try {
      if (!keywords || keywords.length === 0) {
        this.logger.warn('No keywords provided for image generation');
        return this.createEmptyPlaceholder();
      }

      const imageDescription = `Educational illustration featuring: ${keywords.join(', ')}`;
      return await this.generateImage('', imageDescription, context, gradeLevel);
    } catch (error) {
      this.logger.error('Failed to generate image from keywords', {
        error: error instanceof Error ? error.message : String(error),
        keywords,
      });
      return this.createEmptyPlaceholder();
    }
  }

  async generateConceptImage(
    concept: string,
    subject: string,
    gradeLevel: string,
  ): Promise<ImageArtifact> {
    try {
      if (!concept) {
        this.logger.warn('No concept provided for image generation');
        return this.createEmptyPlaceholder();
      }

      const imageDescription = `Educational concept illustration of ${concept} in ${subject}`;
      return await this.generateImage(subject, imageDescription, concept, gradeLevel);
    } catch (error) {
      this.logger.error('Failed to generate concept image', {
        error: error instanceof Error ? error.message : String(error),
        concept,
        subject,
      });
      return this.createEmptyPlaceholder();
    }
  }

  async generateMultipleImages(
    sectionHeading: string,
    imageDescriptions: string[],
    content: string,
    gradeLevel: string,
  ): Promise<ImageArtifact[]> {
    try {
      if (!imageDescriptions || imageDescriptions.length === 0) {
        return [];
      }

      const imagePromises = imageDescriptions.map((description) =>
        this.generateImage(sectionHeading, description, content, gradeLevel),
      );

      return await Promise.all(imagePromises);
    } catch (error) {
      this.logger.error('Failed to generate multiple images', {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
      });
      return [];
    }
  }

  private sanitizeMetadata(value: string): string {
    if (!value) return '';
    // Remove newlines, tabs, and non-ASCII characters
    // AWS S3 metadata only supports ASCII characters and has length limits
    return value
      .replace(/[\r\n\t]/g, ' ') // Replace newlines and tabs with spaces
      .replace(/[^\x20-\x7E]/g, '') // Remove non-ASCII characters
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim() // Remove leading/trailing spaces
      .substring(0, 255); // Limit to 255 characters for S3 metadata
  }
}
