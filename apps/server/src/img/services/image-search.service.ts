import { S3Service } from '@/common/services/s3.service';
import { generateKnowledgeBaseId } from '@/common/utils/id.utils';
import { AiSdk } from '@/llm/sdk/AiSdk';
import { VectorSearchService } from '@/modules/vector-store';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import axios from 'axios';
import OpenAI from 'openai';
import {
  GenerateImageUsingReferenceDto,
  ImageGenerationResponseDto,
  ImageQualityEnum,
  ImageSizeEnum,
  ReferenceImageDto,
} from '../dto/image-generation-reference.dto';
import {
  ImageSearchQueryDto,
  ImageSearchResponseDto,
  ImageSearchResultDto,
  SearchTypeEnum,
  SimilarImagesQueryDto,
} from '../dto/image-search.dto';

@Injectable()
export class ImageSearchService {
  private readonly logger = new Logger(ImageSearchService.name);
  private readonly openai: OpenAI;
  private readonly aiSdk: AiSdk;

  constructor(
    private readonly vectorSearchService: VectorSearchService,
    private readonly s3Service: S3Service,
  ) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.aiSdk = new AiSdk({
      provider: 'openai',
      model: 'gpt-4o-mini',
      identifierName: 'image-search-service',
      identifierValue: 'default',
    });
  }

  /**
   * Generate image using reference images found through search
   */
  async generateImageUsingReference(
    dto: GenerateImageUsingReferenceDto,
    teamId: string,
  ): Promise<ImageGenerationResponseDto> {
    const startTime = Date.now();
    const namespace = teamId;

    try {
      this.logger.log(
        `Generating image using reference search for prompt: "${dto.prompt}" in namespace: ${namespace}`,
      );

      // 1. Search for reference images
      const searchResults = await this.searchImages({
        query: `${dto.userInstructions}`,
        limit: dto.maxReferenceImages || 5,
        namespace,
        minScore: dto.minSimilarityScore || 0.5,
      });

      if (searchResults.results.length === 0) {
        this.logger.warn(`No reference images found for prompt: "${dto.prompt}"`);
      }

      this.logger.log(`Found ${searchResults.results.length} reference images`);

      // 2. Convert reference images to file objects
      const referenceImages: ReferenceImageDto[] = [];
      const imageFiles: any[] = [];

      for (const result of searchResults.results) {
        try {
          // Prefer S3 URL for better reliability, fall back to original fileUrl or url
          const imageUrl = result.metadata.s3Url || result.metadata.fileUrl || result.metadata.url;
          if (!imageUrl) {
            this.logger.warn(`Skipping image ${result.id}: No URL available`);
            continue;
          }

          // Download image as buffer and create file object
          const imageBuffer = await this.downloadImageAsBuffer(imageUrl);
          const fileName = `${result.id}.png`;

          // Use OpenAI's toFile to create file object
          const { toFile } = await import('openai');
          const fileObject = await toFile(imageBuffer, fileName, {
            type: 'image/png',
          });

          referenceImages.push({
            id: result.id,
            name: result.name,
            score: result.score,
            imageUrl,
          });

          imageFiles.push(fileObject);
        } catch (error) {
          this.logger.error(`Failed to process reference image ${result.id}:`, error);
          continue;
        }
      }

      if (imageFiles.length === 0) {
        throw new Error('Failed to process any reference images');
      }

      // 3. Build generation prompt with custom properties
      let generationPrompt = dto.prompt + '\n' + dto.userInstructions;

      if (dto.customProperties && Object.keys(dto.customProperties).length > 0) {
        const customPropsText = Object.entries(dto.customProperties)
          .map(([key, value]) => `${key}: ${value}`)
          .join(', ');
        generationPrompt += `. Additional style requirements: ${customPropsText}`;
      }

      generationPrompt += `. Use the provided reference images as inspiration and incorporate their visual elements.
      If there are faces in the references make sure the faces are kept as it is without any modifications.`;

      // 4. Generate image using OpenAI Images Edit API
      const response = await this.openai.images.edit({
        model: 'gpt-image-1',
        image: imageFiles,
        prompt: generationPrompt,
      });

      // 5. Extract generated image
      if (!response.data || response.data.length === 0) {
        throw new Error('No image generated from OpenAI');
      }

      const imageBase64 = response.data[0]?.b64_json;
      if (!imageBase64) {
        throw new Error('No base64 image data received from OpenAI');
      }

      const imageGenerationId = `gen_${generateKnowledgeBaseId()}`;

      // 6. Upload generated image to S3
      const s3Key = `generated-images/${teamId}/${imageGenerationId}.png`;
      const s3Url = await this.s3Service.uploadBase64File(
        s3Key,
        `data:image/png;base64,${imageBase64}`,
        'image/png',
        {
          teamId,
          generatedBy: 'image-generation-reference',
          prompt: dto.prompt,
          userInstructions: dto.userInstructions,
          referenceImagesCount: referenceImages.length.toString(),
          generatedAt: new Date().toISOString(),
        },
      );

      const generationTime = Date.now() - startTime;

      // 7. Get revised prompt (not available in images.edit API)
      const revisedPrompt = undefined;

      this.logger.log(
        `Successfully generated and uploaded image with ID: ${imageGenerationId} to S3: ${s3Url} in ${generationTime}ms using ${referenceImages.length} reference images`,
      );

      return {
        imageId: imageGenerationId,
        imageUrl: s3Url, // Return S3 URL instead of base64
        searchPrompt: dto.prompt,
        generationPrompt,
        revisedPrompt,
        referenceImages,
        customProperties: dto.customProperties,
        imageSize: dto.imageSize || ImageSizeEnum.SQUARE,
        imageQuality: dto.imageQuality || ImageQualityEnum.AUTO,
        generationTime,
        referenceImagesCount: referenceImages.length,
        createdAt: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Image generation using reference failed: ${errorMessage}`);
      throw new Error(`Image generation using reference failed: ${errorMessage}`);
    }
  }

  /**
   * Download image from URL as buffer
   */
  private async downloadImageAsBuffer(imageUrl: string): Promise<Buffer> {
    try {
      this.logger.debug(`Downloading image from URL: ${imageUrl}`);

      // Download the image
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ImageBot/1.0)',
        },
      });

      const imageBuffer = Buffer.from(response.data);

      this.logger.debug(
        `Successfully downloaded image from URL: ${imageUrl} (size: ${imageBuffer.length} bytes)`,
      );

      return imageBuffer;
    } catch (error) {
      this.logger.error(`Failed to download image from URL ${imageUrl}:`, error);
      throw new Error(`Failed to download image from URL: ${error}`);
    }
  }

  /**
   * Search for images using different search methods
   */
  async searchImages(query: ImageSearchQueryDto): Promise<ImageSearchResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `Searching images with query: "${query.query}" using ${query.searchType || 'hybrid'} search in namespace: ${query.namespace || 'default'}`,
      );

      // Build filters for images
      const filters = this.buildImageFilters(query.tags, query.minScore);
      const namespace = query.namespace || 'default';
      const searchType = query.searchType || SearchTypeEnum.HYBRID;

      let searchResults: any[] = [];

      switch (searchType) {
        case SearchTypeEnum.TEXT:
          searchResults = await this.performTextSearch(
            query.query,
            filters,
            query.limit || 20,
            namespace,
          );
          break;

        case SearchTypeEnum.VECTOR:
          searchResults = await this.performVectorSearch(
            query.query,
            filters,
            query.limit || 20,
            namespace,
          );
          break;

        case SearchTypeEnum.HYBRID:
          searchResults = await this.performHybridSearch(
            query.query,
            filters,
            query.limit || 20,
            namespace,
            query.vectorWeight || 0.7,
          );
          break;

        default:
          throw new Error(`Unsupported search type: ${searchType}`);
      }

      // Transform results to ImageSearchResultDto
      const results: ImageSearchResultDto[] = searchResults
        .filter((item) => (query.minScore ? item.score >= query.minScore : true))
        .map((item) => this.transformToImageResult(item));

      const searchTime = Date.now() - startTime;

      this.logger.log(
        `Found ${results.length} images for query: "${query.query}" using ${searchType} search in ${searchTime}ms`,
      );

      return {
        results,
        total: results.length,
        query: query.query,
        searchTime,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Image search failed for query "${query.query}": ${errorMessage}`);
      throw new Error(`Image search failed: ${errorMessage}`);
    }
  }

  /**
   * Perform text-based search (BM25)
   */
  private async performTextSearch(
    queryText: string,
    filters: any,
    limit: number,
    namespace: string,
  ): Promise<any[]> {
    const searchResult = await this.vectorSearchService.textSearch({
      query: queryText,
      topK: limit,
      filters,
      namespace,
    });

    return searchResult.items;
  }

  /**
   * Perform vector-based search using embeddings
   */
  private async performVectorSearch(
    queryText: string,
    filters: any,
    limit: number,
    namespace: string,
  ): Promise<any[]> {
    // Convert text query to vector embedding
    const embeddingResult = await this.aiSdk.embedText({
      text: queryText,
      identifierName: 'image-search-service',
      identifierValue: 'vector-search',
    });

    // Perform vector search
    const searchResults = await this.vectorSearchService.vectorSearch(embeddingResult.embedding, {
      topK: limit,
      filters,
      namespace,
    });

    return searchResults;
  }

  /**
   * Perform hybrid search combining vector and text search
   */
  private async performHybridSearch(
    queryText: string,
    filters: any,
    limit: number,
    namespace: string,
    vectorWeight: number = 0.7,
  ): Promise<any[]> {
    // Convert text query to vector embedding
    const embeddingResult = await this.aiSdk.embedText({
      text: queryText,
      identifierName: 'image-search-service',
      identifierValue: 'hybrid-search',
    });

    // Perform hybrid search
    const searchResults = await this.vectorSearchService.hybridSearch(
      embeddingResult.embedding,
      queryText,
      {
        topK: limit,
        filters,
        namespace,
        vectorWeight,
      },
    );

    return searchResults;
  }

  /**
   * Find similar images to a given image
   */
  async findSimilarImages(query: SimilarImagesQueryDto): Promise<ImageSearchResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `Finding similar images for ID: ${query.imageId} in namespace: ${query.namespace || 'default'}`,
      );

      // First, get the reference image to extract its vector
      const referenceImage = await this.getImageById(query.imageId, query.namespace || 'default');

      if (!referenceImage) {
        throw new NotFoundException(`Image with ID ${query.imageId} not found`);
      }

      // Build filters for images (excluding the reference image itself)
      const filters = this.buildImageFilters(undefined, query.minScore, query.imageId);

      // Perform vector similarity search
      const searchResult = await this.vectorSearchService.vectorSearch(referenceImage.vector, {
        topK: query.limit || 10,
        filters,
        namespace: query.namespace || 'default',
      });

      // Transform results
      const results: ImageSearchResultDto[] = searchResult
        .filter((item) => (query.minScore ? item.score >= query.minScore : true))
        .map((item) => this.transformToImageResult(item));

      const searchTime = Date.now() - startTime;

      this.logger.log(
        `Found ${results.length} similar images for ID: ${query.imageId} in ${searchTime}ms`,
      );

      return {
        results,
        total: results.length,
        query: `Similar to ${referenceImage.metadata?.name || query.imageId}`,
        searchTime,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Similar image search failed for ID ${query.imageId}: ${errorMessage}`);
      throw new Error(`Similar image search failed: ${errorMessage}`);
    }
  }

  /**
   * Get all images in a namespace
   */
  async listImages(namespace: string, limit: number = 50): Promise<ImageSearchResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log(`Listing images in namespace: ${namespace}`);

      // Build filters for images only
      const filters = this.buildImageFilters();

      // List documents with image filters
      const results = await this.vectorSearchService.listDocuments(
        limit,
        filters,
        'created_at',
        'desc',
        namespace,
      );

      // Transform results
      const imageResults: ImageSearchResultDto[] = results.map((item) =>
        this.transformToImageResult(item),
      );

      const searchTime = Date.now() - startTime;

      this.logger.log(
        `Listed ${imageResults.length} images in namespace: ${namespace} in ${searchTime}ms`,
      );

      return {
        results: imageResults,
        total: imageResults.length,
        query: 'List all images',
        searchTime,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`List images failed for namespace ${namespace}: ${errorMessage}`);
      throw new Error(`List images failed: ${errorMessage}`);
    }
  }

  /**
   * Build filters for image search
   */
  private buildImageFilters(tags?: string[], _minScore?: number, excludeId?: string): any {
    const filters: any[] = [];

    // Always filter for images using source_type field
    filters.push(['source_type', 'Eq', 'image']);

    // Filter by tags if provided
    if (tags && tags.length > 0) {
      filters.push(['tags', 'In', tags]);
    }

    // Exclude specific ID if provided (for similar image search)
    if (excludeId) {
      filters.push(['id', 'Ne', excludeId]);
    }

    // Combine filters with AND if multiple
    if (filters.length === 1) {
      return filters[0];
    } else if (filters.length > 1) {
      return ['And', filters];
    }

    return undefined;
  }

  /**
   * Transform vector search result to ImageSearchResultDto
   */
  private transformToImageResult(item: any): ImageSearchResultDto {
    const metadata = item.metadata || {};

    return {
      id: item.id,
      name: metadata.name || metadata.filename || metadata.imageTitle || 'Untitled Image',
      score: item.score || 0,
      description:
        item.text ||
        metadata.imageDescription ||
        metadata.description ||
        'No description available',
      metadata: {
        filename: metadata.filename,
        fileExtension: metadata.fileExtension,
        url: item.url || metadata.url,
        tags: metadata.tags || [],
        imageTitle: metadata.imageTitle,
        imageDescription: metadata.imageDescription,
        createdAt: metadata.createdAt,
        updatedAt: metadata.updatedAt,
        knowledgeBaseId: metadata.knowledgeBaseId,
      },
    };
  }

  /**
   * Get image by ID for reference
   */
  private async getImageById(imageId: string, namespace: string): Promise<any> {
    try {
      // Search for the specific image by ID
      const filters = [
        'And',
        [
          ['id', 'Eq', imageId],
          ['source_type', 'Eq', 'image'],
        ],
      ];

      const results = await this.vectorSearchService.listDocuments(
        1,
        filters,
        'created_at',
        'desc',
        namespace,
      );

      return results.length > 0 ? results[0] : null;
    } catch (error) {
      this.logger.error(`Failed to get image by ID ${imageId}: ${error}`);
      return null;
    }
  }

  /**
   * Get image search statistics
   */
  async getImageStats(namespace: string): Promise<{
    totalImages: number;
    imagesByTag: Record<string, number>;
  }> {
    try {
      this.logger.log(`Getting image statistics for namespace: ${namespace}`);

      const filters = this.buildImageFilters();

      // Get all images to calculate stats
      const allImages = await this.vectorSearchService.listDocuments(
        1000, // High limit to get all images
        filters,
        'created_at',
        'desc',
        namespace,
      );

      // Count images by tags
      const imagesByTag: Record<string, number> = {};
      allImages.forEach((image) => {
        const tags = image.metadata?.tags || [];
        tags.forEach((tag: string) => {
          imagesByTag[tag] = (imagesByTag[tag] || 0) + 1;
        });
      });

      return {
        totalImages: allImages.length,
        imagesByTag,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to get image stats for namespace ${namespace}: ${errorMessage}`);
      throw new Error(`Failed to get image stats: ${errorMessage}`);
    }
  }
}
