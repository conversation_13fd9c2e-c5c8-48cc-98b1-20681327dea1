import { ReqUser } from '@/common/decorators/req-user.decorator';
import { Team } from '@/resources/team/team.model';
import { User } from '@/resources/user/user.model';
import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Auth } from '../auth/guards/auth.guard';
import {
  GenerateImageUsingReferenceDto,
  ImageAnalysisResponseDto,
  ImageGenerationResponseDto,
  ImageTitleGenerationDto,
  ImageTitleResponseDto,
  ImageVisionDto,
  TextExtractionDto,
  TextExtractionResponseDto,
} from './dto';
import { ImageSearchService, VisionService } from './services';

@ApiTags('Image Analysis')
@Controller('img')
@UseGuards(Auth)
@ApiBearerAuth()
export class ImgController {
  constructor(
    private readonly visionService: VisionService,
    private readonly imageSearchService: ImageSearchService,
  ) {}

  @Post('analyze')
  @ApiOperation({
    summary: 'Analyze image content',
    description:
      'Analyze an image to extract title, description, content, and educational metadata',
  })
  @ApiResponse({
    status: 201,
    description: 'Image analysis completed successfully',
    type: ImageAnalysisResponseDto,
  })
  async analyzeImage(@Body() dto: ImageVisionDto): Promise<ImageAnalysisResponseDto> {
    const result = await this.visionService.analyzeImage(dto);
    return {
      title: result.title,
      description: result.description,
    };
  }

  @Post('extract-text')
  @ApiOperation({
    summary: 'Extract text from image',
    description: 'Extract all visible text content from an image using OCR capabilities',
  })
  @ApiResponse({
    status: 201,
    description: 'Text extraction completed successfully',
    type: TextExtractionResponseDto,
  })
  async extractText(@Body() dto: TextExtractionDto): Promise<TextExtractionResponseDto> {
    const extractedText = await this.visionService.extractTextFromImage(dto.imageUrl);
    return { extractedText };
  }

  @Post('generate-title')
  @ApiOperation({
    summary: 'Generate title for image',
    description: 'Generate a descriptive title for an image based on its visual content',
  })
  @ApiResponse({
    status: 201,
    description: 'Title generation completed successfully',
    type: ImageTitleResponseDto,
  })
  async generateTitle(@Body() dto: ImageTitleGenerationDto): Promise<ImageTitleResponseDto> {
    const title = await this.visionService.generateImageTitle(dto.imageUrl, dto.context);
    return { title };
  }

  @Post('generate-using-reference')
  @ApiOperation({
    summary: 'Generate image using reference images',
    description:
      'Search for reference images in the team knowledge base and use them to generate a new image based on user instructions',
  })
  @ApiResponse({
    status: 201,
    description: 'Image generated successfully using reference images',
    type: ImageGenerationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'No reference images found for the search query',
  })
  async generateImageUsingReference(
    @Body() dto: GenerateImageUsingReferenceDto,
    @ReqUser() user: User,
  ): Promise<ImageGenerationResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    if (!user.team) {
      throw new Error('User must belong to a team to generate images using reference');
    }

    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('Invalid team ID');
    }

    return this.imageSearchService.generateImageUsingReference(dto, teamId);
  }
}
