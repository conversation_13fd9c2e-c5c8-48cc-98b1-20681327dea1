import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsObject, IsOptional, IsString, Max, Min } from 'class-validator';

export enum ImageSizeEnum {
  SQUARE = '1024x1024',
  LANDSCAPE = '1536x1024',
  PORTRAIT = '1024x1536',
  AUTO = 'auto',
}

export enum ImageQualityEnum {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  AUTO = 'auto',
}

export class GenerateImageUsingReferenceDto {
  @ApiProperty({
    description: 'Search prompt to find reference images',
    example: 'beautiful sunset landscape mountains',
  })
  @IsString()
  prompt!: string;

  @ApiProperty({
    description: 'User instructions for image generation',
    example:
      'Create a watercolor painting style image combining elements from the reference images',
  })
  @IsString()
  userInstructions!: string;

  @ApiPropertyOptional({
    description: 'Custom properties as key-value pairs',
    example: { style: 'watercolor', mood: 'serene', colors: 'warm tones' },
  })
  @IsOptional()
  @IsObject()
  customProperties?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'Image size',
    enum: ImageSizeEnum,
    default: ImageSizeEnum.SQUARE,
  })
  @IsOptional()
  @IsEnum(ImageSizeEnum)
  imageSize?: ImageSizeEnum = ImageSizeEnum.SQUARE;

  @ApiPropertyOptional({
    description: 'Image quality',
    enum: ImageQualityEnum,
    default: ImageQualityEnum.AUTO,
  })
  @IsOptional()
  @IsEnum(ImageQualityEnum)
  imageQuality?: ImageQualityEnum = ImageQualityEnum.AUTO;

  @ApiPropertyOptional({
    description: 'Maximum number of reference images to use',
    minimum: 1,
    maximum: 10,
    default: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(10)
  maxReferenceImages?: number = 5;

  @ApiPropertyOptional({
    description: 'Minimum similarity score for reference images',
    minimum: 0,
    maximum: 1,
    default: 0.5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1)
  minSimilarityScore?: number = 0.5;
}

export class ReferenceImageDto {
  @ApiProperty({
    description: 'Reference image ID',
    example: 'img_12345',
  })
  id!: string;

  @ApiProperty({
    description: 'Reference image name',
    example: 'Mountain Sunset.jpg',
  })
  name!: string;

  @ApiProperty({
    description: 'Similarity score',
    example: 0.87,
  })
  score!: number;

  @ApiProperty({
    description: 'Image URL or file path',
  })
  imageUrl?: string;
}

export class ImageGenerationResponseDto {
  @ApiProperty({
    description: 'Generated image ID for future edits',
    example: 'gen_img_67890',
  })
  imageId!: string;

  @ApiProperty({
    description: 'Generated image URL from S3',
    example:
      'https://bucket.s3.region.amazonaws.com/generated-images/team123/1234567890_gen_abc123.png',
  })
  imageUrl!: string;

  @ApiPropertyOptional({
    description: 'Generated image as base64 string (deprecated, use imageUrl)',
  })
  imageBase64?: string;

  @ApiProperty({
    description: 'Original prompt used for search',
    example: 'beautiful sunset landscape mountains',
  })
  searchPrompt!: string;

  @ApiProperty({
    description: 'Final prompt sent to image generation',
    example: 'Create a watercolor painting combining elements from reference images...',
  })
  generationPrompt!: string;

  @ApiProperty({
    description: 'Revised prompt from OpenAI (if available)',
  })
  revisedPrompt?: string;

  @ApiProperty({
    description: 'Reference images used for generation',
    type: [ReferenceImageDto],
  })
  referenceImages!: ReferenceImageDto[];

  @ApiProperty({
    description: 'Custom properties used',
  })
  customProperties?: Record<string, string>;

  @ApiProperty({
    description: 'Image size used',
    enum: ImageSizeEnum,
  })
  imageSize!: ImageSizeEnum;

  @ApiProperty({
    description: 'Image quality used',
    enum: ImageQualityEnum,
  })
  imageQuality!: ImageQualityEnum;

  @ApiProperty({
    description: 'Generation time in milliseconds',
    example: 3450,
  })
  generationTime!: number;

  @ApiProperty({
    description: 'Number of reference images found and used',
    example: 3,
  })
  referenceImagesCount!: number;

  @ApiProperty({
    description: 'Timestamp when the image was generated',
  })
  createdAt!: Date;
}
