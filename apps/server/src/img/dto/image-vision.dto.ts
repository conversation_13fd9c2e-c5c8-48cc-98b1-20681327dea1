import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUrl } from 'class-validator';

export class ImageVisionDto {
  @ApiProperty({
    description: 'URL of the image to analyze',
    example: 'https://example.com/image.jpg',
  })
  @IsUrl()
  @IsString()
  imageUrl!: string;

  @ApiPropertyOptional({
    description: 'Additional context to help with image analysis',
    example: 'This image is from a biology textbook about cell structure',
  })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiPropertyOptional({
    description: 'Whether to extract text content from the image',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  includeTextExtraction?: boolean;
}

export class ImageAnalysisResponseDto {
  @ApiProperty({
    description: 'Generated title for the image',
    example: 'Cell Structure Diagram',
  })
  title!: string;

  @ApiProperty({
    description: 'Detailed description of the image content',
    example:
      'A detailed diagram showing the internal structure of a plant cell with labeled organelles',
  })
  description!: string;
}

export class ImageTitleGenerationDto {
  @ApiProperty({
    description: 'URL of the image to generate title for',
    example: 'https://example.com/image.jpg',
  })
  @IsUrl()
  @IsString()
  imageUrl!: string;

  @ApiPropertyOptional({
    description: 'Additional context to help with title generation',
    example: 'This image is from a physics textbook about energy',
  })
  @IsOptional()
  @IsString()
  context?: string;
}

export class ImageTitleResponseDto {
  @ApiProperty({
    description: 'Generated title for the image',
    example: 'Renewable Energy Sources Comparison Chart',
  })
  title!: string;
}

export class TextExtractionDto {
  @ApiProperty({
    description: 'URL of the image to extract text from',
    example: 'https://example.com/image.jpg',
  })
  @IsUrl()
  @IsString()
  imageUrl!: string;
}

export class TextExtractionResponseDto {
  @ApiProperty({
    description: 'Extracted text content from the image',
    example: 'Chapter 5: Photosynthesis and Cellular Respiration',
  })
  extractedText!: string;
}
