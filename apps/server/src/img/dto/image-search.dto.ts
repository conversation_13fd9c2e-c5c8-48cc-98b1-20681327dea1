import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsArray, IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

export enum SearchTypeEnum {
  TEXT = 'text',
  VECTOR = 'vector',
  HYBRID = 'hybrid',
}

export class ImageSearchQueryDto {
  @ApiProperty({
    description: 'Search query text to find relevant images',
    example: 'sunset landscape mountains',
  })
  @IsString()
  query!: string;

  @ApiPropertyOptional({
    description: 'Type of search to perform',
    enum: SearchTypeEnum,
    default: SearchTypeEnum.HYBRID,
    example: SearchTypeEnum.HYBRID,
  })
  @IsOptional()
  @IsEnum(SearchTypeEnum)
  searchType?: SearchTypeEnum = SearchTypeEnum.HYBRID;

  @ApiPropertyOptional({
    description: 'Number of results to return',
    minimum: 1,
    maximum: 100,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Namespace for the search (team-specific)',
    example: 'team_12345',
  })
  @IsOptional()
  @IsString()
  namespace?: string;

  @ApiPropertyOptional({
    description: 'Tags to filter by',
    type: [String],
    example: ['nature', 'landscape'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map((tag) => tag.trim());
    }
    return value;
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Minimum similarity score (0-1)',
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1)
  minScore?: number;

  @ApiPropertyOptional({
    description: 'Vector weight for hybrid search (0.0 = text only, 1.0 = vector only)',
    minimum: 0,
    maximum: 1,
    default: 0.4,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1)
  vectorWeight?: number = 0.4;
}

export class ImageSearchResultDto {
  @ApiProperty({
    description: 'Unique identifier of the image',
    example: 'img_12345',
  })
  id!: string;

  @ApiProperty({
    description: 'Image name or title',
    example: 'Mountain Sunset.jpg',
  })
  name!: string;

  @ApiProperty({
    description: 'Similarity score',
    example: 0.87,
  })
  score!: number;

  @ApiProperty({
    description: 'Extracted or generated text description of the image',
    example: 'A beautiful sunset over mountain peaks with golden lighting',
  })
  description!: string;

  @ApiProperty({
    description: 'Image metadata',
  })
  metadata!: {
    filename?: string;
    fileExtension?: string;
    fileUrl?: string; // Original URL (for file uploads)
    url?: string; // Original URL (for web page URLs)
    s3Url?: string; // S3 storage URL
    tags?: string[];
    imageTitle?: string;
    imageDescription?: string;
    createdAt?: string;
    updatedAt?: string;
    knowledgeBaseId?: string;
  };
}

export class ImageSearchResponseDto {
  @ApiProperty({
    description: 'Search results',
    type: [ImageSearchResultDto],
  })
  results!: ImageSearchResultDto[];

  @ApiProperty({
    description: 'Total number of results found',
    example: 15,
  })
  total!: number;

  @ApiProperty({
    description: 'Search query used',
    example: 'sunset landscape',
  })
  query!: string;

  @ApiProperty({
    description: 'Time taken for search in milliseconds',
    example: 245,
  })
  searchTime!: number;
}

export class SimilarImagesQueryDto {
  @ApiProperty({
    description: 'Image ID to find similar images for',
    example: 'img_12345',
  })
  @IsString()
  imageId!: string;

  @ApiPropertyOptional({
    description: 'Number of similar images to return',
    minimum: 1,
    maximum: 50,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Namespace for the search',
    example: 'team_12345',
  })
  @IsString()
  namespace!: string;

  @ApiPropertyOptional({
    description: 'Minimum similarity score',
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1)
  minScore?: number;
}
