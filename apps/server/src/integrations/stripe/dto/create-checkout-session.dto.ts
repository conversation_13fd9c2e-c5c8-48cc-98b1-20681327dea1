import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';

import { SubscriptionPlan, BillingPeriod } from '@/resources/team/team.enums';

export class CreateCheckoutSessionDto {
  @ApiProperty({
    enum: SubscriptionPlan,
    description: 'Subscription Plan',
    example: SubscriptionPlan.Pro,
  })
  @IsEnum(SubscriptionPlan)
  plan!: SubscriptionPlan;

  @ApiProperty({
    enum: BillingPeriod,
    description: 'Billing Period',
    example: BillingPeriod.Monthly,
  })
  @IsEnum(BillingPeriod)
  billingPeriod!: BillingPeriod;
}
