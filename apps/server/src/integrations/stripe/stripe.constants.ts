import { SubscriptionPlan, BillingPeriod } from '@/resources/team/team.enums';
import config from '@/common/configs/config';

type PaidPlans = Exclude<SubscriptionPlan, SubscriptionPlan.NoPlan | SubscriptionPlan.Free>;

const STRIPE_PRICES_LIVE: Record<PaidPlans, Record<BillingPeriod, string>> = {
  [SubscriptionPlan.Pro]: {
    [BillingPeriod.Monthly]: 'price_1RERJvFhM9bD4yCuj6BBzdkZ',
    [BillingPeriod.Yearly]: 'price_1RERM3FhM9bD4yCuSELuD0JD',
    [BillingPeriod.Lifetime]: 'price_1RERMnFhM9bD4yCuUYsZnBt8',
  },
};

const STRIPE_PRICES_SANDBOX: Record<PaidPlans, Record<BillingPeriod, string>> = {
  [SubscriptionPlan.Pro]: {
    [BillingPeriod.Monthly]: 'price_1RESkyCLLKTUyvFa8DZkKrny',
    [BillingPeriod.Yearly]: 'price_1RESlXCLLKTUyvFavad4RQ92',
    [BillingPeriod.Lifetime]: 'price_1RESmaCLLKTUyvFauroEVue6',
  },
};

export const STRIPE_PRICES: Record<PaidPlans, Record<BillingPeriod, string>> = config().isProd
  ? STRIPE_PRICES_LIVE
  : STRIPE_PRICES_SANDBOX;
