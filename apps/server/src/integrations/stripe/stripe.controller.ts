import type { User as UserType } from '@/resources/user/user.model';
import type { RawBodyRequest } from '@nestjs/common';

import {
  BadRequestException,
  Controller,
  UseGuards,
  Redirect,
  Headers,
  Logger,
  Query,
  Body,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

// import { BillingPeriod } from '@/resources/team/team.enums';
import { Auth } from '@/auth/guards/auth.guard';
import { ReqUser } from '@/common/decorators/req-user.decorator';

import { CreateCheckoutSessionDto } from './dto/create-checkout-session.dto';
import { StripeService } from './stripe.service';
import config from '@/common/configs/config';

@ApiTags('Stripe')
@Controller('integrations/stripe')
export class StripeController {
  private readonly logger = new Logger(StripeController.name);

  constructor(private readonly stripeService: StripeService) {}

  @Post('activate-free-plan')
  @ApiBearerAuth()
  @UseGuards(Auth)
  async activateFreePlan(@ReqUser() user: UserType) {
    const result = await this.stripeService.activateFreePlan(user);

    return {
      success: result.success,
      redirectUrl: `${config().app.client.url}/settings/billing?success=true`,
    };
  }

  @Post('create-checkout-session')
  @ApiBearerAuth()
  @UseGuards(Auth)
  async createCheckoutSession(@Body() body: CreateCheckoutSessionDto, @ReqUser() user: UserType) {
    const session = await this.stripeService.createCheckoutSession(user, body);

    return { url: session.url };
  }

  @Post('cancel-subscription')
  @ApiBearerAuth()
  @UseGuards(Auth)
  async cancelSubscription(@ReqUser() user: UserType) {
    await this.stripeService.cancelSubscription(user);
    return {
      success: true,
      message: 'Subscription will be canceled at the end of the billing period',
    };
  }

  @Redirect()
  @Get('checkout/success')
  async handleCheckoutSuccess(@Query('session_id') sessionId: string) {
    try {
      if (!sessionId) {
        throw new BadRequestException('Missing session_id parameter');
      }

      const result = await this.stripeService.handleCheckoutSuccess(sessionId);

      return {
        url: `${result.redirectUrl}?success=true&plan=${result.plan}&billingPeriod=${result.billingPeriod}`,
      };
    } catch (error) {
      this.logger.error('Error handling checkout success:', error);
      return {
        url: `${config().app.client.url}/settings/billing?error=true&message=${encodeURIComponent(
          (error as Error).message,
        )}`,
      };
    }
  }

  @Post('webhooks')
  async handleWebhook(
    @Req() request: RawBodyRequest<Request>,
    @Headers('stripe-signature') signature: string,
  ) {
    if (!signature) {
      throw new BadRequestException('Missing stripe-signature header');
    }
    if (!request.rawBody) {
      throw new BadRequestException('Missing request body.');
    }

    try {
      const event = this.stripeService.verifyWebhookSignature(request.rawBody, signature);

      await this.stripeService.handleWebhookEvent(event);

      return { received: true };
    } catch (err) {
      throw new BadRequestException(`Webhook Error: ${(err as Error).message}`);
    }
  }
}
