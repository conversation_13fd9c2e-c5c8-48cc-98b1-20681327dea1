import type { FileUploadDto } from '@/app/dto/file-upload.dto';
import type { DeleteObjectCommandInput, PutObjectCommandInput } from '@aws-sdk/client-s3';

import {
  DeleteObjectCommand,
  ObjectCannedACL,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { createPresignedPost } from '@aws-sdk/s3-presigned-post';
import { Injectable, Logger } from '@nestjs/common';
import sharp from 'sharp';

import config from '@/common/configs/config';
import { sanitizeFilename } from '@/common/utils/file.utils';

// File size constants in bytes
const ONE_MB = 1024 * 1024;
const FIFTEEN_MB = 15 * ONE_MB;

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private s3Client: S3Client;

  constructor() {
    const awsConfig = config().aws;
    this.s3Client = new S3Client({
      credentials: {
        accessKeyId: awsConfig.accessKeyId,
        secretAccessKey: awsConfig.secretAccessKey,
      },
      region: awsConfig.region,
      forcePathStyle: true,
    });
  }

  async uploadFile(
    file: Buffer,
    fileName: string,
    folderName: string,
    contentType?: string,
    acl: ObjectCannedACL = ObjectCannedACL.public_read,
    bucketName?: string,
  ): Promise<string> {
    const bucket = bucketName || config().aws.buckets.main;
    const sanitizedFileName = sanitizeFilename(fileName);
    const key = `${folderName}/${sanitizedFileName}`;

    const params: PutObjectCommandInput = {
      Bucket: bucket,
      Key: key,
      Body: file,
      ACL: acl,
      ContentType: contentType,
    };

    try {
      const command = new PutObjectCommand(params);
      await this.s3Client.send(command);

      // Construct the public URL with proper encoding
      const encodedKey = encodeURIComponent(key).replace(/%2F/g, '/');
      const location = `https://${bucket}.s3.${config().aws.region}.amazonaws.com/${encodedKey}`;
      this.logger.debug(`uploaded file to s3: ${location}`);
      return location;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`error uploading file to s3: ${errorMessage}`);
      throw error;
    }
  }

  async deleteFile(key: string, bucketName?: string): Promise<void> {
    const bucket = bucketName || config().aws.buckets.main;

    const params: DeleteObjectCommandInput = {
      Bucket: bucket,
      Key: key,
    };

    try {
      const command = new DeleteObjectCommand(params);
      await this.s3Client.send(command);
      this.logger.debug(`deleted file from s3: ${key}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`error deleting file from s3: ${errorMessage}`);
      throw error;
    }
  }

  async resizeImage(
    file: Buffer,
    width: number,
    height?: number,
    format: 'jpeg' | 'png' | 'webp' = 'png',
  ): Promise<Buffer> {
    try {
      let sharpInstance = sharp(file);

      if (height) {
        sharpInstance = sharpInstance.resize(width, height);
      } else {
        sharpInstance = sharpInstance.resize({ width });
      }

      const resizedImage = await sharpInstance.toFormat(format).toBuffer();

      return resizedImage;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`error resizing image: ${errorMessage}`);
      throw error;
    }
  }

  async uploadImage(
    file: Buffer,
    fileName: string,
    folderName: string,
    width?: number,
    height?: number,
    format: 'jpeg' | 'png' | 'webp' = 'webp',
    bucketName?: string,
  ): Promise<string> {
    try {
      let processedFile = file;

      if (width) {
        processedFile = await this.resizeImage(file, width, height, format);
      }

      const contentType = `image/${format === 'jpeg' ? 'jpeg' : format}`;
      const bucket = bucketName || config().aws.buckets.images;

      return await this.uploadFile(
        processedFile,
        fileName,
        folderName,
        contentType,
        ObjectCannedACL.public_read,
        bucket,
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`error uploading image: ${errorMessage}`);
      throw error;
    }
  }

  async getPresignedPost({
    folderName,
    fileName,
    fileType = 'image/',
    minFileSize = 0,
    maxFileSize = FIFTEEN_MB,
  }: FileUploadDto & {
    minFileSize?: number;
    maxFileSize?: number;
  }) {
    const Bucket = this.getBucketName(fileType);

    // Sanitize the filename before constructing the key
    const sanitizedFileName = sanitizeFilename(fileName);

    const Key = `${folderName}/${sanitizedFileName}`;

    try {
      const presignedPost = await createPresignedPost(this.s3Client, {
        Bucket,
        Key,
        Conditions: [
          ['content-length-range', minFileSize, maxFileSize],
          ['starts-with', '$Content-Type', fileType],
        ],
        Expires: 300,
        Fields: { ACL: ObjectCannedACL.public_read, 'Content-Type': fileType },
      });

      return presignedPost;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`S3 Post Generation Failed: ${errorMessage}`);
      throw new Error('S3 Post Generation Failed');
    }
  }

  private getBucketName(fileType: string): string {
    return config().aws.buckets[fileType.startsWith('image/') ? 'images' : 'main'];
  }
}
