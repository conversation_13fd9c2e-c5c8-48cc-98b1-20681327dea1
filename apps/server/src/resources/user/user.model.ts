import type { Team } from '@/resources/team/team.model';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Schema as MongoSchema } from 'mongoose';
import { hash } from 'bcrypt';

import { BaseModel } from '@/resources/base/base.model';
import config from '@/common/configs/config';

import { UserStatus, UserRole } from './user.enums';

@Schema({ timestamps: true, versionKey: false })
export class User extends BaseModel {
  // User Info
  @Prop({ type: String, required: true })
  name!: string;

  @Prop({ type: String, required: true, unique: true })
  email!: string;

  @Prop({ type: String })
  profilePicture?: string;

  // Auth Info
  @Prop({ type: String, required: false })
  password?: string;

  @Prop({ type: String })
  googleId?: string;

  @Prop({ type: String, enum: Object.values(UserRole), default: UserRole.Owner })
  role!: UserRole;

  @Prop({ type: String, enum: Object.values(UserStatus), default: UserStatus.Active })
  status!: UserStatus;

  @Prop({ type: Boolean, default: false })
  verified!: boolean;

  @Prop({ type: Date })
  lastLoginAt!: Date;

  // IP & Geo Location
  @Prop({ type: String })
  ip?: string;

  @Prop({ type: String })
  city?: string;

  @Prop({ type: String })
  country?: string;

  // Relations
  @Prop({ type: MongoSchema.Types.ObjectId, ref: 'Team', required: true })
  team!: Relation<Team>;
}

const UserSchema = SchemaFactory.createForClass(User);

UserSchema.pre('save', async function (next) {
  const user = this as User;
  if (user.password && user.isModified('password')) {
    try {
      user.password = await hash(user.password, config().jwt.bcryptSaltOrRound);
      next();
    } catch (error) {
      next(error as Error);
    }
  }
});

export { UserSchema };
