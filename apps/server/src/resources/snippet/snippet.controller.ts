import type { ControllerOptions } from '@/resources/base/base.types';
import type { Snippet } from './snippet.model';

import { BaseController } from '@/resources/base/base.controller';
import { UserRole } from '@/resources/user/user.enums';

import { SnippetCreateDto } from './dto/snippet-create.dto';
import { SnippetUpdateDto } from './dto/snippet-update.dto';
import { SnippetService } from './snippet.service';

const options: Partial<ControllerOptions> = {
  isTeamResource: false,
  list: { roles: [UserRole.User] },
  show: { roles: [UserRole.User] },
  create: { roles: [UserRole.SuperAdmin] },
  update: { roles: [UserRole.SuperAdmin] },
  remove: { roles: [UserRole.SuperAdmin] },
};

export class SnippetController extends BaseController<
  Snippet,
  typeof SnippetService,
  SnippetCreateDto,
  SnippetUpdateDto
>({
  name: 'Snippet',
  path: 'snippets',
  service: SnippetService,
  options,
}) {}
