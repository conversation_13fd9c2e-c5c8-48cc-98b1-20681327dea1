import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';

import { BaseModel } from '@/resources/base/base.model';
import { Category } from '@/resources/category/category.model';

@Schema({ collection: 'snippets', timestamps: true, versionKey: false })
export class Snippet extends BaseModel {
  @Prop({ type: String, required: true })
  name!: string;

  @Prop({ type: String, required: true })
  description!: string;

  @Prop({ type: String, required: true })
  icon!: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Category', required: true })
  category!: Category;

  @Prop({ type: String })
  promptTemplate?: string;

  @Prop({ type: Boolean, default: true })
  enableChatBox!: boolean;

  @Prop({ type: String })
  chatboxPlaceholder?: string;

  @Prop({ type: Boolean, default: true })
  enableGradeField!: boolean;

  @Prop({ type: [Object] })
  inputFields?: {
    type: 'text' | 'number' | 'select' | 'date' | 'checkbox' | 'radio';
    label: string;
    placeholder?: string;
    options?: {
      label: string;
      value: string;
    }[];
    required?: boolean;
    description?: string;
    defaultValue?: string;
    min?: number;
    max?: number;
    step?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    multiple?: boolean;
    accept?: string;
    rows?: number;
  }[];
}

const SnippetSchema = SchemaFactory.createForClass(Snippet);
export { SnippetSchema };
