import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { SnippetSchema, Snippet } from './snippet.model';
import { SnippetController } from './snippet.controller';
import { SnippetService } from './snippet.service';

@Module({
  imports: [MongooseModule.forFeature([{ schema: SnippetSchema, name: Snippet.name }])],
  controllers: [SnippetController],
  providers: [SnippetService],
  exports: [SnippetService, MongooseModule],
})
export class SnippetModule {}
