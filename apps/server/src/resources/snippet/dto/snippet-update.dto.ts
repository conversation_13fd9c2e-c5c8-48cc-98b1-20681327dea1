import { IsOptional, IsBoolean, IsMongoId, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class SnippetUpdateDto {
  @ApiPropertyOptional({
    description: 'Snippet name',
    example: 'Blog Post Template',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Snippet description',
    example: 'A template for creating engaging blog posts',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Icon URL',
    example: 'https://example.com/icons/blog.png',
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiPropertyOptional({
    description: 'Category ID reference',
    example: '507f1f77bcf86cd799439011',
  })
  @IsOptional()
  @IsMongoId()
  category?: string;

  @ApiPropertyOptional({
    description: 'Prompt template for the snippet',
    example: 'Write a blog post about {topic} in {tone} tone',
  })
  @IsOptional()
  @IsString()
  promptTemplate?: string;

  @ApiPropertyOptional({
    description: 'Enable chatbox',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  enableChatBox?: boolean;

  @ApiPropertyOptional({
    description: 'Chatbox placeholder',
    example: 'Ask AI anything about the topic',
  })
  @IsOptional()
  @IsString()
  chatboxPlaceholder?: string;

  @ApiPropertyOptional({
    description: 'Enable grade field',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  enableGradeField?: boolean;
}
