import { IsNotEmpty, IsOptional, IsBoolean, IsMongoId, IsString } from 'class-validator';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';

export class SnippetCreateDto {
  @ApiProperty({
    description: 'Snippet name',
    example: 'Blog Post Template',
  })
  @IsNotEmpty()
  @IsString()
  name!: string;

  @ApiProperty({
    description: 'Snippet description',
    example: 'A template for creating engaging blog posts',
  })
  @IsNotEmpty()
  @IsString()
  description!: string;

  @ApiProperty({
    description: 'Icon URL',
    example: 'https://example.com/icons/blog.png',
  })
  @IsNotEmpty()
  @IsString()
  icon!: string;

  @ApiProperty({
    description: 'Category ID reference',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsMongoId()
  category!: string;

  @ApiPropertyOptional({
    description: 'Prompt template for the snippet',
    example: 'Write a blog post about {topic} in {tone} tone',
  })
  @IsOptional()
  @IsString()
  promptTemplate?: string;

  @ApiPropertyOptional({
    description: 'Enable chatbox',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  enableChatBox?: boolean;

  @ApiPropertyOptional({
    description: 'Chatbox placeholder',
    example: 'Ask AI anything about the topic',
  })
  @IsOptional()
  @IsString()
  chatboxPlaceholder?: string;

  @ApiPropertyOptional({
    description: 'Enable grade field',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  enableGradeField?: boolean;
}
