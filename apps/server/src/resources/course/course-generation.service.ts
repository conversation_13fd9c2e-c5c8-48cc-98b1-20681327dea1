import { Injectable, Logger } from '@nestjs/common';

import { ImageGenerationService } from '@/img/services/image-generation.service';
import { RagService } from '@/modules/rag/rag.service';
import { AiSdk } from '@/llm/sdk/AiSdk';

import { CourseGenerationResponse, CourseChapter } from './course.type';
import { CourseType } from './course.enums';
import { Course } from './course.model';

interface CourseOutlineResponse {
  title: string;
  description: string;
  chapters: Array<{
    title: string;
    description: string;
    topics?: string[];
  }>;
}

@Injectable()
export class CourseGenerationService {
  private readonly logger = new Logger(CourseGenerationService.name);

  constructor(
    private readonly imageGenerationService: ImageGenerationService,
    private readonly ragService: RagService,
    private readonly aiSdk: AiSdk,
  ) {}

  async generate(teamId: string, course: Course): Promise<CourseGenerationResponse> {
    const startTime = Date.now();
    this.logger.log(
      `Starting course generation for team: ${teamId}, courseId: ${String(course._id)}`,
    );

    try {
      // Process temporary files if provided
      if (course.inputs.fileUrls || course.inputs.urls) {
        this.logger.log(
          `Processing ${course.inputs.fileUrls?.length || 0} files and ${course.inputs.urls?.length || 0} urls`,
        );
        await this.ragService.processTemporaryFiles(
          teamId,
          String(course._id),
          course.inputs.fileUrls,
          course.inputs.urls,
        );
        this.logger.log(
          `Temporary files and urls processed in ${(Date.now() - startTime) / 1000}s`,
        );
      }

      let ragContext = { content: '' };
      if (course.inputs.useKnowledgeBase) {
        const ragStartTime = Date.now();
        // Build RAG context using available parameters
        ragContext = await this.ragService.buildRagContext(
          teamId,
          String(course._id),
          'Education',
          'Course Generation',
          'Generate comprehensive course outline',
          'General',
          true,
          false,
        );
        const ragTime = (Date.now() - ragStartTime) / 1000;
        this.logger.log(`RAG context built in ${ragTime}s`);
      }

      // Build the system prompt for course generation
      console.log('course', course);
      const systemPrompt = this.buildCourseGenerationPrompt(course, ragContext.content);

      // Generate course outline using AI
      const result = await this.aiSdk.generateText({
        prompt: course.inputs.prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'course-generation',
        identifierValue: 'course-outline',
      });

      // Parse the course outline from AI response
      const courseOutline = this.parseCourseOutline(result.text);
      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.log(`Course outline generation completed in ${processingTime}s`);

      // Generate images for course cover and chapters in parallel (if requested)
      const imageGenerationStartTime = Date.now();
      const shouldGenerateCoverImage = course.inputs.generateCoverImage !== false; // default to true
      const shouldGenerateChapterImages = course.inputs.generateChapterImages !== false; // default to true

      const [coverImage, chapterImages] = await Promise.all([
        shouldGenerateCoverImage
          ? this.generateCoverImage(courseOutline.title, courseOutline.description)
          : Promise.resolve(''),
        shouldGenerateChapterImages
          ? this.generateChapterImages(courseOutline.chapters, courseOutline.title)
          : Promise.resolve(courseOutline.chapters.map(() => '')),
      ]);
      const imageGenerationTime = (Date.now() - imageGenerationStartTime) / 1000;
      this.logger.log(
        `Image generation completed in ${imageGenerationTime}s (cover: ${shouldGenerateCoverImage}, chapters: ${shouldGenerateChapterImages})`,
      );

      // Calculate total cost
      const totalCost = this.calculateTotalCost(
        coverImage,
        chapterImages,
        shouldGenerateCoverImage,
        shouldGenerateChapterImages,
      );

      // Create chapters with images
      const chapters: CourseChapter[] = courseOutline.chapters.map((chapter, index) => ({
        title: chapter.title,
        description: chapter.description,
        chapterImage: chapterImages[index] || '',
        ...(chapter.topics && { topics: chapter.topics }),
      }));

      // Create response
      const response: CourseGenerationResponse = {
        coverImage: coverImage || '',
        courseType: course.type,
        title: courseOutline.title,
        description: courseOutline.description,
        chapters,
        totalChapters: chapters.length,
        totalCost,
      };

      const totalTime = (Date.now() - startTime) / 1000;
      this.logger.log(
        `Course generation completed for team: ${teamId}, courseId: ${String(course._id)} in ${totalTime}s, total cost: $${totalCost.toFixed(6)}`,
      );
      return response;
    } catch (error) {
      this.logger.error(`Error generating course for team ${teamId}:`, error);
      throw error;
    }
  }

  private buildCourseGenerationPrompt(course: Course, context: string): string {
    let systemPrompt = `You are an expert curriculum designer and educational content creator.

Your task is to generate a comprehensive course outline based on the following specifications:

Course Type: ${course.type} (${this.getCourseTypeDescription(course.type)})
Target Audience: ${course.audience.join(', ')}
Number of Chapters: ${course.inputs.numberOfChapters}

Requirements:
1. Create a ${course.type} course that is appropriate for: ${course.audience.join(', ')}
2. Generate exactly ${course.inputs.numberOfChapters} chapters
3. Each chapter should be substantial and cover important topics
4. Ensure logical progression from basic to advanced concepts
5. Make the course engaging and educational
6. Tailor the content and complexity level based on the course type (${course.type})`;

    // Add context if available
    if (context) {
      systemPrompt += `\n\nAvailable Context you should use to generate the course delimited by <context> and </context>:\n<context>${context}</context>`;
    }

    // Build the JSON format based on whether topics are requested
    const chapterFormat = course.inputs.generateTopics
      ? `{
      "title": "Chapter 1 Title",
      "description": "Detailed description of what this chapter covers, learning objectives, and key concepts students will master",
      "topics": ["Topic 1", "Topic 2", "Topic 3", "Topic 4", "Topic 5"]
    }`
      : `{
      "title": "Chapter 1 Title",
      "description": "Detailed description of what this chapter covers, learning objectives, and key concepts students will master"
    }`;

    systemPrompt += `\n\nOutput Requirements:
You must respond with a valid JSON object in the following format:
{
  "title": "Course Title Here",
  "description": "Comprehensive course description explaining what students will learn, the course objectives, and who it's designed for",
  "chapters": [
    ${chapterFormat},
    ${chapterFormat.replace('Chapter 1', 'Chapter 2')}
    // Continue for all ${course.inputs.numberOfChapters} chapters
  ]
}

Important:
- The course title should be engaging and clearly indicate the subject matter and course type (${course.type})
- The course description should be 2-3 sentences explaining the course scope and objectives
- Each chapter description should be detailed (2-3 sentences) explaining specific learning outcomes${
      course.inputs.generateTopics
        ? '\n- Each chapter must include exactly 5 topics as an array of strings that cover the key learning points'
        : ''
    }
- Ensure the JSON format is valid and can be parsed
- Do not include any text outside the JSON object
- Make sure to include exactly ${course.inputs.numberOfChapters} chapters`;

    return systemPrompt;
  }

  private getCourseTypeDescription(courseType: CourseType): string {
    switch (courseType) {
      case CourseType.OnBoarding:
        return 'introductory content to help new users get started';
      case CourseType.Training:
        return 'comprehensive skill development and practical learning';
      case CourseType.Tutorial:
        return 'step-by-step instructional content with hands-on examples';
      default:
        return 'educational content';
    }
  }

  private parseCourseOutline(aiResponse: string): CourseOutlineResponse {
    try {
      // Clean the response to extract JSON
      let jsonString = aiResponse.trim();

      // Remove any markdown code blocks if present
      if (jsonString.startsWith('```json')) {
        jsonString = jsonString.replace(/```json\n?/, '').replace(/\n?```$/, '');
      } else if (jsonString.startsWith('```')) {
        jsonString = jsonString.replace(/```\n?/, '').replace(/\n?```$/, '');
      }

      const parsed = JSON.parse(jsonString) as CourseOutlineResponse;

      // Validate the structure
      if (!parsed.title || !parsed.description || !Array.isArray(parsed.chapters)) {
        throw new Error('Invalid course outline structure');
      }

      return {
        title: parsed.title,
        description: parsed.description,
        chapters: parsed.chapters.map((chapter) => ({
          title: chapter.title || 'Untitled Chapter',
          description: chapter.description || 'Chapter description not provided',
          ...(chapter.topics && Array.isArray(chapter.topics) && { topics: chapter.topics }),
        })),
      };
    } catch (error) {
      this.logger.error('Failed to parse course outline from AI response:', error);
      this.logger.debug('AI Response:', aiResponse);

      // Fallback: create a basic structure
      return {
        title: 'Generated Course',
        description:
          'A comprehensive educational course designed to provide students with essential knowledge and skills.',
        chapters: Array.from({ length: 5 }, (_, i) => ({
          title: `Chapter ${i + 1}`,
          description: 'This chapter covers important concepts and learning objectives.',
          topics: [],
        })),
      };
    }
  }

  private async generateCoverImage(
    courseTitle: string,
    courseDescription: string,
  ): Promise<string> {
    try {
      const prompt = `Create a professional course cover image for "${courseTitle}". ${courseDescription}. The image should be educational, engaging, and represent the course content visually.`;

      const image = await this.imageGenerationService.generateImage(
        courseTitle,
        prompt,
        courseDescription,
        'General',
      );

      return image?.imageUrl || '';
    } catch (error) {
      this.logger.error('Failed to generate cover image:', error);
      return '';
    }
  }

  private async generateChapterImages(
    chapters: Array<{ title: string; description: string }>,
    courseTitle: string,
  ): Promise<string[]> {
    try {
      const imagePromises = chapters.map(async (chapter) => {
        const prompt = `Create an educational chapter image for "${chapter.title}" from the course "${courseTitle}". ${chapter.description}. The image should visually represent the chapter content and be engaging for learners.`;

        const image = await this.imageGenerationService.generateImage(
          chapter.title,
          prompt,
          chapter.description,
          'General',
        );

        return image?.imageUrl || '';
      });

      return await Promise.all(imagePromises);
    } catch (error) {
      this.logger.error('Failed to generate chapter images:', error);
      return chapters.map(() => '');
    }
  }

  private calculateTotalCost(
    coverImage: string,
    chapterImages: string[],
    shouldGenerateCoverImage: boolean,
    shouldGenerateChapterImages: boolean,
  ): number {
    // Base cost estimation for image generation
    // In a real implementation, this would be calculated based on actual service costs
    let totalCost = 0;

    // Cost for cover image (only if generated and successful)
    if (shouldGenerateCoverImage && coverImage) {
      totalCost += 0.04; // Estimated cost per image
    }

    // Cost for chapter images (only if generated and successful)
    if (shouldGenerateChapterImages) {
      totalCost += chapterImages.filter((img) => img).length * 0.04;
    }

    // Add small AI text generation cost
    totalCost += 0.01;

    return totalCost;
  }
}
