import { SchemaF<PERSON>y, Schema, Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';

import { BaseModel } from '@/resources/base/base.model';

import { CourseStatus, CourseType } from './course.enums';
import { CourseGenerationResponse } from './course.type';
import { Team } from '../team/team.model';

interface CourseChapter {
  title: string;
  content: string;
  topics: {
    title: string;
    content: string;
    type: 'text' | 'embed' | 'image' | 'video' | 'audio' | 'document';
  }[];
  attachments: {
    name: string;
    type: 'image' | 'video' | 'audio' | 'document';
    url: string;
  }[];
}

@Schema({ collection: 'courses', timestamps: true, versionKey: false })
export class Course extends BaseModel {
  @Prop({ type: String, enum: Object.values(CourseType), required: true })
  type!: CourseType;

  @Prop({ type: String })
  coverImage!: string;

  @Prop({ type: String })
  title!: string;

  @Prop({ type: String })
  description!: string;

  @Prop({ type: [String], default: [] })
  audience!: string[];

  @Prop({ type: [Object] })
  chapters!: CourseChapter[];

  @Prop({ type: String, enum: Object.values(CourseStatus), default: CourseStatus.Generating })
  status!: CourseStatus;

  // Keep User Inputs as Readonly Record
  @Prop({ type: Object, required: true })
  inputs!: {
    prompt: string;
    urls?: string[];
    fileUrls?: string[];
    webSearch?: boolean;
    numberOfChapters: number;
    useKnowledgeBase: boolean;
    generateTopics?: boolean;
    generateCoverImage?: boolean;
    generateChapterImages?: boolean;
  };

  // Keep Generated Outline & Cost as Readonly Record
  @Prop({ type: Object })
  generatedOutline!: CourseGenerationResponse;

  @Prop({ type: Number, default: 0 })
  generationCost!: number;

  // Relations
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Team' })
  team!: Relation<Team>;
}

const CourseSchema = SchemaFactory.createForClass(Course);
export { CourseSchema };
