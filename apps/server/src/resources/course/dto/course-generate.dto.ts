import {
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON>tring,
  IsArray,
  IsEnum,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

import { CourseType } from '../course.enums';

export class CourseGenerateDto {
  @ApiProperty({
    description: 'Type of course being generated',
    example: CourseType.OnBoarding,
    enum: CourseType,
  })
  @IsEnum(CourseType)
  type!: CourseType;

  @ApiProperty({
    description: 'User instructions for course generation',
    example:
      'Create a comprehensive mathematics course focused on practical applications with real-world examples',
  })
  @IsNotEmpty()
  @IsString()
  prompt!: string;

  @ApiProperty({
    description: 'Target audience for the course',
    example: 'High school students preparing for university-level mathematics',
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  audience!: string[];

  @ApiProperty({
    description: 'Number of chapters in the course',
    example: 8,
    minimum: 1,
    maximum: 20,
  })
  @IsNumber()
  @Min(1)
  @Max(20)
  @Type(() => Number)
  numberOfChapters!: number;

  @ApiProperty({
    description: 'Whether to use knowledge base for course generation',
    example: true,
  })
  @IsBoolean()
  useKnowledgeBase!: boolean;

  @ApiProperty({
    description: 'Whether to generate topics for each chapter',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  generateTopics?: boolean = true;

  @ApiPropertyOptional({
    description: 'Whether to generate cover image for the course',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  generateCoverImage?: boolean = true;

  @ApiPropertyOptional({
    description: 'Whether to generate images for each chapter',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  generateChapterImages?: boolean = true;

  @ApiPropertyOptional({
    type: [String],
    description: 'Optional file URLs to upload for temporary knowledge base',
    example: [
      'https://example.com/files/curriculum.pdf',
      'https://example.com/files/syllabus.docx',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fileUrls?: string[];

  @ApiPropertyOptional({
    type: [String],
    description: 'Optional URLs to extract content from for temporary knowledge base',
    example: ['https://example.com/educational-resource', 'https://wikipedia.org/wiki/Mathematics'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  urls?: string[];

  @ApiPropertyOptional({
    type: [String],
    description: 'Whether to use web search for course generation',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  webSearch?: boolean = false;
}
