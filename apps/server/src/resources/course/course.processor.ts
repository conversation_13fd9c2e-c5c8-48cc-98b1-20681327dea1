import type { CourseChapter } from './course.type';
import type { Course } from './course.model';
import type { Team } from '../team/team.model';
import type { Job } from 'bullmq';

import { WorkerHost, Processor } from '@nestjs/bullmq';

import { COURSE_GENERATION_QUEUE_NAME } from './course.constants';
import { CourseGenerationService } from './course-generation.service';
import { CourseService } from './course.service';
import { CourseStatus } from './course.enums';

@Processor(COURSE_GENERATION_QUEUE_NAME, { concurrency: 10 })
export class CourseProcessor extends WorkerHost {
  constructor(
    private readonly courseGenerationService: CourseGenerationService,
    private readonly courseService: CourseService,
  ) {
    super();
  }

  async process({ data: course }: Job<Course>): Promise<Course> {
    const generatedOutline = await this.courseGenerationService.generate(
      String((course.team as Team)._id),
      course,
    );

    const updatedCourse = await this.courseService.update(course._id, {
      coverImage: generatedOutline.coverImage,
      title: generatedOutline.title,
      description: generatedOutline.description,
      chapters: generatedOutline.chapters.map((chapter) => ({
        title: chapter.title,
        content: this.getContent(chapter),
        topics:
          chapter.topics?.map((topic) => ({
            title: topic,
            content: '',
            type: 'text',
          })) || [],
        attachments: [],
      })),
      generatedOutline: generatedOutline,
      generationCost: generatedOutline.totalCost,
      status: CourseStatus.Generated,
    });

    return updatedCourse;
  }

  private getContent(chapter: CourseChapter): string {
    return `
${chapter.chapterImage ? `<img src="${chapter.chapterImage}" />` : ''}
<p>${chapter.description}</p>
    `.trim();
  }
}
