import type { ControllerOptions } from '@/resources/base/base.types';
import type { User } from '../user/user.model';

import { Controller, UseGuards, Body, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

import { BaseController } from '@/resources/base/base.controller';
import { AccessGuard } from '@/auth/guards/access.guard';
import { UserRole } from '@/resources/user/user.enums';
import { ReqUser } from '@/common/decorators/req-user.decorator';
import { Auth } from '@/auth/guards/auth.guard';

import { COURSE_GENERATION_QUEUE_NAME } from './course.constants';
import { CourseGenerateDto } from './dto/course-generate.dto';
import { CourseService } from './course.service';
import { Course } from './course.model';
import { Team } from '../team/team.model';

const options: Partial<ControllerOptions> = {
  list: { roles: [UserRole.Admin] },
  show: { roles: [UserRole.Admin] },
  create: { roles: [UserRole.Admin] },
  update: { roles: [UserRole.Admin] },
  remove: { roles: [UserRole.Admin] },
};

@Controller('courses')
export class CourseController extends BaseController<Course, typeof CourseService, null, null>({
  name: 'Course',
  path: 'courses',
  service: CourseService,
  options,
}) {
  constructor(
    @InjectQueue(COURSE_GENERATION_QUEUE_NAME) private readonly courseGenerationQueue: Queue,
    private readonly courseService: CourseService,
  ) {
    super();
  }

  @ApiOperation({
    summary: 'Generate a comprehensive course outline',
    description: `Generate a complete course outline with cover image and chapter details based on user specifications.

This endpoint:
- Uses teamId from authentication for knowledge base namespace
- Generates course outlines with specified number of chapters
- Supports different course types: onboarding, training, tutorial
- Optionally generates topics for each chapter when topicsForChapters is true
- Optionally creates cover image and chapter images using AI (both enabled by default)
- Optionally uses knowledge base content when useKnowledgeBase is true
- Returns structured course data with images, topics (if requested), and cost information

Example request:
{
  "courseType": "training",
  "prompt": "Create a comprehensive mathematics course focusing on practical applications with real-world examples",
  "audience": ["High school students preparing for university-level mathematics"],
  "numberOfChapters": 8,
  "useKnowledgeBase": true,
  "generateTopics": true,
  "generateCoverImage": true,
  "generateChapterImages": true,
  "fileUrls": ["https://example.com/files/curriculum.pdf"],
  "urls": ["https://example.com/math-resources"],
  "webSearch": true
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Course generated successfully',
    type: Course,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during course generation',
  })
  @Post('generate')
  @UseGuards(Auth, AccessGuard)
  async generateCourse(
    @Body() { type, audience, ...body }: CourseGenerateDto,
    @ReqUser() user: User,
  ): Promise<Course> {
    const course = await this.courseService.create({
      team: (user.team as Team)?._id,
      inputs: body,
      audience,
      type,
    });

    await this.courseGenerationQueue.add(COURSE_GENERATION_QUEUE_NAME, course);

    return course;
  }
}
