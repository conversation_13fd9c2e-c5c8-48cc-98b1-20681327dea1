import { BullBoardModule } from '@bull-board/nestjs';
import { MongooseModule } from '@nestjs/mongoose';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';

import { ImageGenerationService } from '@/img/services/image-generation.service';
import { RagModule } from '@/modules/rag/rag.module';
import { LlmModule } from '@/llm/llm.module';

import {
  COURSE_GENERATION_QUEUE_JOB_OPTIONS,
  COURSE_GENERATION_QUEUE_NAME,
} from './course.constants';
import { CourseGenerationService } from './course-generation.service';
import { CourseSchema, Course } from './course.model';
import { CourseController } from './course.controller';
import { CourseProcessor } from './course.processor';
import { CourseService } from './course.service';

@Module({
  imports: [
    BullModule.registerQueue({
      defaultJobOptions: COURSE_GENERATION_QUEUE_JOB_OPTIONS,
      name: COURSE_GENERATION_QUEUE_NAME,
    }),
    BullBoardModule.forFeature({ name: COURSE_GENERATION_QUEUE_NAME, adapter: BullMQAdapter }),
    MongooseModule.forFeature([{ schema: CourseSchema, name: Course.name }]),
    RagModule,
    LlmModule,
  ],
  controllers: [CourseController],
  providers: [CourseGenerationService, ImageGenerationService, CourseProcessor, CourseService],
  exports: [CourseService],
})
export class CourseModule {}
