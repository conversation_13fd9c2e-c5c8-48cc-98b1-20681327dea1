import type { ControllerOptions } from './base.types';

import { AccessGuard } from '@/auth/guards/access.guard';
import { UserRole } from '@/resources/user/user.enums';
import { Auth } from '@/auth/guards/auth.guard';

export const DEFAULT_CONTROLLER_OPTIONS: ControllerOptions = {
  isTeamResource: true,
  list: {
    enabled: true,
    guards: [Auth, AccessGuard],
    roles: [UserRole.Admin],
    cacheEnabled: true,
  },
  show: {
    enabled: true,
    guards: [Auth, AccessGuard],
    roles: [UserRole.Admin],
    cacheEnabled: true,
  },
  create: {
    enabled: true,
    guards: [Auth, AccessGuard],
    roles: [UserRole.Admin],
  },
  update: {
    enabled: true,
    guards: [Auth, AccessGuard],
    roles: [UserRole.Admin],
  },
  remove: {
    enabled: true,
    guards: [Auth, AccessGuard],
    roles: [UserRole.Admin],
  },
};
