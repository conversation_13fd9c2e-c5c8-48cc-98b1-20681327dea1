import type { RootFilterQuery, Model } from 'mongoose';
import type { ObjectId } from 'mongodb';

import { mixin } from '@nestjs/common';

export function BaseService<Resource>() {
  class BaseServiceMixin {
    public model: Model<Resource>;

    constructor(model: Model<Resource>) {
      this.model = model;
    }

    async findMany(
      filter: RootFilterQuery<Resource>,
      options?: Partial<Omit<MongoParams<Resource>, 'filter'>>,
    ): Promise<Resource[]> {
      const query = this.model.find(filter);

      (Object.keys(options || {}) as (keyof MongoParams<Resource>)[]).forEach((key) => {
        if (options?.[key]) {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
          query[key](options[key]);
        }
      });

      return query.exec();
    }

    async findOne(
      filter: RootFilterQuery<Resource>,
      options?: Partial<Pick<MongoParams<Resource>, 'sort' | 'populate'>>,
    ): Promise<Resource | null> {
      const query = this.model.findOne(filter);

      (Object.keys(options || {}) as (keyof MongoParams<Resource>)[]).forEach((key) => {
        if (options?.[key]) {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
          query[key](options[key]);
        }
      });

      return query.exec();
    }

    async findById(
      id: string | ObjectId,
      options?: Partial<Pick<MongoParams<Resource>, 'filter' | 'populate'>>,
    ): Promise<Resource> {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      const query = this.model.findById(id, null, options?.filter ? options.filter : {});

      if (options?.populate?.length) {
        query.populate(options.populate);
      }

      return (await query.exec()) as Resource;
    }

    async count(filter: RootFilterQuery<Resource>): Promise<number> {
      return this.model.countDocuments(filter).exec();
    }

    async create(data: Partial<Resource>): Promise<Resource> {
      const createdItem = new this.model(data);
      return (await createdItem.save()) as Resource;
    }

    async update(id: string | ObjectId, data: Partial<Resource>): Promise<Resource> {
      return (await this.model
        .findByIdAndUpdate(id, data, { returnDocument: 'after' })
        .exec()) as Resource;
    }

    async updateOne(filter: RootFilterQuery<Resource>, data: Partial<Resource>): Promise<Resource> {
      return (await this.model
        .findOneAndUpdate(filter, data, { returnDocument: 'after' })
        .exec()) as Resource;
    }

    async delete(id: string | ObjectId): Promise<{ success: boolean }> {
      await this.update(id, { deleted: true } as Resource);
      return { success: true };
    }

    async deleteOne(filter: RootFilterQuery<Resource>): Promise<{ success: boolean }> {
      await this.updateOne(filter, { deleted: true } as Resource);
      return { success: true };
    }

    async destroy(id: string | ObjectId): Promise<{ success: boolean }> {
      await this.model.findByIdAndDelete(id, { deleted: true });
      return { success: true };
    }
  }

  const service = mixin(BaseServiceMixin);
  return service;
}
