import type { TestingModule } from '@nestjs/testing';

import { MongooseModule } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import mongoose from 'mongoose';

import { UserService } from '@/resources/user/user.service';
import { Users } from '@/test/seeds';
import config from '@/common/configs/config';

import { UserSchema, User } from '@/resources/user/user.model';

describe('BaseService', () => {
  let userService: UserService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(config().mongo.url),
        MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
      ],
      providers: [UserService],
    }).compile();

    userService = module.get<UserService>(UserService);
  });

  afterAll(async () => {
    await module.close();
    await mongoose.disconnect();
  });

  it('should fetch many users from db', async () => {
    expect(userService).toBeDefined();
    const users = await userService.findMany({}, { limit: 2 });
    expect(Array.isArray(users)).toBeTruthy();
    expect(users.length).toBe(2);
  });

  it('should fetch single user from db', async () => {
    const user = await userService.findOne({ _id: Users.Admin });
    expect(user).toBeDefined();
    expect(user?._id.toString()).toBe(Users.Admin.toString());
    expect(user?.name).toBe('Jane Smith');
  });

  it('should update user in db', async () => {
    const user1 = await userService.findOne({ _id: Users.Owner });
    expect(user1?.name).toBe('John Doe');
    await userService.update(Users.Owner, { name: 'Johnny Doe' });
    const user2 = await userService.findOne({ _id: Users.Owner });
    expect(user2?.name).toBe('Johnny Doe');
    await userService.update(Users.Owner, { name: 'John Doe' });
  });
});
