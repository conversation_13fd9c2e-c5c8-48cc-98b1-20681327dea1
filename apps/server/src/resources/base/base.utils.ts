import type { FilterQuery as RootFilterQuery } from 'mongoose';
import type { ControllerOptions, CrudQuery } from './base.types';

import { ObjectId } from 'mongodb';

import { DEFAULT_CONTROLLER_OPTIONS } from './base.constants';

function recursivelyCastObjectIds<T>(obj: T): T {
  if (Array.isArray(obj)) {
    return obj.map(recursivelyCastObjectIds) as T;
  }

  if (obj !== null && typeof obj === 'object') {
    const cloned = { ...(obj as Record<string, unknown>) };

    for (const key in cloned) {
      const val = cloned[key];

      if (Array.isArray(val) || (val !== null && typeof val === 'object')) {
        cloned[key] = recursivelyCastObjectIds(val);
      } else if (typeof val === 'string' && ObjectId.isValid(val)) {
        cloned[key] = new ObjectId(val);
      }
    }

    return cloned as T;
  }

  return obj;
}

function parseFilter<Resource = unknown>(s = ''): RootFilterQuery<Resource> {
  const { $and, $or } = JSON.parse(s || '{}') as {
    $and?: RootFilterQuery<Resource>[];
    $or?: RootFilterQuery<Resource>[];
  };

  return {
    ...($and?.length ? { $and: recursivelyCastObjectIds($and) } : {}),
    $or: recursivelyCastObjectIds([{ deleted: false }, { deleted: null }, ...($or || [])]),
  } as RootFilterQuery<Resource>;
}

const parseSort = <Resource>(sorts: string[] = ['createdAt,DESC']): MongoSort<Resource> => {
  return sorts.reduce((_sort: MongoSort<Resource>, _s: string) => {
    const [field, order] = _s.split(',');
    _sort[field as keyof MongoSort<Resource>] = order === 'DESC' ? -1 : 1;
    return _sort;
  }, {} as MongoSort<Resource>);
};

export const parseQuery = <Resource>(query: CrudQuery): MongoParams<Resource> => {
  const { join, page, s } = query;

  const filter = parseFilter<Resource>(s);
  const sort = parseSort<Resource>(query.sort);

  // Populate
  const populate = join?.length ? join : [];

  // Pagination
  const limit = query.limit ? +query.limit : 10;
  const skip = limit * ((page ? +page : 1) - 1);

  return {
    filter,
    sort,
    populate,
    limit,
    skip,
  };
};

export const getOptions = (options: Partial<ControllerOptions> = {}): ControllerOptions => ({
  ...DEFAULT_CONTROLLER_OPTIONS,
  ...options,
  list: { ...DEFAULT_CONTROLLER_OPTIONS.list, ...(options.list || {}) },
  show: { ...DEFAULT_CONTROLLER_OPTIONS.show, ...(options.show || {}) },
  create: { ...DEFAULT_CONTROLLER_OPTIONS.create, ...(options.create || {}) },
  update: { ...DEFAULT_CONTROLLER_OPTIONS.update, ...(options.update || {}) },
  remove: { ...DEFAULT_CONTROLLER_OPTIONS.remove, ...(options.remove || {}) },
});
