/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
import type { BaseServiceInterface, ControllerOptions } from './base.types';
import type { InjectionToken } from '@nestjs/common';
import type { Team } from '@/resources/team/team.model';
import type { User } from '@/resources/user/user.model';

import {
  ForbiddenException,
  NotFoundException,
  UseInterceptors,
  Controller,
  UseGuards,
  Headers,
  Delete,
  Inject,
  mixin,
  Param,
  Patch,
  Query,
  Body,
  Post,
  Get,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

import { NoopInterceptor } from '@/common/interceptors/noop.interceptor';
import { MILLISECONDS_IN } from '@ps/common/constants/time';
import { User<PERSON>ole } from '@/resources/user/user.enums';
import { ReqUser } from '@/common/decorators/req-user.decorator';
import { Roles } from '@/auth/guards/access.guard';
import config from '@/common/configs/config';

import { getOptions, parseQuery } from './base.utils';
import { QueryDto } from './dto/query.dto';

export function BaseController<Resource, Service, CreateDto, UpdateDto>({
  name,
  path,
  service,
  options,
}: {
  name: string;
  path: string;
  service: Service;
  CreateResource?: CreateDto | null;
  UpdateResource?: UpdateDto | null;
  options?: Partial<ControllerOptions>;
}) {
  const { isTeamResource, pathParams, list, show, create, update, remove } = getOptions(options);

  @Controller(path)
  class BaseControllerMixin {
    @Inject(service as unknown as InjectionToken) baseService!: BaseServiceInterface<Resource>;

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} list${list.enabled ? '' : '; Disabled ❌'}` })
    @Get()
    @CacheTTL(() => (config().isProd ? MILLISECONDS_IN.FIFTEEN_MINUTES : 1))
    @UseInterceptors(list.cacheEnabled && !isTeamResource ? CacheInterceptor : NoopInterceptor)
    @Roles(...(list.roles || []))
    @UseGuards(...(list.guards || []))
    async list(
      @Query() query: QueryDto,
      @ReqUser() user: User,
      @Headers() headers: { 'x-app-name': 'admin' },
      @Param() params: Record<string, string>,
    ) {
      if (!list.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const { filter, ...filterOptions } = parseQuery<Resource>(query);

      const isRequestFromAdmin =
        user?.role === UserRole.SuperAdmin && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.team) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.team = (user.team as Team)?._id || user.team;
      }

      if (pathParams?.length) {
        pathParams.forEach((param) => (filter[param] = params[param]));
      }

      const [data, total] = await Promise.all([
        this.baseService.findMany(filter, filterOptions),
        this.baseService.count(filter),
      ]);

      return { data, total };
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} show${show.enabled ? '' : '; Disabled ❌'}` })
    @Get(':id')
    @CacheTTL(() => (config().isProd ? MILLISECONDS_IN.FIFTEEN_MINUTES : 1))
    @UseInterceptors(show.cacheEnabled && !isTeamResource ? CacheInterceptor : NoopInterceptor)
    @Roles(...(show.roles || []))
    @UseGuards(...(show.guards || []))
    async show(
      @Query() query: QueryDto,
      @Param('id') id: string,
      @ReqUser() user: User,
      @Headers() headers: { 'x-app-name': 'admin' },
      @Param() params: Record<string, string>,
    ) {
      if (!show.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const { filter, populate } = parseQuery<Resource>(query);

      const isRequestFromAdmin =
        user?.role === UserRole.SuperAdmin && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.team) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.team = (user.team as Team)?._id || user.team;
      }

      if (pathParams?.length) {
        pathParams.forEach((param) => (filter[param] = params[param]));
      }

      const result = await this.baseService.findById(id, { filter, populate });
      if (!result) {
        throw new NotFoundException('Not Found');
      }

      return result;
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} create${create.enabled ? '' : '; Disabled ❌'}` })
    @Post()
    @Roles(...(create.roles || []))
    @UseGuards(...(create.guards || []))
    create(
      // @ts-expect-error for now
      @Body() data: CreateResource,
      @ReqUser() user: User,
      @Headers() headers: { 'x-app-name': 'admin' },
      @Param() params: Record<string, string>,
    ) {
      if (!create.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const isRequestFromAdmin =
        user?.role === UserRole.SuperAdmin && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.team) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        data.team = (user.team as Team)?._id || user.team;
      }

      if (pathParams?.length) {
        pathParams.forEach((param) => (data[param] = params[param]));
      }

      return this.baseService.create(data);
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} update${update.enabled ? '' : '; Disabled ❌'}` })
    @Patch(':id')
    @Roles(...(update.roles || []))
    @UseGuards(...(update.guards || []))
    update(
      @Param() { id }: { id: string },
      // @ts-expect-error for now
      @Body() data: UpdateResource,
      @ReqUser() user: User,
      @Headers() headers: { 'x-app-name': 'admin' },
      @Param() params: Record<string, string>,
    ) {
      if (!update.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const filter: Record<string, unknown> = { _id: id };
      const isRequestFromAdmin =
        user?.role === UserRole.SuperAdmin && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.team) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.team = (user.team as Team)?._id || user.team;
      }

      if (pathParams?.length) {
        pathParams.forEach((param) => (filter[param] = params[param]));
      }

      return this.baseService.updateOne(filter, data);
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} delete${remove.enabled ? '' : '; Disabled ❌'}` })
    @Delete(':id')
    @Roles(...(remove.roles || []))
    @UseGuards(...(remove.guards || []))
    delete(
      @Param() { id }: { id: string },
      @ReqUser() user: User,
      @Headers() headers: { 'x-app-name': 'admin' },
      @Param() params: Record<string, string>,
    ) {
      if (!remove.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const filter: Record<string, unknown> = { _id: id };
      const isRequestFromAdmin =
        user?.role === UserRole.SuperAdmin && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.team) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.team = (user.team as Team)?._id || user.team;
      }

      if (pathParams?.length) {
        pathParams.forEach((param) => (filter[param] = params[param]));
      }

      return this.baseService.deleteOne(filter);
    }
  }

  const controller = mixin(BaseControllerMixin);
  return controller;
}
