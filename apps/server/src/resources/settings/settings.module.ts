import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { SettingsSchema, Settings } from './settings.model';
import { SettingsController } from './settings.controller';
import { SettingsService } from './settings.service';

@Module({
  imports: [MongooseModule.forFeature([{ schema: SettingsSchema, name: Settings.name }])],
  controllers: [SettingsController],
  providers: [SettingsService],
  exports: [SettingsService, MongooseModule],
})
export class SettingsModule {}
