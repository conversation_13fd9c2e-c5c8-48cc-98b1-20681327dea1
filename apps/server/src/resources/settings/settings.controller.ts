import type { ControllerOptions } from '@/resources/base/base.types';
import type { Settings } from './settings.model';

import { BaseController } from '@/resources/base/base.controller';
import { UserRole } from '@/resources/user/user.enums';

import { SettingsService } from './settings.service';

const options: Partial<ControllerOptions> = {
  isTeamResource: false,
  list: { roles: [UserRole.SuperAdmin] },
  show: { roles: [UserRole.SuperAdmin] },
  create: { roles: [UserRole.SuperAdmin] },
  update: { roles: [UserRole.SuperAdmin] },
  remove: { roles: [UserRole.SuperAdmin] },
};

export class SettingsController extends BaseController<
  Settings,
  typeof SettingsService,
  null,
  null
>({
  name: 'Settings',
  path: 'settings',
  service: SettingsService,
  options,
}) {}
