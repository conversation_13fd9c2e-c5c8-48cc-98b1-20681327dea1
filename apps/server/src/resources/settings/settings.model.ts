import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';
import { BaseModel } from '../base/base.model';

@Schema({ collection: 'settings', timestamps: true, versionKey: false })
export class Settings extends BaseModel {
  @Prop({ type: String, required: true })
  key!: string;

  @Prop({ type: String, required: true })
  type!: 'text' | 'number' | 'boolean' | 'date' | 'tag' | 'json';

  @Prop({ type: MongooseSchema.Types.Mixed, required: true })
  value!: unknown;
}

const SettingsSchema = SchemaFactory.createForClass(Settings);
export { SettingsSchema };
