import type { ControllerOptions } from '@/resources/base/base.types';
import type { Category } from './category.model';

import { BaseController } from '@/resources/base/base.controller';
import { UserRole } from '@/resources/user/user.enums';

import { CategoryCreateDto } from './dto/category-create.dto';
import { CategoryUpdateDto } from './dto/category-update.dto';
import { CategoryService } from './category.service';

const options: Partial<ControllerOptions> = {
  isTeamResource: false,
  list: { roles: [UserRole.User] },
  show: { roles: [UserRole.SuperAdmin] },
  create: { roles: [UserRole.SuperAdmin] },
  update: { roles: [UserRole.SuperAdmin] },
  remove: { roles: [UserRole.SuperAdmin] },
};

export class CategoryController extends BaseController<
  Category,
  typeof CategoryService,
  CategoryCreateDto,
  CategoryUpdateDto
>({
  name: 'Category',
  path: 'categories',
  service: CategoryService,
  options,
}) {}
