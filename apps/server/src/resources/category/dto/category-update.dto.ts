import { IsOptional, IsString, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

import { CategoryType } from '../category.enums';

export class CategoryUpdateDto {
  @ApiPropertyOptional({
    description: 'Category name',
    example: 'Technology',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Category type',
    example: 'TBA',
  })
  @IsOptional()
  @IsEnum(CategoryType)
  type?: CategoryType;
}
