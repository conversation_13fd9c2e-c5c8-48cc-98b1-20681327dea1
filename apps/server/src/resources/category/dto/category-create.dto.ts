import { IsNotEmpty, IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { CategoryType } from '../category.enums';

export class CategoryCreateDto {
  @ApiProperty({
    description: 'Category name',
    example: 'Technology',
  })
  @IsNotEmpty()
  @IsString()
  name!: string;

  @ApiProperty({
    description: 'Category type',
    example: 'TBA',
  })
  @IsNotEmpty()
  @IsEnum(CategoryType)
  type!: CategoryType;
}
