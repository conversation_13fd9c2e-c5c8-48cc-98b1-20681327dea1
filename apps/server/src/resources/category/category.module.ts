import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { CategorySchema, Category } from './category.model';
import { CategoryController } from './category.controller';
import { CategoryService } from './category.service';

@Module({
  imports: [MongooseModule.forFeature([{ schema: CategorySchema, name: Category.name }])],
  controllers: [CategoryController],
  providers: [CategoryService],
  exports: [CategoryService, MongooseModule],
})
export class CategoryModule {}
