import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';

import { BaseModel } from '@/resources/base/base.model';

import { CategoryType } from './category.enums';

@Schema({ collection: 'categories', timestamps: true, versionKey: false })
export class Category extends BaseModel {
  @Prop({ type: String, required: true })
  name!: string;

  @Prop({ type: String, enum: Object.values(CategoryType), required: true })
  type!: CategoryType;

  @Prop({ type: Number, default: 0 })
  sequence!: number;
}

const CategorySchema = SchemaFactory.createForClass(Category);
export { CategorySchema };
