import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { BaseModel } from '@/resources/base/base.model';

import { SubscriptionStatus, SubscriptionPlan, BillingPeriod } from './team.enums';

@Schema({ timestamps: true, versionKey: false })
export class Team extends BaseModel {
  @Prop({ type: String })
  name!: string;

  // Stripe Information
  @Prop({ type: String })
  stripeCustomerId?: string;

  // Subscription Information
  @Prop({ type: String, enum: Object.values(BillingPeriod) })
  billingPeriod?: BillingPeriod;

  @Prop({ type: String, enum: Object.values(SubscriptionPlan), default: SubscriptionPlan.NoPlan })
  subscriptionPlan!: SubscriptionPlan;

  @Prop({ type: String, enum: Object.values(SubscriptionStatus) })
  subscriptionStatus?: SubscriptionStatus;

  @Prop({ type: String })
  stripeSubscriptionId?: string | null;

  @Prop({ type: Date })
  subscriptionStartDate?: Date;

  @Prop({ type: Date })
  subscriptionEndDate?: Date;

  @Prop({ type: Date })
  trialEndDate?: Date;

  @Prop({ type: Boolean, default: false })
  cancelAtPeriodEnd?: boolean;
}

const TeamSchema = SchemaFactory.createForClass(Team);
export { TeamSchema };
