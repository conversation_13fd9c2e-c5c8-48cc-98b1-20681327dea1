import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { TeamSchema, Team } from './team.model';
import { TeamController } from './team.controller';
import { TeamService } from './team.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: Team.name, schema: TeamSchema }])],
  controllers: [TeamController],
  providers: [TeamService],
  exports: [MongooseModule, TeamService],
})
export class TeamModule {}
