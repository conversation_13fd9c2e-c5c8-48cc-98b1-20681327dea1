import { Logger } from '@nestjs/common';

import { SlackService } from '@/integrations/slack/slack.service';

export function setupProcessErrorHandlers(slackService: SlackService) {
  const logger = new Logger('ProcessErrorHandler');

  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', error.stack);
    void sendCrashToSlack('Uncaught Exception', error, slackService);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason: Error) => {
    logger.error('Unhandled Rejection', reason?.stack || reason);
    void sendCrashToSlack('Unhandled Rejection', reason, slackService);
    process.exit(1);
  });

  process.on('exit', (code) => {
    logger.warn(`Process exiting with code ${code}`);
  });
}

async function sendCrashToSlack(title: string, error: Error, slackService: SlackService) {
  const logger = new Logger('ProcessErrorHandler');
  try {
    await slackService.sendMessage({
      channel: 'demo',
      text: `${title}: ${error?.message || error}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*${title}*\n\`\`\`${error?.stack || JSON.stringify(error)}\`\`\``,
          },
        },
      ],
    });
  } catch (err) {
    logger.error('Failed to send Slack message:', err);
  }
}
