import type { NestExpressApplication } from '@nestjs/platform-express';
import type { NestApplicationOptions } from '@nestjs/common';

import { ValidationPipe, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import compression from 'compression';
import qs from 'qs';
import * as path from 'path';
import * as fs from 'fs';

import { generateOpenAPISpec } from '@/open-api-docs';
import { SlackService } from '@/integrations/slack/slack.service';
import { AppModule } from '@/app.module';
import config from '@/common/configs/config';

import { setupProcessErrorHandlers } from './process-error-handler';

const getHttpsOptions = (): NestApplicationOptions => {
  if (!config().nest.https) return {};

  const projectRoot = path.resolve(process.cwd(), '../../');
  const certsPath = path.join(projectRoot, 'certs');

  const keyPath = path.join(certsPath, 'localhost-key.pem');
  const certPath = path.join(certsPath, 'localhost.pem');

  if (config().isDev && (!fs.existsSync(keyPath) || !fs.existsSync(certPath))) {
    console.error(
      '\u001b[31m \u274C Certificate files (localhost-key.pem and localhost.pem) not found in the certs directory. Please install the certificate files before running the server in the development environment. \u001b[0m',
    );
    return process.exit(1);
  }

  const httpsOptions = config().isDev
    ? {
        httpsOptions: {
          key: fs.readFileSync(path.join(certsPath, 'localhost-key.pem')),
          cert: fs.readFileSync(path.join(certsPath, 'localhost.pem')),
        },
      }
    : {};

  return httpsOptions;
};

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    ...getHttpsOptions(),
    rawBody: true,
  });

  // App Settings
  app.set('query parser', (str: string) => {
    return qs.parse(str, {
      parseArrays: true,
      arrayLimit: 100,
      allowDots: true,
    });
  });

  // Validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: false, // TODO: Remove this after fixing admin
    }),
  );

  // Middlewares
  app.useBodyParser('json', { limit: '50mb' });
  app.useBodyParser('urlencoded', { extended: true, limit: '50mb' });
  app.use(compression());

  // Swagger
  generateOpenAPISpec(app);

  // CORS
  if (config().nest.cors.enabled) {
    app.enableCors({
      origin: [
        new RegExp(`^https://([a-zA-Z0-9-]+\\.)*${config().app.domain}$`),
        /.*localhost.*/,
        /127\.0\.0\.1/,
        /192.168/,
      ],
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      credentials: true,
      maxAge: 86400,
    });
  }

  // Process Error Handlers
  const slackService = app.get(SlackService);
  setupProcessErrorHandlers(slackService);

  // Shutdown Hooks
  if (config().isProd) {
    app.enableShutdownHooks();
  }

  await app.listen(config().nest.port);

  new Logger('NestApplication').log(
    `✔️  ${config().app.name} server is running on: http${config().nest.https ? 's' : ''}://localhost:${config().nest.port}/`,
  );
}

void bootstrap();
