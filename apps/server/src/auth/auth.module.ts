import { PassportModule } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { SlackModule } from '@/integrations/slack/slack.module';
import { TeamModule } from '@/resources/team/team.module';
import { UserModule } from '@/resources/user/user.module';

import { IPGeolocationGuard } from './guards/ip-geolocation.guard';
import { GoogleController } from './google.controller';
import { AuthController } from './auth.controller';
import { GoogleStrategy } from './strategies/google.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { EmailGuard } from './guards/email.guard';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    SlackModule,
    TeamModule,
    UserModule,
  ],
  controllers: [GoogleController, AuthController],
  providers: [
    IPGeolocationGuard,
    GoogleStrategy,
    LocalStrategy,
    AuthService,
    JwtStrategy,
    EmailGuard,
    JwtService,
  ],
})
export class AuthModule {}
