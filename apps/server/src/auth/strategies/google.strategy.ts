import type { Profile } from 'passport-google-oauth20';
import type { User } from '@/resources/user/user.model';

import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { Strategy } from 'passport-google-oauth20';

import config from '@/common/configs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor() {
    super({
      clientID: config().google.clientId,
      clientSecret: config().google.clientSecret,
      callbackURL: `${config().app.server.url}/auth/google/callback`,
      scope: ['profile', 'email'],
    });
  }

  validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: (_: null, user: unknown) => void,
  ): void {
    const { sub, name, email, picture, email_verified } = profile._json;

    const user: Partial<User> & { accessToken: string; refreshToken: string } = {
      name,
      email: email?.toLowerCase(),
      profilePicture: picture,
      googleId: sub,
      verified: email_verified,

      accessToken,
      refreshToken,
    };

    done(null, user);
  }
}
