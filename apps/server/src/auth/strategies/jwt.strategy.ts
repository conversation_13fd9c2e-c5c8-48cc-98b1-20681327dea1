import type { User } from '@/resources/user/user.model';

import { UnauthorizedException, Injectable } from '@nestjs/common';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';

import { UserService } from '@/resources/user/user.service';
import config from '@/common/configs/config';

export interface JwtPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  iat?: number;
  exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: config().jwt.secret,
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: JwtPayload): Promise<User> {
    const { sub: userId } = payload;

    const user = await this.userService.findById(userId, { populate: ['team'] });
    if (!user) {
      throw new UnauthorizedException('User not found.');
    }
    if (!user.team) {
      throw new UnauthorizedException(`The user does not belong to any team.`);
    }

    req.user = user;

    return user;
  }
}
