import type { ExecutionContext } from '@nestjs/common';

import { UnauthorizedException, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

const ERROR_MESSAGE =
  'Sorry, the provided credentials are incorrect or you are not authorized to access this resource.';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {
  async canActivate(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest<Request>();
    if (!req.body?.email || !req.body?.password) {
      return true;
    }

    if (await super.canActivate(context)) {
      return true;
    } else {
      throw new UnauthorizedException(ERROR_MESSAGE);
    }
  }

  handleRequest<T>(err: unknown, user: T): T {
    if (err || !user) {
      throw new UnauthorizedException(ERROR_MESSAGE);
    }
    return user;
  }
}
