import type { ExecutionContext, CanActivate } from '@nestjs/common';

import { ForbiddenException, Injectable } from '@nestjs/common';

import config from '@/common/configs/config';

@Injectable()
export class EmailGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const { body } = request;

    if (body?.email && config().blockedDomains.some((e) => body?.email?.endsWith(e))) {
      throw new ForbiddenException(
        'The email domain is blacklisted. Please use a different email address.',
      );
    }

    return true;
  }
}
