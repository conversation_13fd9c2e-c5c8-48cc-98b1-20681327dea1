import type { ExecutionContext, CanActivate } from '@nestjs/common';
import type { Cache } from '@nestjs/cache-manager';

import { ForbiddenException, Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import geoip from 'geoip-lite';

import { MILLISECONDS_IN } from '@ps/common/constants/time';
import config from '@/common/configs/config';

type SignupAttempt = {
  ip: string;
  country: string | null;
  count: number;
  lastAttempt: string;
};

@Injectable()
export class IPGeolocationGuard implements CanActivate {
  private readonly logger = new Logger(IPGeolocationGuard.name);

  constructor(@Inject(CACHE_MANAGER) private distributedCache: Cache) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest<Request>();

    const ipAddress = (req.headers['x-forwarded-for'] || req.ip || req.connection.remoteAddress) as
      | string
      | string[];
    const ip = (Array.isArray(ipAddress) ? ipAddress[0] : ipAddress) as string;
    const geo = geoip.lookup(ip);

    // Add IP, city & country to the request object
    req.clientIp = ip;
    req.clientCity = geo?.city || null;
    req.clientCountry = geo?.country || null;

    if (geo && config().blockedCountries.includes(geo.country)) {
      return false;
    }

    if (config().blockedIPAddresses.includes(ip)) {
      return false;
    }

    if (ip && req.body?.email) {
      await this.checkSignupAttempts(ip, req.body?.email);
      await this.trackSignupAttempt(ip, req.clientCountry);
    }

    return true;
  }

  private async checkSignupAttempts(ip: string, email: string): Promise<void> {
    if (config().whitelistedDomains.some((s) => email.endsWith(s))) {
      return;
    }

    const cache = await this.distributedCache.get<string>(ip);
    if (cache) {
      const attempt = JSON.parse(cache) as SignupAttempt;

      if (attempt?.count >= 10) {
        this.logger.warn(`Blocked IP ${ip} & Email ${email} from signing up.`);
        throw new ForbiddenException('Maximum signup attempts reached for this IP.');
      }
    }
  }

  private async trackSignupAttempt(ip: string, country: string | null): Promise<void> {
    const cache = await this.distributedCache.get<string>(ip);
    let attempt = cache ? (JSON.parse(cache) as SignupAttempt) : null;

    if (attempt) {
      attempt.count += 1;
      attempt.lastAttempt = new Date().toISOString();
    } else {
      attempt = {
        ip,
        country,
        count: 1,
        lastAttempt: new Date().toISOString(),
      };
    }

    await this.distributedCache.set(ip, JSON.stringify(attempt), MILLISECONDS_IN.TWENTY_FOUR_HOURS);
  }
}
