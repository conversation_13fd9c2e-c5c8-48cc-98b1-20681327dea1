import type { <PERSON><PERSON> } from '@nestjs/cache-manager';
import type { User } from '@/resources/user/user.model';

import { ForbiddenException, NotFoundException, Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuid } from 'uuid';
import { compare } from 'bcrypt';

import { SlackService } from '@/integrations/slack/slack.service';
import { UserService } from '@/resources/user/user.service';
import { TeamService } from '@/resources/team/team.service';
import { MailService } from '@/modules/mail/mail.service';
import config from '@/common/configs/config';

import { SignupDto } from './dto/signup.dto';

@Injectable()
export class AuthService {
  constructor(
    @Inject(CACHE_MANAGER) private distributedCache: Cache,
    private readonly slackService: SlackService,
    private readonly teamService: TeamService,
    private readonly userService: UserService,
    private readonly mailService: MailService,
    private readonly jwtService: JwtService,
  ) {}

  async signup<S extends SignupDto>(signupDto: S, method: 'local' | 'google'): Promise<User> {
    const team = await this.teamService.create({ name: `${signupDto.name}'s Team` });

    const user = await this.userService.create({
      ...signupDto,
      team: team._id,
    });

    await this.slackService.sendSignupAlert(signupDto, method);
    delete user.password;

    return user;
  }

  async login(user: User): Promise<{ accessToken: string }> {
    const accessToken = await this.generateJwtToken(user);
    await this.userService.update(user._id, { lastLoginAt: new Date() });
    return { accessToken };
  }

  async forgotPassword(email: string) {
    const user = await this.userService.findOne({ email });

    if (!user) {
      throw new NotFoundException('No account found associated with this email.');
    }

    const resetId = uuid();
    await this.distributedCache.set(resetId, user.id, 6 * 60 * 60 * 1000);
    await this.mailService.addToMailQueue({
      name: user.name,
      to: user.email,
      subject: 'Password Reset',
      template: 'password-reset',
      context: {
        ctaUrl: `${config().app.client.url}/password/reset/${resetId}`,
      },
    });
  }

  async resetPassword(password: string, token: string) {
    const userId = await this.distributedCache.get<string>(token);
    if (!userId) return { notFound: true };

    void this.distributedCache.del(token);

    const user = await this.userService.findById(userId);
    if (!user) return { notFound: true };

    user.password = password;
    await user.save();

    if (!user) {
      throw new ForbiddenException(
        'The password reset link has expired. Please request a new one.',
      );
    }

    await this.mailService.addToMailQueue({
      name: user.name,
      to: user.email,
      subject: 'Password Reset Successful.',
      template: 'password-reset-success',
    });
  }

  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.userService.findOne({ email });

    if (user?.password) {
      const isPasswordMatches = await compare(password, user.password);
      if (isPasswordMatches) {
        delete user.password;
        return user;
      }
    }

    return null;
  }

  private async generateJwtToken(user: User): Promise<string> {
    const payload = {
      sub: user._id,
      email: user.email,
      teamId: user.team,
    };

    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: config().jwt.expiresIn,
      secret: config().jwt.secret,
    });

    return accessToken;
  }
}
