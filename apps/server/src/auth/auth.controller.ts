import type { User } from '@/resources/user/user.model';

import {
  InternalServerErrorException,
  BadRequestException,
  ForbiddenException,
  Controller,
  UseGuards,
  Logger,
  Body,
  Post,
  Req,
} from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';
import { ApiTags } from '@nestjs/swagger';

import { MILLISECONDS_IN } from '@ps/common/constants/time';
import { UserStatus } from '@/resources/user/user.enums';
import { ReqUser } from '@/common/decorators/req-user.decorator';

import { IPGeolocationGuard } from './guards/ip-geolocation.guard';
import { PasswordForgotDto } from './dto/password-forgot.dto';
import { PasswordResetDto } from './dto/password-reset.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { LocalSignupDto } from './dto/signup.dto';
import { AuthService } from './auth.service';
import { EmailGuard } from './guards/email.guard';
import { LoginDto } from './dto/login.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @Throttle({ default: { limit: 5, ttl: MILLISECONDS_IN.ONE_MINUTE } }) // 5 requests per minute
  @UseGuards(EmailGuard, IPGeolocationGuard)
  async signup(
    @Body() signupDto: LocalSignupDto,
    @Req() req: Request,
  ): Promise<{ accessToken: string }> {
    try {
      signupDto = {
        ...signupDto,
        ip: req.clientIp,
        city: req.clientCity,
        country: req.clientCountry,
      };

      const user = await this.authService.signup<LocalSignupDto>(signupDto, 'local');
      return await this.authService.login(user);
    } catch (error) {
      this.logger.error(`Signup failed for ${signupDto.email}`, (error as Error)?.message);
      if ((error as { code?: number })?.code === 11000) {
        throw new BadRequestException('Email Already exist.');
      } else {
        throw new InternalServerErrorException((error as Error)?.message);
      }
    }
  }

  @Post('login')
  @UseGuards(EmailGuard, LocalAuthGuard)
  async login(@Body() loginDto: LoginDto, @ReqUser() user: User): Promise<{ accessToken: string }> {
    try {
      if (user.status === UserStatus.Deactivated) {
        throw new ForbiddenException(
          'Your account has been deactivated. Please request your admin to reactivate your account.',
        );
      }

      return await this.authService.login(user);
    } catch (error) {
      this.logger.error(`Login failed for ${loginDto.email}`, (error as Error)?.message);
      throw new BadRequestException((error as Error)?.message);
    }
  }

  @Post('password/forgot')
  async forgotPassword(@Body() { email }: PasswordForgotDto) {
    await this.authService.forgotPassword(email.toLowerCase().trim());
    return { success: true };
  }

  @Post('password/reset')
  async resetPassword(@Body() { password, token }: PasswordResetDto) {
    await this.authService.resetPassword(password, token);
    return { success: true };
  }
}
