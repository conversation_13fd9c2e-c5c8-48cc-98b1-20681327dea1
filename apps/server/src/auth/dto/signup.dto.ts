import { <PERSON><PERSON>ption<PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>th, IsString, IsEmail } from 'class-validator';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class SignupDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name!: string;

  @ApiProperty()
  @IsString()
  @IsEmail()
  @Transform(({ value }: { value?: string }) => value?.toLowerCase())
  email!: string;
}

export class LocalSignupDto extends SignupDto {
  @ApiProperty()
  @IsNotEmpty()
  @MinLength(8)
  @IsString()
  password!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  ip?: string | null;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  city?: string | null;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  country?: string | null;
}

export class GoogleSignupDto extends SignupDto {
  @ApiHideProperty()
  @IsOptional()
  @IsString()
  profilePicture?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  googleId?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  verified?: boolean;
}
