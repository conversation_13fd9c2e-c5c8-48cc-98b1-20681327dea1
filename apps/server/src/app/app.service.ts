import type { OnApplicationShutdown } from '@nestjs/common';
import type { Queue } from 'bullmq';

import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';

import { MAIL_QUEUE_NAME } from '@/modules/mail/mail.constants';

@Injectable()
export class AppService implements OnApplicationShutdown {
  private readonly logger = new Logger(AppService.name);

  constructor(@InjectQueue(MAIL_QUEUE_NAME) private readonly mailQueue: Queue) {}

  async onApplicationShutdown(signal?: string): Promise<void> {
    this.logger.log({ signal }, 'Server is shutting down...');

    await this.mailQueue.close();

    this.logger.log({ signal }, 'Server shut down complete.');
  }
}
