import { Controller, UseGuards, Body, Post, Get } from '@nestjs/common';

import { UrlService } from '@/common/services/url.service';
import { S3Service } from '@/integrations/aws/s3.service';
import { Auth } from '@/auth/guards/auth.guard';

import { FileUploadDto } from './dto/file-upload.dto';
import { UrlMetaDto } from './dto/url-meta.dto';

@Controller()
export class AppController {
  constructor(
    private readonly urlService: UrlService,
    private readonly s3Service: S3Service,
  ) {}
  @Get()
  app() {
    return { status: 'Ok' };
  }

  @Get('health')
  health() {
    return { status: 'Ok' };
  }

  @UseGuards(Auth)
  @Post('upload')
  getPresignedPost(@Body() fileUploadDto: FileUploadDto) {
    return this.s3Service.getPresignedPost(fileUploadDto);
  }

  @UseGuards(Auth)
  @Post('url/meta')
  getUrlMeta(@Body() { url }: UrlMetaDto) {
    return this.urlService.getUrlMeta(url);
  }
}
