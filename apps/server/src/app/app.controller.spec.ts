import { TestingModule, Test } from '@nestjs/testing';
import { MongooseModule } from '@nestjs/mongoose';
import mongoose from 'mongoose';

import { AppController } from '@/app/app.controller';
import { AppService } from '@/app/app.service';
import { UrlService } from '@/common/services/url.service';
import { S3Service } from '@/integrations/aws/s3.service';
import config from '@/common/configs/config';

class MockAppService {
  async onApplicationShutdown() {}
}

describe('AppController', () => {
  let appController: AppController;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [MongooseModule.forRoot(config().mongo.url)],
      controllers: [AppController],
      providers: [{ provide: AppService, useClass: MockAppService }, UrlService, S3Service],
    }).compile();

    appController = module.get<AppController>(AppController);
  });

  afterAll(async () => {
    await module.close();
    await mongoose.disconnect();
  });

  describe('root', () => {
    it(`should return { status: 'Ok' }`, () => {
      const resp = appController.app();
      expect(resp.status).toEqual('Ok');
    });
  });

  describe('health', () => {
    it(`should return { status: 'Ok' }`, () => {
      const resp = appController.health();
      expect(resp.status).toEqual('Ok');
    });
  });

  describe('upload', () => {
    it(`should return s3 presigned post`, async () => {
      const resp = await appController.getPresignedPost({
        folderName: 'test',
        fileName: 'test.png',
        fileType: 'image/png',
        userRef: 'test',
      });
      expect(resp).toBeDefined();
      expect(resp.url).toBeDefined();
      expect(resp.fields).toBeDefined();
      expect(resp.fields.key).toBeDefined();
      expect(resp.fields.Policy).toBeDefined();
      expect(resp.fields['X-Amz-Date']).toBeDefined();
      expect(resp.fields['X-Amz-Algorithm']).toBeDefined();
      expect(resp.fields['X-Amz-Signature']).toBeDefined();
      expect(resp.fields['X-Amz-Credential']).toBeDefined();
      expect(resp.fields.ACL).toBe('public-read');
      expect(resp.fields.bucket).toBe(config().aws.buckets.images);
    });
  });

  describe('url/meta', () => {
    it(`should return url meta data`, async () => {
      const resp = await appController.getUrlMeta({ url: 'https://blogify.ai' });
      expect(resp).toBeDefined();
      expect(resp.title).toContain('Blogify');
      expect(resp.image).toContain('images.blogify.ai');
      expect(resp.description).toBeDefined();
    });
  });
});
