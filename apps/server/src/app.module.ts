import type { OnApplicationShutdown } from '@nestjs/common';

import { BullBoardModule } from '@bull-board/nestjs';
import { ThrottlerModule } from '@nestjs/throttler';
import { ExpressAdapter } from '@bull-board/express';
import { MongooseModule } from '@nestjs/mongoose';
import { Logger, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import basicAuth from 'express-basic-auth';

import { CommonServicesModule } from '@/common/services/common-services.module';
import { CacheModule } from '@/modules/cache/cache.module';
import { AuthModule } from '@/auth/auth.module';
import { MailModule } from '@/modules/mail/mail.module';
import { UrlService } from '@/common/services/url.service';
import { MeModule } from '@/me/me.module';
import config from '@/common/configs/config';

// AI Modules
import { ContentGenerationModule } from '@/modules/content-generation';
import { KnowledgeBaseModule } from '@/modules/knowledge-base/knowledge-base.module';
import { VoiceAgentModule } from '@/modules/voice-agent/voice-agent.module';
import { AiWizardsModule } from '@/modules/ai-wizards';
import { ImgModule } from '@/img';
import { RagModule } from '@/modules/rag/rag.module';
// Integrations
import { StripeModule } from '@/integrations/stripe/stripe.module';
import { AWSModule } from '@/integrations/aws/aws.module';
// Resources
import { CategoryModule } from '@/resources/category/category.module';
import { SettingsModule } from '@/resources/settings/settings.module';
import { SnippetModule } from '@/resources/snippet/snippet.module';
import { CourseModule } from '@/resources/course/course.module';
import { TeamModule } from '@/resources/team/team.module';
import { UserModule } from '@/resources/user/user.module';

import { AppController } from './app/app.controller';
import { AppService } from './app/app.service';

const boardOptions = {
  uiConfig: {
    boardTitle: config().app.name,
    favIcon: {
      alternative: `${config().app.client.url}/favicon.ico`,
      default: `${config().app.client.url}/favicon.ico`,
    },
    boardLogo: { path: config().app.logo },
  },
};

@Module({
  imports: [
    MongooseModule.forRoot(config().mongo.url, {
      onConnectionCreate: () => new Logger('Mongoose').log('Connected to MongoDB.'),
    }),
    CacheModule.forRoot(),
    ThrottlerModule.forRoot({ throttlers: [{ ttl: 60000, limit: 10 }] }),
    BullModule.forRoot({ connection: config().redis }),
    BullBoardModule.forRoot({
      route: '/bull-board',
      adapter: ExpressAdapter,
      middleware: basicAuth({
        challenge: true,
        users: { [config().bullBoard.username]: config().bullBoard.password },
      }),
      boardOptions,
    }),
    // Resources
    CategoryModule,
    SettingsModule,
    SnippetModule,
    CourseModule,
    TeamModule,
    UserModule,
    // Common Modules
    CommonServicesModule,
    AuthModule,
    MailModule,
    // Integrations
    StripeModule,
    AWSModule,
    // Others
    MeModule,
    // Ingestion & Generation base on RAG
    ContentGenerationModule,
    KnowledgeBaseModule,
    VoiceAgentModule,
    AiWizardsModule,
    ImgModule,
    RagModule,
  ],
  controllers: [AppController],
  providers: [AppService, UrlService],
})
export class AppModule implements OnApplicationShutdown {
  constructor(private readonly appService: AppService) {}

  async onApplicationShutdown(signal?: string): Promise<void> {
    await this.appService.onApplicationShutdown(signal);
  }
}
