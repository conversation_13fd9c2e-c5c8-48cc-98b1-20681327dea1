import { Body, Controller, Header, MessageEvent, Post, <PERSON>s, Sse, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { Observable } from 'rxjs';
import { ReqUser } from '../../common/decorators/req-user.decorator';
import { User } from '../../resources/user/user.model';
import { AiWizardsService } from './ai-wizards.service';
import {
  AiWizardResponseDto,
  ExtendTextContentDto,
  RephraseTextContentDto,
  TextToSpeechDto,
  TextToSpeechResponseDto,
  TranslateTextDto,
  TranslationResponseDto,
} from './dto/ai-wizards.dto';

@ApiTags('AI Wizards')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
@Controller('ai-wizards')
export class AiWizardsController {
  constructor(private readonly aiWizardsService: AiWizardsService) {}

  @Post('translate')
  @ApiOperation({
    summary: 'Translate text to target language',
    description: `Translate text to any target language using advanced AI translation with automatic source language detection.

Features:
- Automatic source language detection
- Support for any language
- High accuracy and natural fluency
- Preserves formatting and tone

Example request:
{
  "text": "Hello, how are you today?",
  "targetLanguage": "spanish"
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Text translated successfully',
    type: TranslationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during translation',
  })
  async translateText(
    @Body() dto: TranslateTextDto,
    @ReqUser() user: User,
  ): Promise<TranslationResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.translateText(dto);
  }

  @Post('text-to-speech')
  @ApiOperation({
    summary: 'Convert text to speech using AI voice synthesis',
    description: `Generate high-quality speech audio from text using Google's Gemini TTS with customizable voice options.

Features:
- Multiple voice options (Kore, Charon, Fenrir, Aoede)
- Customizable speech style and tone
- High-quality 24kHz PCM audio output
- Support for custom speaking instructions
- Real-time audio generation

Example request:
{
  "text": "Welcome to our AI-powered text-to-speech service!",
  "voiceName": "Kore",
  "speechStyle": "friendly",
  "instruction": "Say this with enthusiasm and warmth"
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Audio generated successfully',
    type: TextToSpeechResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during speech synthesis',
  })
  async textToSpeech(
    @Body() dto: TextToSpeechDto,
    @ReqUser() user: User,
  ): Promise<TextToSpeechResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.textToSpeech(dto);
  }

  @Post('text-to-speech-stream')
  @ApiOperation({
    summary: 'Convert text to speech and stream audio directly',
    description: `Generate and stream speech audio directly as PCM audio data for real-time playback.

This endpoint returns the audio as a binary stream instead of JSON, suitable for direct audio playback.

Features:
- Direct audio streaming
- PCM format at 24kHz sample rate
- Immediate playback capability
- Same voice and style options as text-to-speech endpoint

Example request:
{
  "text": "This audio will be streamed directly!",
  "voiceName": "Kore",
  "speechStyle": "cheerful"
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Audio stream generated successfully',
    headers: {
      'Content-Type': {
        description: 'audio/pcm;rate=24000',
      },
      'Content-Length': {
        description: 'Size of audio data in bytes',
      },
    },
  })
  @Header('Content-Type', 'audio/pcm;rate=24000')
  async textToSpeechStream(
    @Body() dto: TextToSpeechDto,
    @ReqUser() user: User,
    @Res() res: Response,
  ): Promise<void> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    try {
      const result = await this.aiWizardsService.textToSpeech(dto);

      // Convert base64 audio data to buffer
      const audioBuffer = Buffer.from(result.audioData, 'base64');

      // Set appropriate headers for audio streaming
      res.set({
        'Content-Type': 'audio/pcm;rate=24000',
        'Content-Length': audioBuffer.length.toString(),
        'Content-Disposition': 'inline; filename="speech.pcm"',
        'X-Audio-Duration': result.duration.toString(),
        'X-Voice-Used': result.voiceUsed,
        'X-Processing-Time': result.processingTime?.toString() || '0',
      });

      // Stream the audio data
      res.send(audioBuffer);
    } catch (error) {
      res.status(500).json({
        error: 'Text-to-speech generation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  @Post('rephrase-content')
  @ApiOperation({
    summary: 'Rephrase existing text content',
    description: `Rephrase text while maintaining the original meaning but with different wording and structure.

Features:
- Maintains original meaning and intent
- Uses varied vocabulary and sentence structures
- Customizable tone and style
- Avoids plagiarism concerns
- Natural flow and readability
- Style-specific rephrasing

Example request:
{
  "text": "The quick brown fox jumps over the lazy dog.",
  "tone": "formal",
  "style": "Make it more elaborate and descriptive"
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Content rephrased successfully',
    type: AiWizardResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during content rephrasing',
  })
  async rephraseTextContent(
    @Body() dto: RephraseTextContentDto,
    @ReqUser() user: User,
  ): Promise<AiWizardResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.rephraseTextContent(dto);
  }

  @Post('extend-content')
  @ApiOperation({
    summary: 'Extend existing text content',
    description: `Extend and expand existing text content with additional details and information based on custom instructions.

Features:
- Maintains original tone and style
- Preserves original language
- Keeps core message intact
- Custom extension instructions
- Natural integration of new content

Example request:
{
  "text": "Climate change is a pressing global issue.",
  "additionalPrompt": "Add more examples and statistics about renewable energy solutions"
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Content extended successfully',
    type: AiWizardResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during content extension',
  })
  async extendTextContent(
    @Body() dto: ExtendTextContentDto,
    @ReqUser() user: User,
  ): Promise<AiWizardResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.extendTextContent(dto);
  }

  @Sse('text-to-speech-sse')
  @ApiOperation({
    summary: 'Convert text to speech with real-time streaming using Server-Sent Events',
    description: `Generate and stream speech audio in real-time using Server-Sent Events (SSE).

This endpoint breaks down text into chunks, generates audio for each chunk, and streams them as they're ready.

Features:
- True real-time streaming via SSE
- Lower latency - audio starts playing immediately
- Chunked processing for faster response
- Same voice and style options as other TTS endpoints
- Progress updates and metadata

Usage:
- Connect to the SSE endpoint: GET /ai-wizards/text-to-speech-sse
- Send text via query parameters or connect and send data
- Receive audio chunks as 'audio-chunk' events
- Receive metadata as 'audio-info' events
- End of stream indicated by 'audio-complete' event

Example events:
- audio-info: {"chunkIndex": 1, "totalChunks": 3, "text": "Hello world!"}
- audio-chunk: {"audioData": "base64-encoded-pcm", "chunkIndex": 1, "size": 48000}
- audio-complete: {"totalChunks": 3, "totalDuration": 2.5, "processingTime": 1.8}`,
  })
  async streamTextToSpeechSSE(
    @Body() dto: TextToSpeechDto,
    @ReqUser() user: User,
  ): Promise<Observable<MessageEvent>> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.streamTextToSpeechSSE(dto);
  }

  @Sse('translate-sse')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Translate text with real-time streaming using Server-Sent Events',
    description: `Translate text in real-time using Server-Sent Events (SSE) for immediate response feedback.

Features:
- Real-time streaming translation
- Lower latency - text appears as it's translated
- Progress updates and metadata
- Auto-detects source language
- Same translation quality as standard endpoint

Event Types:
- stream-start: Translation process started
- text-chunk: Partial translation text chunk
- stream-complete: Translation finished with final result

Example events:
- stream-start: {"operation": "translation", "inputText": "Hello", "parameters": {"targetLanguage": "spanish"}}
- text-chunk: {"chunk": "Hola", "fullText": "Hola", "chunkIndex": 1}
- stream-complete: {"finalText": "Hola, ¿cómo estás?", "processingTime": 1.2, "totalChunks": 3}`,
  })
  @ApiResponse({
    status: 200,
    description: 'Translation streaming started successfully',
    schema: {
      type: 'object',
      properties: {
        'stream-start': { $ref: '#/components/schemas/StreamingStartDto' },
        'text-chunk': { $ref: '#/components/schemas/StreamingChunkDto' },
        'stream-complete': { $ref: '#/components/schemas/StreamingCompleteDto' },
      },
    },
  })
  async streamTranslateSSE(
    @Body() dto: TranslateTextDto,
    @ReqUser() user: User,
  ): Promise<Observable<MessageEvent>> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.streamTranslateSSE(dto);
  }

  @Sse('rephrase-content-sse')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Rephrase text content with real-time streaming using Server-Sent Events',
    description: `Rephrase text in real-time using Server-Sent Events (SSE) for immediate response feedback.

Features:
- Real-time streaming text rephrasing
- Lower latency - rephrased text appears as it's generated
- Progress updates and metadata
- Maintains original meaning
- Customizable tone and style

Event Types:
- stream-start: Rephrasing process started
- text-chunk: Partial rephrased text chunk
- stream-complete: Rephrasing finished with final result

Example events:
- stream-start: {"operation": "rephrasing", "inputText": "Quick fox", "parameters": {"tone": "formal"}}
- text-chunk: {"chunk": "The swift", "fullText": "The swift", "chunkIndex": 1}
- stream-complete: {"finalText": "The swift canine creature", "processingTime": 1.8, "totalChunks": 4}`,
  })
  @ApiResponse({
    status: 200,
    description: 'Rephrasing streaming started successfully',
    schema: {
      type: 'object',
      properties: {
        'stream-start': { $ref: '#/components/schemas/StreamingStartDto' },
        'text-chunk': { $ref: '#/components/schemas/StreamingChunkDto' },
        'stream-complete': { $ref: '#/components/schemas/StreamingCompleteDto' },
      },
    },
  })
  async streamRephraseSSE(
    @Body() dto: RephraseTextContentDto,
    @ReqUser() user: User,
  ): Promise<Observable<MessageEvent>> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.streamRephraseSSE(dto);
  }

  @Sse('extend-content-sse')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Extend text content with real-time streaming using Server-Sent Events',
    description: `Extend text content in real-time using Server-Sent Events (SSE) for immediate response feedback.

Features:
- Real-time streaming text extension
- Lower latency - extended content appears as it's generated
- Progress updates and metadata
- Maintains original tone and style
- Custom extension instructions

Event Types:
- stream-start: Extension process started
- text-chunk: Partial extended text chunk
- stream-complete: Extension finished with final result

Example events:
- stream-start: {"operation": "extension", "inputText": "Climate change", "parameters": {"additionalPrompt": "Add statistics"}}
- text-chunk: {"chunk": " is affecting", "fullText": "Climate change is affecting", "chunkIndex": 1}
- stream-complete: {"finalText": "Climate change is affecting global temperatures with 1.5°C increase", "processingTime": 2.1, "totalChunks": 6}`,
  })
  @ApiResponse({
    status: 200,
    description: 'Extension streaming started successfully',
    schema: {
      type: 'object',
      properties: {
        'stream-start': { $ref: '#/components/schemas/StreamingStartDto' },
        'text-chunk': { $ref: '#/components/schemas/StreamingChunkDto' },
        'stream-complete': { $ref: '#/components/schemas/StreamingCompleteDto' },
      },
    },
  })
  async streamExtendSSE(
    @Body() dto: ExtendTextContentDto,
    @ReqUser() user: User,
  ): Promise<Observable<MessageEvent>> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    return this.aiWizardsService.streamExtendSSE(dto);
  }
}
