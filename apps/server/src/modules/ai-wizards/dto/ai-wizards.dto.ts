import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

// Removed SupportedLanguage enum to support all languages as strings

export enum ContentTone {
  FORMAL = 'formal',
  CASUAL = 'casual',
  ACADEMIC = 'academic',
  PROFESSIONAL = 'professional',
  FRIENDLY = 'friendly',
  PERSUASIVE = 'persuasive',
  TECHNICAL = 'technical',
}

// TTS Voice options based on Google's Gemini TTS
export enum VoiceName {
  KORE = 'Kore',
  CHARON = 'Charon',
  FENRIR = 'Fenrir',
  AOEDE = 'Aoede',
}

export enum SpeechStyle {
  CHEERFUL = 'cheerful',
  CALM = 'calm',
  EXCITED = 'excited',
  PROFESSIONAL = 'professional',
  FRIENDLY = 'friendly',
  SERIOUS = 'serious',
}

// Removed ContentLength enum as CreateTextContentDto was removed

// Base DTO for common properties
export class BaseAiWizardDto {
  @ApiProperty({
    description: 'The input text to process',
    example: 'Hello, how are you today?',
    minLength: 1,
    maxLength: 10000,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(10000)
  text!: string;
}

// Translation DTO
export class TranslateTextDto extends BaseAiWizardDto {
  @ApiProperty({
    description:
      'Target language for translation (e.g., "spanish", "french", "german", "mandarin", "japanese", etc.)',
    example: 'spanish',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  targetLanguage!: string;
}

// Text-to-Speech DTO
export class TextToSpeechDto {
  @ApiProperty({
    description: 'The text to convert to speech',
    example: 'Hello, welcome to our AI-powered text-to-speech service!',
    minLength: 1,
    maxLength: 5000,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(5000)
  text!: string;

  @ApiPropertyOptional({
    enum: VoiceName,
    description: 'Voice to use for speech synthesis',
    example: VoiceName.KORE,
    default: VoiceName.KORE,
  })
  @IsOptional()
  @IsEnum(VoiceName)
  voiceName?: VoiceName;

  @ApiPropertyOptional({
    enum: SpeechStyle,
    description: 'Style or mood for the speech',
    example: SpeechStyle.FRIENDLY,
  })
  @IsOptional()
  @IsEnum(SpeechStyle)
  speechStyle?: SpeechStyle;

  @ApiPropertyOptional({
    description:
      'Additional instruction for how to say the text (e.g., "Say this with enthusiasm")',
    example: 'Say this with a warm and welcoming tone',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  instruction?: string;
}

// Removed CreateTextContentDto as requested

// Removed EnhanceTextContentDto as requested

// Rephrase Content DTO
export class RephraseTextContentDto extends BaseAiWizardDto {
  @ApiProperty({
    enum: ContentTone,
    description: 'Desired tone for the rephrased content',
    example: ContentTone.CASUAL,
  })
  @IsEnum(ContentTone)
  tone!: ContentTone;

  @ApiPropertyOptional({
    description: 'Specific style or approach for rephrasing',
    example: 'Make it more concise and direct',
  })
  @IsOptional()
  @IsString()
  @MaxLength(300)
  style?: string;
}

// Extend Content DTO
export class ExtendTextContentDto extends BaseAiWizardDto {
  @ApiProperty({
    description: 'Additional prompt or instructions for extending the text',
    example: 'Add more examples and make it more detailed for university students',
    minLength: 1,
    maxLength: 1000,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(1000)
  additionalPrompt!: string;
}

// Response DTOs - Simplified for streaming
export class AiWizardResponseDto {
  @ApiProperty({
    description: 'The processed/generated text',
    example: 'This is the result of the AI processing',
  })
  result!: string;

  @ApiPropertyOptional({
    description: 'Processing time in seconds',
    example: 1.25,
  })
  processingTime?: number;
}

export class TranslationResponseDto {
  @ApiProperty({
    description: 'The translated text',
    example: 'Hola, ¿cómo estás hoy?',
  })
  result!: string;

  @ApiProperty({
    description: 'Target language of the translation',
    example: 'spanish',
  })
  targetLanguage!: string;

  @ApiPropertyOptional({
    description: 'Processing time in seconds',
    example: 1.25,
  })
  processingTime?: number;
}

// Streaming Event DTOs
export class StreamingStartDto {
  @ApiProperty({
    description: 'Type of AI operation being streamed',
    example: 'translation',
  })
  operation!: string;

  @ApiProperty({
    description: 'Input text being processed',
    example: 'Hello, how are you?',
  })
  inputText!: string;

  @ApiProperty({
    description: 'Start time of the operation',
    example: '2024-06-28T17:30:00.000Z',
  })
  startTime!: string;

  @ApiPropertyOptional({
    description: 'Additional operation parameters',
    example: { targetLanguage: 'spanish', tone: 'formal' },
  })
  parameters?: Record<string, any>;
}

export class StreamingChunkDto {
  @ApiProperty({
    description: 'Text chunk being streamed',
    example: 'Hola, ',
  })
  chunk!: string;

  @ApiProperty({
    description: 'Cumulative text so far',
    example: 'Hola, ',
  })
  fullText!: string;

  @ApiProperty({
    description: 'Chunk sequence number',
    example: 1,
  })
  chunkIndex!: number;

  @ApiProperty({
    description: 'Timestamp of this chunk',
    example: '2024-06-28T17:30:01.500Z',
  })
  timestamp!: string;
}

export class StreamingCompleteDto {
  @ApiProperty({
    description: 'Final complete text result',
    example: 'Hola, ¿cómo estás hoy?',
  })
  finalText!: string;

  @ApiProperty({
    description: 'Total processing time in seconds',
    example: 2.3,
  })
  processingTime!: number;

  @ApiProperty({
    description: 'Total number of chunks streamed',
    example: 5,
  })
  totalChunks!: number;

  @ApiProperty({
    description: 'Operation completion time',
    example: '2024-06-28T17:30:02.300Z',
  })
  completedAt!: string;
}

export class TextToSpeechResponseDto {
  @ApiProperty({
    description: 'Audio data in WAV format (base64 encoded)',
    example:
      'UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp...',
  })
  audioData!: string;

  @ApiProperty({
    description: 'MIME type of the audio',
    example: 'audio/wav',
  })
  mimeType!: string;

  @ApiProperty({
    description: 'Duration of the audio in seconds',
    example: 3.2,
  })
  duration!: number;

  @ApiProperty({
    description: 'Size of the audio data in bytes',
    example: 76800,
  })
  size!: number;

  @ApiProperty({
    description: 'Voice used for synthesis',
    example: 'Kore',
  })
  voiceUsed!: string;

  @ApiPropertyOptional({
    description: 'Cost of the operation in USD',
    example: 0.001,
  })
  cost?: number;

  @ApiPropertyOptional({
    description: 'Processing time in seconds',
    example: 1.8,
  })
  processingTime?: number;
}
