import { GoogleGenAI } from '@google/genai';
import { Injectable, Logger, MessageEvent } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import config from '../../common/configs/config';
import { AiSdk } from '../../llm/sdk/AiSdk';
import {
  AiWizardResponseDto,
  ContentTone,
  ExtendTextContentDto,
  RephraseTextContentDto,
  StreamingChunkDto,
  StreamingCompleteDto,
  StreamingStartDto,
  TextToSpeechDto,
  TextToSpeechResponseDto,
  TranslateTextDto,
  TranslationResponseDto,
  VoiceName,
} from './dto/ai-wizards.dto';

@Injectable()
export class AiWizardsService {
  private readonly logger = new Logger(AiWizardsService.name);
  private readonly googleAI: GoogleGenAI;

  constructor(private readonly aiSdk: AiSdk) {
    // Initialize Google AI for TTS
    this.googleAI = new GoogleGenAI({
      apiKey: config().google.apiKey,
    });
  }

  async translateText(dto: TranslateTextDto): Promise<TranslationResponseDto> {
    const startTime = Date.now();

    this.logger.log(`Starting translation to ${dto.targetLanguage}`);

    try {
      const systemPrompt = this.buildTranslationPrompt(dto);
      const prompt = `Translate the following text: "${dto.text}"`;

      const result = await this.aiSdk.generateText({
        prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'ai-wizards',
        identifierValue: 'translation',
      });

      const processingTime = (Date.now() - startTime) / 1000;

      this.logger.log(`Translation completed in ${processingTime}s`);

      return {
        result: result.text.trim(),
        targetLanguage: dto.targetLanguage,
        processingTime,
      };
    } catch (error) {
      this.logger.error('Translation failed:', error);
      throw error;
    }
  }

  async textToSpeech(dto: TextToSpeechDto): Promise<TextToSpeechResponseDto> {
    const startTime = Date.now();

    this.logger.log('Starting text-to-speech generation');

    try {
      // Build the prompt based on style and instruction
      let textToSpeak = dto.text;

      if (dto.speechStyle || dto.instruction) {
        const styleInstruction = dto.speechStyle ? `Say ${dto.speechStyle}: ` : '';
        const customInstruction = dto.instruction ? dto.instruction + ': ' : '';
        textToSpeak = `${customInstruction}${styleInstruction}${dto.text}`;
      }

      // Use Google's Gemini TTS model
      const response = await this.googleAI.models.generateContent({
        model: 'gemini-2.5-flash-preview-tts',
        contents: [{ parts: [{ text: textToSpeak }] }],
        config: {
          responseModalities: ['AUDIO'],
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: {
                voiceName: dto.voiceName || VoiceName.KORE,
              },
            },
          },
        },
      });

      const audioData = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;

      if (!audioData) {
        throw new Error('No audio data received from TTS service');
      }

      // Convert base64 audio to buffer to get size info
      const audioBuffer = Buffer.from(audioData, 'base64');

      // Estimate duration (approximation for PCM audio at 24kHz, 16-bit, mono)
      // Duration = data_size / (sample_rate * channels * bytes_per_sample)
      const estimatedDuration = audioBuffer.length / (24000 * 1 * 2);

      const processingTime = (Date.now() - startTime) / 1000;

      this.logger.log(`TTS generation completed in ${processingTime}s`);

      return {
        audioData,
        mimeType: 'audio/pcm;rate=24000',
        duration: Math.round(estimatedDuration * 100) / 100, // Round to 2 decimal places
        size: audioBuffer.length,
        voiceUsed: dto.voiceName || VoiceName.KORE,
        cost: 0, // Cost tracking could be added here
        processingTime,
      };
    } catch (error) {
      this.logger.error('Text-to-speech generation failed:', error);
      throw error;
    }
  }

  /**
   * Stream text-to-speech using Server-Sent Events (SSE)
   * Breaks text into chunks and streams audio as it's generated
   */
  streamTextToSpeechSSE(dto: TextToSpeechDto): Observable<MessageEvent> {
    const subject = new Subject<MessageEvent>();
    const startTime = Date.now();

    this.logger.log('Starting SSE TTS streaming');

    // Process the streaming asynchronously
    this.processTextToSpeechStreaming(dto, subject, startTime).catch((error) => {
      this.logger.error('SSE TTS streaming error:', error);
      subject.next({
        type: 'error',
        data: JSON.stringify({
          error: 'Text-to-speech streaming failed',
          message: error instanceof Error ? error.message : 'Unknown error',
        }),
      } as MessageEvent);
      subject.complete();
    });

    return subject.asObservable();
  }

  /**
   * Stream translation using Server-Sent Events (SSE)
   */
  streamTranslateSSE(dto: TranslateTextDto): Observable<MessageEvent> {
    const subject = new Subject<MessageEvent>();
    const startTime = Date.now();

    this.logger.log(`Starting SSE translation streaming to ${dto.targetLanguage}`);

    // Process the streaming asynchronously
    this.processTranslationStreaming(dto, subject, startTime).catch((error) => {
      this.logger.error('SSE translation streaming error:', error);
      subject.next({
        type: 'error',
        data: JSON.stringify({
          error: 'Translation streaming failed',
          message: error instanceof Error ? error.message : 'Unknown error',
        }),
      } as MessageEvent);
      subject.complete();
    });

    return subject.asObservable();
  }

  /**
   * Stream text rephrasing using Server-Sent Events (SSE)
   */
  streamRephraseSSE(dto: RephraseTextContentDto): Observable<MessageEvent> {
    const subject = new Subject<MessageEvent>();
    const startTime = Date.now();

    this.logger.log('Starting SSE rephrasing streaming');

    // Process the streaming asynchronously
    this.processRephrasingStreaming(dto, subject, startTime).catch((error) => {
      this.logger.error('SSE rephrasing streaming error:', error);
      subject.next({
        type: 'error',
        data: JSON.stringify({
          error: 'Rephrasing streaming failed',
          message: error instanceof Error ? error.message : 'Unknown error',
        }),
      } as MessageEvent);
      subject.complete();
    });

    return subject.asObservable();
  }

  /**
   * Stream text extension using Server-Sent Events (SSE)
   */
  streamExtendSSE(dto: ExtendTextContentDto): Observable<MessageEvent> {
    const subject = new Subject<MessageEvent>();
    const startTime = Date.now();

    this.logger.log('Starting SSE text extension streaming');

    // Process the streaming asynchronously
    this.processExtensionStreaming(dto, subject, startTime).catch((error) => {
      this.logger.error('SSE extension streaming error:', error);
      subject.next({
        type: 'error',
        data: JSON.stringify({
          error: 'Text extension streaming failed',
          message: error instanceof Error ? error.message : 'Unknown error',
        }),
      } as MessageEvent);
      subject.complete();
    });

    return subject.asObservable();
  }

  private async processTextToSpeechStreaming(
    dto: TextToSpeechDto,
    subject: Subject<MessageEvent>,
    startTime: number,
  ): Promise<void> {
    try {
      // Split text into sentences for chunked processing
      const sentences = this.splitTextIntoChunks(dto.text);
      const totalChunks = sentences.length;
      let totalDuration = 0;

      this.logger.log(`Processing ${totalChunks} text chunks for streaming`);

      // Send initial info
      subject.next({
        type: 'audio-info',
        data: JSON.stringify({
          totalChunks,
          voiceName: dto.voiceName || VoiceName.KORE,
          speechStyle: dto.speechStyle || 'default',
          startTime: new Date().toISOString(),
        }),
      } as MessageEvent);

      // Process each chunk
      for (let i = 0; i < sentences.length; i++) {
        const sentence = sentences[i];
        if (!sentence) continue;

        const chunkText = sentence.trim();
        if (!chunkText) continue;

        this.logger.log(
          `Processing chunk ${i + 1}/${totalChunks}: "${chunkText.substring(0, 50)}..."`,
        );

        try {
          // Build chunk-specific prompt
          let textToSpeak = chunkText;

          if (dto.speechStyle || dto.instruction) {
            // Only add instruction to first chunk to avoid repetition
            if (i === 0) {
              const styleInstruction = dto.speechStyle ? `Say ${dto.speechStyle}: ` : '';
              const customInstruction = dto.instruction ? dto.instruction + ': ' : '';
              textToSpeak = `${customInstruction}${styleInstruction}${chunkText}`;
            } else {
              // For subsequent chunks, maintain the style but don't repeat instructions
              textToSpeak = chunkText;
            }
          }

          // Generate audio for this chunk
          const response = await this.googleAI.models.generateContent({
            model: 'gemini-2.5-flash-preview-tts',
            contents: [{ parts: [{ text: textToSpeak }] }],
            config: {
              responseModalities: ['AUDIO'],
              speechConfig: {
                voiceConfig: {
                  prebuiltVoiceConfig: {
                    voiceName: dto.voiceName || VoiceName.KORE,
                  },
                },
              },
            },
          });

          const audioData = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;

          if (audioData) {
            const audioBuffer = Buffer.from(audioData, 'base64');
            const chunkDuration = audioBuffer.length / (24000 * 1 * 2);
            totalDuration += chunkDuration;

            // Send audio chunk
            subject.next({
              type: 'audio-chunk',
              data: JSON.stringify({
                chunkIndex: i + 1,
                totalChunks,
                audioData,
                size: audioBuffer.length,
                duration: Math.round(chunkDuration * 100) / 100,
                text: chunkText,
                mimeType: 'audio/pcm;rate=24000',
              }),
            } as MessageEvent);

            this.logger.log(
              `Sent chunk ${i + 1}/${totalChunks} (${audioBuffer.length} bytes, ${chunkDuration.toFixed(2)}s)`,
            );
          } else {
            this.logger.warn(`No audio data for chunk ${i + 1}: "${chunkText}"`);
          }
        } catch (chunkError) {
          this.logger.error(`Error processing chunk ${i + 1}:`, chunkError);

          // Send error for this chunk but continue with others
          subject.next({
            type: 'chunk-error',
            data: JSON.stringify({
              chunkIndex: i + 1,
              error: chunkError instanceof Error ? chunkError.message : 'Chunk processing failed',
              text: chunkText,
            }),
          } as MessageEvent);
        }
      }

      const totalProcessingTime = (Date.now() - startTime) / 1000;

      // Send completion info
      subject.next({
        type: 'audio-complete',
        data: JSON.stringify({
          totalChunks,
          processedChunks: sentences.length,
          totalDuration: Math.round(totalDuration * 100) / 100,
          processingTime: totalProcessingTime,
          voiceUsed: dto.voiceName || VoiceName.KORE,
          completedAt: new Date().toISOString(),
        }),
      } as MessageEvent);

      this.logger.log(
        `SSE TTS streaming completed: ${totalChunks} chunks, ${totalDuration.toFixed(2)}s audio, ${totalProcessingTime.toFixed(2)}s processing`,
      );

      subject.complete();
    } catch (error) {
      this.logger.error('Error in TTS streaming process:', error);
      throw error;
    }
  }

  /**
   * Split text into manageable chunks for streaming
   * Prioritizes sentence boundaries for natural speech flow
   */
  private splitTextIntoChunks(text: string): string[] {
    // First, try to split by sentences
    const sentences = text.split(/[.!?]+/).filter((s) => s.trim().length > 0);

    // If sentences are too long (>200 chars), split them further
    const chunks: string[] = [];

    for (const sentence of sentences) {
      if (sentence.length <= 200) {
        chunks.push(sentence.trim());
      } else {
        // Split long sentences by commas, semicolons, or clause breaks
        const subChunks = sentence
          .split(
            /[,;:]|(?=\s(?:and|but|or|so|yet|for|nor|because|since|although|though|while|whereas|if|unless|until|when|where|why|how)\s)/i,
          )
          .filter((s) => s.trim().length > 0);

        chunks.push(...subChunks.map((chunk) => chunk.trim()));
      }
    }

    // Ensure no chunk is too short (unless it's the only one)
    if (chunks.length > 1) {
      return chunks.filter((chunk) => chunk.length >= 10);
    }

    return chunks.length > 0 ? chunks : [text]; // Fallback to original text
  }

  // Removed createTextContent and enhanceTextContent methods as requested

  async rephraseTextContent(dto: RephraseTextContentDto): Promise<AiWizardResponseDto> {
    const startTime = Date.now();

    this.logger.log('Starting text rephrasing');

    try {
      const systemPrompt = this.buildRephrasingPrompt(dto);
      const prompt = `Rephrase the following text: "${dto.text}"`;

      const result = await this.aiSdk.generateText({
        prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'ai-wizards',
        identifierValue: 'text-rephrasing',
      });

      const processingTime = (Date.now() - startTime) / 1000;

      this.logger.log(`Text rephrasing completed in ${processingTime}s`);

      return {
        result: result.text.trim(),
        processingTime,
      };
    } catch (error) {
      this.logger.error('Text rephrasing failed:', error);
      throw error;
    }
  }

  async extendTextContent(dto: ExtendTextContentDto): Promise<AiWizardResponseDto> {
    const startTime = Date.now();

    this.logger.log('Starting text extension');

    try {
      const systemPrompt = this.buildExtensionPrompt(dto);
      const prompt = `Extend the following text: "${dto.text}"`;

      const result = await this.aiSdk.generateText({
        prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'ai-wizards',
        identifierValue: 'text-extension',
      });

      const processingTime = (Date.now() - startTime) / 1000;

      this.logger.log(`Text extension completed in ${processingTime}s`);

      return {
        result: result.text.trim(),
        processingTime,
      };
    } catch (error) {
      this.logger.error('Text extension failed:', error);
      throw error;
    }
  }

  private buildTranslationPrompt(dto: TranslateTextDto): string {
    const prompt = `You are a professional translator with expertise in multiple languages.

Your task is to provide accurate, natural, and contextually appropriate translations.

Translation Requirements:
- Detect the source language automatically
- Translate to: ${dto.targetLanguage}
- Maintain the original meaning and tone
- Use natural, fluent language in the target language
- Preserve formatting and structure when possible

Provide only the translated text without any additional commentary or explanations.`;

    return prompt;
  }

  // Removed buildContentCreationPrompt and buildEnhancementPrompt methods as requested

  private buildRephrasingPrompt(dto: RephraseTextContentDto): string {
    const toneInstructions = this.getToneInstructions(dto.tone);

    let prompt = `You are an expert writer specializing in text rephrasing and rewriting.

Your task is to rephrase the provided text while maintaining its original meaning and key information.

Rephrasing Requirements:
- Tone: ${toneInstructions}
- Maintain the original language of the text`;

    if (dto.style) {
      prompt += `\n- Style Instructions: ${dto.style}`;
    }

    prompt += `\n\nRephrase the text by:
1. Using different sentence structures
2. Employing varied vocabulary and word choices
3. Maintaining the original meaning and intent
4. Applying the specified tone consistently
5. Ensuring natural flow and readability
6. Avoiding plagiarism while preserving key information

Provide only the rephrased text without any explanations or commentary.`;

    return prompt;
  }

  private buildExtensionPrompt(dto: ExtendTextContentDto): string {
    const prompt = `You are an expert content writer specializing in expanding and extending text.

Your task is to extend and expand the provided text according to the additional instructions.

Extension Requirements:
- Maintain the original tone and style of the text
- Preserve the original language of the text
- Keep the core message and meaning intact
- Additional Instructions: ${dto.additionalPrompt}

Extend the text by:
1. Adding relevant details and examples
2. Expanding on key points
3. Maintaining logical flow and coherence
4. Following the specific instructions provided
5. Ensuring the extended content feels natural and well-integrated

Provide only the extended text without any explanations or commentary.`;

    return prompt;
  }

  private getToneInstructions(tone: ContentTone): string {
    switch (tone) {
      case ContentTone.FORMAL:
        return 'Use formal language, proper grammar, and professional vocabulary';
      case ContentTone.CASUAL:
        return 'Use relaxed, conversational language with a friendly approach';
      case ContentTone.ACADEMIC:
        return 'Use scholarly language, precise terminology, and analytical approach';
      case ContentTone.PROFESSIONAL:
        return 'Use business-appropriate language that is clear and authoritative';
      case ContentTone.FRIENDLY:
        return 'Use warm, approachable language with a welcoming tone';
      case ContentTone.PERSUASIVE:
        return 'Use compelling language designed to convince and motivate';
      case ContentTone.TECHNICAL:
        return 'Use precise, technical language with industry-specific terminology';
      default:
        return 'Use clear and natural language';
    }
  }

  private async processTranslationStreaming(
    dto: TranslateTextDto,
    subject: Subject<MessageEvent>,
    startTime: number,
  ): Promise<void> {
    try {
      // Send initial info
      subject.next({
        type: 'stream-start',
        data: JSON.stringify({
          operation: 'translation',
          inputText: dto.text,
          startTime: new Date().toISOString(),
          parameters: { targetLanguage: dto.targetLanguage },
        } as StreamingStartDto),
      } as MessageEvent);

      const systemPrompt = this.buildTranslationPrompt(dto);
      const prompt = `Translate the following text: "${dto.text}"`;

      // Use streaming AI SDK
      const stream = await this.aiSdk.streamText({
        prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'ai-wizards',
        identifierValue: 'translation-streaming',
      });

      let fullText = '';
      let chunkIndex = 0;

      // Stream text chunks as they arrive
      for await (const chunk of stream.textStream) {
        chunkIndex++;
        fullText += chunk;

        subject.next({
          type: 'text-chunk',
          data: JSON.stringify({
            chunk,
            fullText,
            chunkIndex,
            timestamp: new Date().toISOString(),
          } as StreamingChunkDto),
        } as MessageEvent);

        this.logger.debug(`Translation chunk ${chunkIndex}: "${chunk}"`);
      }

      const processingTime = (Date.now() - startTime) / 1000;

      // Send completion info
      subject.next({
        type: 'stream-complete',
        data: JSON.stringify({
          finalText: fullText.trim(),
          processingTime,
          totalChunks: chunkIndex,
          completedAt: new Date().toISOString(),
        } as StreamingCompleteDto),
      } as MessageEvent);

      this.logger.log(
        `Translation streaming completed: ${chunkIndex} chunks, ${processingTime.toFixed(2)}s`,
      );
      subject.complete();
    } catch (error) {
      this.logger.error('Error in translation streaming process:', error);
      throw error;
    }
  }

  private async processRephrasingStreaming(
    dto: RephraseTextContentDto,
    subject: Subject<MessageEvent>,
    startTime: number,
  ): Promise<void> {
    try {
      // Send initial info
      subject.next({
        type: 'stream-start',
        data: JSON.stringify({
          operation: 'rephrasing',
          inputText: dto.text,
          startTime: new Date().toISOString(),
          parameters: { tone: dto.tone, style: dto.style },
        } as StreamingStartDto),
      } as MessageEvent);

      const systemPrompt = this.buildRephrasingPrompt(dto);
      const prompt = `Rephrase the following text: "${dto.text}"`;

      // Use streaming AI SDK
      const stream = await this.aiSdk.streamText({
        prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'ai-wizards',
        identifierValue: 'rephrasing-streaming',
      });

      let fullText = '';
      let chunkIndex = 0;

      // Stream text chunks as they arrive
      for await (const chunk of stream.textStream) {
        chunkIndex++;
        fullText += chunk;

        subject.next({
          type: 'text-chunk',
          data: JSON.stringify({
            chunk,
            fullText,
            chunkIndex,
            timestamp: new Date().toISOString(),
          } as StreamingChunkDto),
        } as MessageEvent);

        this.logger.debug(`Rephrasing chunk ${chunkIndex}: "${chunk}"`);
      }

      const processingTime = (Date.now() - startTime) / 1000;

      // Send completion info
      subject.next({
        type: 'stream-complete',
        data: JSON.stringify({
          finalText: fullText.trim(),
          processingTime,
          totalChunks: chunkIndex,
          completedAt: new Date().toISOString(),
        } as StreamingCompleteDto),
      } as MessageEvent);

      this.logger.log(
        `Rephrasing streaming completed: ${chunkIndex} chunks, ${processingTime.toFixed(2)}s`,
      );
      subject.complete();
    } catch (error) {
      this.logger.error('Error in rephrasing streaming process:', error);
      throw error;
    }
  }

  private async processExtensionStreaming(
    dto: ExtendTextContentDto,
    subject: Subject<MessageEvent>,
    startTime: number,
  ): Promise<void> {
    try {
      // Send initial info
      subject.next({
        type: 'stream-start',
        data: JSON.stringify({
          operation: 'extension',
          inputText: dto.text,
          startTime: new Date().toISOString(),
          parameters: { additionalPrompt: dto.additionalPrompt },
        } as StreamingStartDto),
      } as MessageEvent);

      const systemPrompt = this.buildExtensionPrompt(dto);
      const prompt = `Extend the following text: "${dto.text}"`;

      // Use streaming AI SDK
      const stream = await this.aiSdk.streamText({
        prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'ai-wizards',
        identifierValue: 'extension-streaming',
      });

      let fullText = '';
      let chunkIndex = 0;

      // Stream text chunks as they arrive
      for await (const chunk of stream.textStream) {
        chunkIndex++;
        fullText += chunk;

        subject.next({
          type: 'text-chunk',
          data: JSON.stringify({
            chunk,
            fullText,
            chunkIndex,
            timestamp: new Date().toISOString(),
          } as StreamingChunkDto),
        } as MessageEvent);

        this.logger.debug(`Extension chunk ${chunkIndex}: "${chunk}"`);
      }

      const processingTime = (Date.now() - startTime) / 1000;

      // Send completion info
      subject.next({
        type: 'stream-complete',
        data: JSON.stringify({
          finalText: fullText.trim(),
          processingTime,
          totalChunks: chunkIndex,
          completedAt: new Date().toISOString(),
        } as StreamingCompleteDto),
      } as MessageEvent);

      this.logger.log(
        `Extension streaming completed: ${chunkIndex} chunks, ${processingTime.toFixed(2)}s`,
      );
      subject.complete();
    } catch (error) {
      this.logger.error('Error in extension streaming process:', error);
      throw error;
    }
  }
}
