import { Injectable, Logger } from '@nestjs/common';
import { AiSdk } from '../../llm/sdk/AiSdk';
import { KnowledgeBaseService } from '../knowledge-base';
import { TextSearchResult } from '../vector-store/types/vector-store.types';
import { VectorSearchService } from '../vector-store/vector-search.service';

export interface TemporarySnippetData {
  teamId: string;
  snippetId: string;
  fileUrls?: string[];
  urls?: string[];
  createdAt: Date;
  vectorIds: string[];
}

export interface RagContext {
  content: string;
  sources: string[];
  knowledgeBaseSources: string[];
  webSearchSources: string[];
}

@Injectable()
export class RagService {
  private readonly logger = new Logger(RagService.name);
  private readonly temporarySnippets = new Map<string, TemporarySnippetData>();

  constructor(
    private readonly aiSdk: AiSdk,
    private readonly vectorSearchService: VectorSearchService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
  ) {}

  async processTemporaryFiles(
    teamId: string,
    snippetId: string,
    fileUrls?: string[],
    urls?: string[],
  ): Promise<void> {
    const namespace = `${teamId}_${snippetId}`;

    try {
      this.logger.log(
        `Processing temporary files for snippet ${snippetId} in namespace ${namespace}`,
      );

      const vectorIds: string[] = [];

      // Process file URLs if provided
      if (fileUrls && fileUrls.length > 0) {
        for (const fileUrl of fileUrls) {
          try {
            const result = await this.knowledgeBaseService.createKnowledgeBaseWithNamespace(
              {
                type: 'upload' as any,
                fileUrl,
                description: `Temporary file for snippet ${snippetId}`,
              },
              namespace,
            );

            if (result.vectorId) {
              vectorIds.push(result.vectorId);
            }
          } catch (error) {
            this.logger.error(`Error processing file URL ${fileUrl}:`, error);
          }
        }
      }

      // Process URLs if provided
      if (urls && urls.length > 0) {
        for (const url of urls) {
          try {
            const result = await this.knowledgeBaseService.createKnowledgeBaseWithNamespace(
              {
                type: 'url' as any,
                url,
                description: `Temporary URL content for snippet ${snippetId}`,
              },
              namespace,
            );

            if (result.vectorId) {
              vectorIds.push(result.vectorId);
            }
          } catch (error) {
            this.logger.error(`Error processing URL ${url}:`, error);
          }
        }
      }

      // Store the temporary snippet data
      const temporaryData: TemporarySnippetData = {
        teamId,
        snippetId,
        fileUrls,
        urls,
        createdAt: new Date(),
        vectorIds,
      };

      this.temporarySnippets.set(namespace, temporaryData);

      this.logger.log(
        `Processed temporary files for snippet ${snippetId} in namespace ${namespace}`,
      );
    } catch (error) {
      this.logger.error(`Error processing temporary files for snippet ${snippetId}:`, error);
      throw error;
    }
  }

  async getKnowledgeBaseContext(
    teamId: string,
    snippetId: string,
    searchQuery: string,
  ): Promise<{ content: string; sources: string[] } | null> {
    try {
      const namespace = `${teamId}_${snippetId}`;

      // Search both main team knowledge base and temporary snippet data
      const searchPromises: Promise<TextSearchResult>[] = [
        // Search main team knowledge base
        this.vectorSearchService.textSearch({
          query: searchQuery,
          topK: 3,
          threshold: 0.6,
          namespace: teamId, // Main team knowledge base
        }),
      ];

      // Search temporary snippet knowledge base if it exists
      if (this.temporarySnippets.has(namespace)) {
        this.logger.log(
          `Found total ${this.temporarySnippets.get(namespace)?.vectorIds.length} snippet based knowledge base for ${namespace}`,
        );
        searchPromises.push(
          this.vectorSearchService.textSearch({
            query: searchQuery,
            topK: 5,
            threshold: 0.6,
            namespace: namespace, // Temporary snippet namespace
          }),
        );
      }

      const searchResults = await Promise.all(searchPromises);

      this.logger.log('Total search results found from knowledge base:', searchResults.length);

      const allItems = searchResults.flatMap((result) => result.items || []);

      if (allItems.length === 0) {
        return null;
      }

      const content = allItems.map((item) => `${item.text}`).join('\n\n');

      const sources = allItems.map((item) => item.id);

      return { content, sources };
    } catch (error) {
      this.logger.error('Error retrieving knowledge base context:', error);
      return null;
    }
  }

  async getWebSearchContext(searchQuery: string): Promise<{
    content: string;
    sources: string[];
  } | null> {
    try {
      const result = await this.aiSdk.generateText({
        prompt: `Search for educational content about: ${searchQuery}`,
        provider: 'perplexity',
        model: 'sonar-pro',
        searchMode: 'research',
        maxSourceCount: 5,
        identifierName: 'content-generation',
        identifierValue: 'web-search',
      });

      if (!result.text) {
        return null;
      }

      return {
        content: result.text,
        sources: ['web-search-result'],
      };
    } catch (error) {
      this.logger.error('Error performing web search:', error);
      return null;
    }
  }

  async buildRagContext(
    teamId: string,
    snippetId: string,
    category: string,
    snippetName: string,
    snippetDescription: string,
    grade: string,
    useKnowledgeBase: boolean,
    useWebSearch: boolean,
  ): Promise<RagContext> {
    let context = '';
    const sourceReferences: string[] = [];
    const knowledgeBaseSources: string[] = [];
    const webSearchSources: string[] = [];

    const searchQuery = `${category} ${snippetName} ${snippetDescription} ${grade}`;

    // Retrieve from knowledge base if requested
    if (useKnowledgeBase) {
      const knowledgeBaseContext = await this.getKnowledgeBaseContext(
        teamId,
        snippetId,
        searchQuery,
      );
      if (knowledgeBaseContext) {
        context += `Knowledge Base Context:\n${knowledgeBaseContext.content}\n\n`;
        knowledgeBaseSources.push(...knowledgeBaseContext.sources);
        sourceReferences.push(...knowledgeBaseContext.sources);
      }
    }

    // Perform web search if requested
    if (useWebSearch) {
      const webSearchContext = await this.getWebSearchContext(searchQuery);
      if (webSearchContext) {
        context += `Web Search Context:\n${webSearchContext.content}\n\n`;
        webSearchSources.push(...webSearchContext.sources);
        sourceReferences.push(...webSearchContext.sources);
      }
    }

    return {
      content: context,
      sources: sourceReferences,
      knowledgeBaseSources,
      webSearchSources,
    };
  }
}
