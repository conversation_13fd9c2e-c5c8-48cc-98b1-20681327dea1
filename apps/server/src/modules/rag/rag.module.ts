import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { LlmModule } from '../../llm/llm.module';
import { KnowledgeBaseModule } from '../knowledge-base/knowledge-base.module';
import { VectorStoreModule } from '../vector-store/vector-store.module';
import { RagService } from './rag.service';

@Module({
  imports: [
    ScheduleModule.forRoot(), // Enable cron jobs
    VectorStoreModule,
    KnowledgeBaseModule,
    LlmModule,
  ],
  providers: [RagService],
  exports: [RagService],
})
export class RagModule {}
