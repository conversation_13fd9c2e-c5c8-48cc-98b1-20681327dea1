import { KnowledgeBaseType } from '@/modules/knowledge-base/dto/knowledge-base.dto';

export interface VectorDocument {
  id: string;
  vector: number[];
  text: string;
  metadata: {
    description?: string;
    tags?: string[];
    url?: string;
    title?: string;
    fileExtension?: string;
    sourceType: KnowledgeBaseType;
    ingestionId?: string; // For backward compatibility
    knowledgeBaseId?: string; // New field for knowledge base
    chunkIndex?: number; // Index of this chunk within the document
    totalChunks?: number; // Total number of chunks for this document
    isChunk?: boolean; // Whether this is a chunk of a larger document
    createdAt: string;
    updatedAt: string;
  };
}

export interface VectorSearchResult {
  id: string;
  score: number;
  text: string;
  metadata: VectorDocument['metadata'];
}

export interface VectorSearchOptions {
  topK?: number;
  filters?: any[];
  includeAttributes?: string[];
  threshold?: number;
}

export interface VectorStoreConfig {
  namespace?: string;
  distanceMetric?: 'cosine_distance';
}

export interface NamespaceStats {
  namespace: string;
  hasData: boolean;
  latestDocument: string | null;
}

export interface HybridSearchOptions extends VectorSearchOptions {
  vectorWeight?: number;
}

export interface SearchFilters {
  sourceType?: string;
  tags?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
  fileType?: string;
}

export interface TextSearchResult {
  items: VectorSearchResult[];
  total?: number;
}
