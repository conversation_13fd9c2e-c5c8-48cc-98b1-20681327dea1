import { Injectable, Logger } from '@nestjs/common';

import { NamespaceQueryResponse, Row } from '@turbopuffer/turbopuffer/resources/namespaces';
import { KnowledgeBaseType } from '../knowledge-base/dto/knowledge-base.dto';
import {
  HybridSearchOptions,
  SearchFilters,
  TextSearchResult,
  VectorSearchOptions,
  VectorSearchResult,
} from './types/vector-store.types';
import { VectorStoreService } from './vector-store.service';

@Injectable()
export class VectorSearchService {
  private readonly logger = new Logger(VectorSearchService.name);

  constructor(private readonly vectorStoreService: VectorStoreService) {}

  /**
   * Search for similar documents using vector similarity
   */
  async vectorSearch(
    queryVector: number[],
    options: VectorSearchOptions & { namespace: string },
  ): Promise<VectorSearchResult[]> {
    const { topK = 10, filters, includeAttributes, namespace, threshold } = options;

    try {
      this.logger.log(`Performing vector search with topK: ${topK} in namespace: ${namespace}`);

      const queryParams: any = {
        rank_by: ['vector', 'ANN', queryVector],
        top_k: topK,
        include_attributes: includeAttributes || true,
      };

      if (filters && filters.length > 0) {
        queryParams.filters = filters;
      }

      const response: NamespaceQueryResponse = await this.vectorStoreService
        .getClient()
        .namespace(namespace)
        .query(queryParams);

      let results: VectorSearchResult[] = (response.rows || []).map((result: Row) => ({
        id: String(result.id),
        score: result.$dist || 0,
        text: String(result.text || ''),
        metadata: {
          description: String(result.description || ''),
          tags: Array.isArray(result.tags) ? result.tags : [],
          url: String(result.url || ''),
          fileExtension: String(result.fileExtension || ''),
          title: String(result.title || ''),
          sourceType: result.sourceType as any,
          ingestionId: String(result.ingestionId || ''),
          knowledgeBaseId: String(result.knowledgeBaseId || ''),
          createdAt: String(result.createdAt || ''),
          updatedAt: String(result.updatedAt || result.createdAt || ''),
        },
      }));

      // Apply threshold filtering if specified
      if (threshold !== undefined) {
        results = results.filter((result) => result.score >= threshold);
      }

      this.logger.log(`Vector search completed, found ${results.length} results`);
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Vector search failed: ${errorMessage}`);
      throw new Error(`Vector search failed: ${errorMessage}`);
    }
  }

  /**
   * Full-text search using BM25 algorithm
   * Returns format expected by RAG service
   */
  async textSearch(
    options: VectorSearchOptions & { query: string; namespace: string },
  ): Promise<TextSearchResult> {
    const { query, topK = 10, filters, includeAttributes, namespace, threshold } = options;

    try {
      this.logger.log(`Performing text search for query: "${query}" in namespace: ${namespace}`);

      const queryParams: any = {
        rank_by: ['text', 'BM25', query],
        top_k: topK,
        include_attributes: includeAttributes || true,
      };

      if (filters && filters.length > 0) {
        queryParams.filters = filters;
      }

      const response = await this.vectorStoreService
        .getClient()
        .namespace(namespace)
        .query(queryParams);

      let results: VectorSearchResult[] = (response.rows || []).map((result: any) => ({
        id: String(result.id),
        score: result.$dist || 0,
        text: String(result.text || ''),
        metadata: {
          description: String(result.description || ''),
          tags: Array.isArray(result.tags) ? result.tags : [],
          url: String(result.url || ''),
          fileExtension: String(result.file_extension || ''),
          title: String(result.title || ''),
          sourceType: result.source_type,
          ingestionId: String(result.ingestion_id || ''),
          knowledgeBaseId: String(result.knowledge_base_id || ''),
          chunkIndex: result.chunk_index !== null ? Number(result.chunk_index) : undefined,
          totalChunks: result.total_chunks !== null ? Number(result.total_chunks) : undefined,
          isChunk: Boolean(result.is_chunk),
          createdAt: String(result.created_at || ''),
          updatedAt: String(result.updated_at || result.created_at || ''),
        },
      }));

      // Apply threshold filtering if specified
      if (threshold !== undefined) {
        results = results.filter((result) => result.score >= threshold);
      }

      this.logger.log(`Text search completed, found ${results.length} results`);

      return {
        items: results,
        total: results.length,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Text search failed: ${errorMessage}`);
      throw new Error(`Text search failed: ${errorMessage}`);
    }
  }

  /**
   * Hybrid search combining vector and text search
   * Uses multi-query for optimal performance
   */
  async hybridSearch(
    queryVector: number[],
    textQuery: string,
    options: HybridSearchOptions & { namespace: string },
  ): Promise<VectorSearchResult[]> {
    const { topK = 10, filters, vectorWeight = 0.5, namespace } = options;

    try {
      this.logger.log(
        `Performing hybrid search with vector and text queries in namespace: ${namespace}`,
      );

      // Execute both searches separately and combine results
      const [vectorResults, textSearchResult] = await Promise.all([
        this.vectorSearch(queryVector, { topK: topK * 2, filters, namespace }),
        this.textSearch({ query: textQuery, topK: topK * 2, filters, namespace }),
      ]);

      // Combine results using reciprocal rank fusion
      const combinedResults = this.combineResultsWithRrf(
        [{ results: vectorResults }, { results: textSearchResult.items }],
        topK,
        vectorWeight,
      );

      this.logger.log(`Hybrid search completed, found ${combinedResults.length} results`);
      return combinedResults;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Hybrid search failed: ${errorMessage}`);
      throw new Error(`Hybrid search failed: ${errorMessage}`);
    }
  }

  /**
   * List documents with optional filtering and ordering
   */
  async listDocuments(
    limit: number = 20,
    filters: any[] | undefined,
    orderBy: string = 'created_at',
    orderDirection: 'asc' | 'desc' = 'desc',
    namespace: string,
  ): Promise<VectorSearchResult[]> {
    try {
      this.logger.log(
        `Listing documents with limit: ${limit}, order: ${orderBy} ${orderDirection} in namespace: ${namespace}`,
      );

      const queryParams: any = {
        rank_by: [orderBy, orderDirection],
        top_k: limit,
        include_attributes: true,
      };

      if (filters && filters.length > 0) {
        queryParams.filters = filters;
      }

      const response = await this.vectorStoreService
        .getClient()
        .namespace(namespace)
        .query(queryParams);

      const results: VectorSearchResult[] = (response.rows || []).map((result: Row) => ({
        id: String(result.id),
        score: 0, // Not applicable for listing
        text: String(result.text || ''),
        metadata: {
          description: String(result.description || ''),
          tags: Array.isArray(result.tags) ? result.tags : [],
          url: String(result.url || ''),
          fileExtension: String(result.file_extension || ''),
          title: String(result.title || ''),
          sourceType: result.source_type as KnowledgeBaseType,
          ingestionId: String(result.ingestion_id || ''),
          knowledgeBaseId: String(result.knowledge_base_id || ''),
          chunkIndex: result.chunk_index !== null ? Number(result.chunk_index) : undefined,
          totalChunks: result.total_chunks !== null ? Number(result.total_chunks) : undefined,
          isChunk: Boolean(result.is_chunk),
          createdAt: String(result.created_at || ''),
          updatedAt: String(result.updated_at || result.created_at || ''),
        },
      }));

      this.logger.log(`Listed ${results.length} documents`);
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to list documents: ${errorMessage}`);
      throw new Error(`Failed to list documents: ${errorMessage}`);
    }
  }

  /**
   * Delete documents by ID
   */
  async deleteById(id: string, namespace: string): Promise<void> {
    try {
      await this.vectorStoreService.deleteByFilter(['id', 'Eq', id], namespace);
      this.logger.log(`Deleted document with ID: ${id} from namespace: ${namespace}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to delete document ${id}: ${errorMessage}`);
      throw new Error(`Failed to delete document: ${errorMessage}`);
    }
  }

  /**
   * Delete entire namespace
   */
  async deleteNamespace(namespace: string): Promise<number> {
    try {
      return await this.vectorStoreService.deleteNamespace(namespace);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to delete namespace ${namespace}: ${errorMessage}`);
      throw new Error(`Failed to delete namespace: ${errorMessage}`);
    }
  }

  /**
   * Legacy search method for backward compatibility
   * @deprecated Use textSearch instead
   */
  async search(
    options: VectorSearchOptions & { query: string; namespace: string },
  ): Promise<TextSearchResult> {
    return this.textSearch(options);
  }

  /**
   * Search with advanced filters
   */
  async searchWithFilters(
    query: string | number[],
    searchType: 'vector' | 'text' | 'hybrid',
    filters: SearchFilters,
    options: VectorSearchOptions & { namespace: string },
  ): Promise<VectorSearchResult[]> {
    const turbopufferFilters = this.buildTurbopufferFilters(filters);
    const searchOptions = { ...options, filters: turbopufferFilters };

    switch (searchType) {
      case 'vector':
        if (typeof query === 'string') {
          throw new Error('Vector search requires a number array, not a string');
        }
        return this.vectorSearch(query, searchOptions);
      case 'text':
        if (typeof query !== 'string') {
          throw new Error('Text search requires a string query, not a number array');
        }
        const result = await this.textSearch({ query, ...searchOptions });
        return result.items;
      case 'hybrid':
        throw new Error('Hybrid search requires both vector and text queries');
      default:
        throw new Error(`Unknown search type: ${searchType}`);
    }
  }

  private buildTurbopufferFilters(filters: SearchFilters): any[] {
    const turbopufferFilters: any[] = [];

    if (filters.sourceType) {
      turbopufferFilters.push(['source_type', 'Eq', filters.sourceType]);
    }

    if (filters.fileType) {
      turbopufferFilters.push(['file_extension', 'Eq', filters.fileType]);
    }

    if (filters.tags && filters.tags.length > 0) {
      // For array fields, you might need to use different operators depending on Turbopuffer's capabilities
      filters.tags.forEach((tag) => {
        turbopufferFilters.push(['tags', 'Contains', tag]);
      });
    }

    if (filters.dateRange) {
      turbopufferFilters.push(['created_at', 'Gte', filters.dateRange.from]);
      turbopufferFilters.push(['created_at', 'Lte', filters.dateRange.to]);
    }

    return turbopufferFilters;
  }

  private combineResultsWithRrf(
    queryResults: any[],
    topK: number,
    vectorWeight: number,
  ): VectorSearchResult[] {
    const resultMap = new Map<string, VectorSearchResult & { rrfScore: number }>();

    queryResults.forEach((queryResult, queryIndex) => {
      const weight = queryIndex === 0 ? vectorWeight : 1 - vectorWeight;
      queryResult.results.forEach((result: VectorSearchResult, rank: number) => {
        const rrfScore = weight / (60 + rank + 1); // RRF formula with k=60

        if (resultMap.has(result.id)) {
          const existing = resultMap.get(result.id)!;
          existing.rrfScore += rrfScore;
        } else {
          resultMap.set(result.id, { ...result, rrfScore });
        }
      });
    });

    // Sort by RRF score and return top K
    return Array.from(resultMap.values())
      .sort((a, b) => b.rrfScore - a.rrfScore)
      .slice(0, topK)
      .map(({ rrfScore, ...result }) => result);
  }

  /**
   * Get namespace for specific team/tenant
   */
  getNamespaceForTeam(teamId: string): string {
    if (!teamId) {
      throw new Error('Team ID is required for namespace generation');
    }
    return teamId;
  }

  /**
   * Get namespace for temporary snippet storage
   */
  getNamespaceForSnippet(teamId: string, snippetId: string): string {
    if (!teamId) {
      throw new Error('Team ID is required for snippet namespace generation');
    }
    if (!snippetId) {
      throw new Error('Snippet ID is required for snippet namespace generation');
    }
    return `${teamId}_${snippetId}`;
  }
}
