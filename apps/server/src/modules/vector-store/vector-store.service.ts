import { Injectable, Logger } from '@nestjs/common';
import Turbopuffer from '@turbopuffer/turbopuffer';

import config from '@/common/configs/config';
import { NamespaceStats, VectorDocument } from './types/vector-store.types';

@Injectable()
export class VectorStoreService {
  private readonly logger = new Logger(VectorStoreService.name);
  private readonly turbopuffer: Turbopuffer;
  private readonly defaultNamespace: string;
  private readonly distanceMetric: 'cosine_distance';

  constructor() {
    this.turbopuffer = new Turbopuffer({
      apiKey: config().turbopuffer?.apiKey || process.env.TURBOPUFFER_API_KEY,
      region: process.env.TURBOPUFFER_REGION || 'gcp-us-central1',
    });

    this.defaultNamespace = 'default'; // TODO: make this dynamic based on business id
    this.distanceMetric = 'cosine_distance';
  }

  /**
   * Store a single document in the vector database
   * Uses row-based writes for single documents as recommended by Turbopuffer
   */
  async storeDocument(document: VectorDocument, namespace: string): Promise<string> {
    try {
      this.logger.log(`Storing document with ID: ${document.id} in namespace: ${namespace}`);

      const response = await this.turbopuffer.namespace(namespace).write({
        upsert_rows: [
          {
            id: document.id,
            vector: document.vector,
            text: document.text,
            description: document.metadata.description || null,
            tags: document.metadata.tags || [],
            url: document.metadata.url || null,
            title: document.metadata.title || null,
            file_extension: document.metadata.fileExtension || null,
            source_type: document.metadata.sourceType,
            ingestion_id: document.metadata.ingestionId || null,
            knowledge_base_id: document.metadata.knowledgeBaseId || null,
            chunk_index: document.metadata.chunkIndex || null,
            total_chunks: document.metadata.totalChunks || null,
            is_chunk: document.metadata.isChunk || false,
            created_at: document.metadata.createdAt,
            updated_at: document.metadata.updatedAt,
          },
        ],
        distance_metric: this.distanceMetric,
        schema: this.getSchema(),
      });

      this.logger.log(
        `Document stored successfully with ID: ${document.id}, rows affected: ${response.rows_affected}`,
      );
      return document.id;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to store document ${document.id}: ${errorMessage}`);
      throw new Error(`Failed to store document: ${errorMessage}`);
    }
  }

  /**
   * Store multiple documents in batch for better performance
   * Uses column-based writes for bulk operations as recommended by Turbopuffer
   */
  async storeDocumentsBatch(documents: VectorDocument[], namespace: string): Promise<string[]> {
    if (documents.length === 0) {
      return [];
    }

    try {
      this.logger.log(`Storing ${documents.length} documents in batch in namespace: ${namespace}`);

      // Prepare column-based data for optimal performance
      const columnData = {
        id: documents.map((doc) => doc.id),
        vector: documents.map((doc) => doc.vector),
        text: documents.map((doc) => doc.text),
        description: documents.map((doc) => doc.metadata.description || null),
        tags: documents.map((doc) => doc.metadata.tags || []),
        url: documents.map((doc) => doc.metadata.url || null),
        file_extension: documents.map((doc) => doc.metadata.fileExtension || null),
        title: documents.map((doc) => doc.metadata.title || null),
        source_type: documents.map((doc) => doc.metadata.sourceType),
        ingestion_id: documents.map((doc) => doc.metadata.ingestionId || null),
        knowledge_base_id: documents.map((doc) => doc.metadata.knowledgeBaseId || null),
        chunk_index: documents.map((doc) => doc.metadata.chunkIndex || null),
        total_chunks: documents.map((doc) => doc.metadata.totalChunks || null),
        is_chunk: documents.map((doc) => doc.metadata.isChunk || false),
        created_at: documents.map((doc) => doc.metadata.createdAt),
        updated_at: documents.map((doc) => doc.metadata.updatedAt),
      };

      const response = await this.turbopuffer.namespace(namespace).write({
        upsert_columns: columnData,
        distance_metric: this.distanceMetric,
        schema: this.getSchema(),
      });

      const storedIds = documents.map((doc) => doc.id);
      this.logger.log(
        `Successfully stored ${storedIds.length} documents in batch, rows affected: ${response.rows_affected}`,
      );
      return storedIds;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to store documents batch: ${errorMessage}`);
      throw new Error(`Failed to store documents batch: ${errorMessage}`);
    }
  }

  /**
   * Delete documents by ingestion ID
   */
  async deleteByIngestionId(ingestionId: string, namespace: string): Promise<number> {
    try {
      this.logger.log(
        `Deleting documents for ingestion ID: ${ingestionId} in namespace: ${namespace}`,
      );

      const response = await this.turbopuffer.namespace(namespace).write({
        delete_by_filter: ['ingestion_id', 'Eq', ingestionId],
      });

      this.logger.log(`Deleted documents for ingestion ID: ${ingestionId}`);
      return response.rows_affected || 0;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Failed to delete documents for ingestion ID ${ingestionId}: ${errorMessage}`,
      );
      throw new Error(`Failed to delete documents: ${errorMessage}`);
    }
  }

  /**
   * Delete documents by knowledge base ID
   */
  async deleteByKnowledgeBaseId(knowledgeBaseId: string, namespace: string): Promise<number> {
    try {
      this.logger.log(
        `Deleting documents for knowledge base ID: ${knowledgeBaseId} in namespace: ${namespace}`,
      );

      // Try to delete by knowledge_base_id first, then fallback to ingestion_id for backward compatibility
      const response1 = await this.turbopuffer.namespace(namespace).write({
        delete_by_filter: ['knowledge_base_id', 'Eq', knowledgeBaseId],
      });

      let totalDeleted = response1.rows_affected || 0;

      // Also try deleting by ingestion_id for backward compatibility
      const response2 = await this.turbopuffer.namespace(namespace).write({
        delete_by_filter: ['ingestion_id', 'Eq', knowledgeBaseId],
      });

      totalDeleted += response2.rows_affected || 0;

      this.logger.log(
        `Deleted documents for knowledge base ID: ${knowledgeBaseId}, total: ${totalDeleted}`,
      );
      return totalDeleted;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Failed to delete documents for knowledge base ID ${knowledgeBaseId}: ${errorMessage}`,
      );
      throw new Error(`Failed to delete documents: ${errorMessage}`);
    }
  }

  /**
   * Delete documents by filter
   */
  async deleteByFilter(filter: any[], namespace: string): Promise<number> {
    try {
      this.logger.log(
        `Deleting documents with filter: ${JSON.stringify(filter)} in namespace: ${namespace}`,
      );

      const response = await this.turbopuffer.namespace(namespace).write({
        delete_by_filter: filter,
      });

      this.logger.log(`Deleted documents with filter`);
      return response.rows_affected || 0;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to delete documents with filter: ${errorMessage}`);
      throw new Error(`Failed to delete documents: ${errorMessage}`);
    }
  }

  /**
   * Delete entire namespace
   */
  async deleteNamespace(namespace: string): Promise<number> {
    try {
      this.logger.log(`Deleting entire namespace: ${namespace}`);

      // Delete all documents in the namespace
      const response = await this.turbopuffer.namespace(namespace).write({
        delete_by_filter: ['created_at', 'Gte', '1970-01-01T00:00:00Z'], // Delete all documents
      });

      this.logger.log(
        `Deleted namespace: ${namespace}, documents deleted: ${response.rows_affected || 0}`,
      );
      return response.rows_affected || 0;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to delete namespace ${namespace}: ${errorMessage}`);
      throw new Error(`Failed to delete namespace: ${errorMessage}`);
    }
  }

  /**
   * Get namespace statistics
   */
  async getNamespaceStats(namespace: string): Promise<NamespaceStats> {
    try {
      const response = await this.turbopuffer.namespace(namespace).query({
        rank_by: ['created_at', 'desc'],
        top_k: 1,
        include_attributes: ['created_at'],
      });

      return {
        namespace: namespace,
        hasData: Boolean(response.rows && response.rows.length > 0),
        latestDocument: (response.rows?.[0]?.created_at as string) || null,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to get namespace stats: ${errorMessage}`);
      return {
        namespace: namespace,
        hasData: false,
        latestDocument: null,
      };
    }
  }

  /**
   * Get the default namespace
   */
  getNamespace(): string {
    return this.defaultNamespace;
  }

  /**
   * Get the Turbopuffer client instance
   */
  getClient(): Turbopuffer {
    return this.turbopuffer;
  }

  /**
   * Get namespace for specific team/tenant
   */
  getNamespaceForTeam(teamId: string): string {
    if (!teamId) {
      throw new Error('Team ID is required for namespace generation');
    }
    return teamId;
  }

  /**
   * Get namespace for temporary snippet storage
   */
  getNamespaceForSnippet(teamId: string, snippetId: string): string {
    if (!teamId) {
      throw new Error('Team ID is required for snippet namespace generation');
    }
    if (!snippetId) {
      throw new Error('Snippet ID is required for snippet namespace generation');
    }
    return `${teamId}_${snippetId}`;
  }

  private getSchema() {
    return {
      text: { type: 'string', full_text_search: true },
      description: { type: 'string', full_text_search: true },
      tags: { type: '[]string', filterable: true },
      url: { type: 'string', filterable: true },
      title: { type: 'string', filterable: true },
      file_extension: { type: 'string', filterable: true },
      source_type: { type: 'string', filterable: true },
      ingestion_id: { type: 'string', filterable: true },
      knowledge_base_id: { type: 'string', filterable: true },
      chunk_index: { type: 'int', filterable: true },
      total_chunks: { type: 'int', filterable: true },
      is_chunk: { type: 'bool', filterable: true },
      created_at: { type: 'datetime', filterable: true },
      updated_at: { type: 'datetime', filterable: true },
    };
  }
}
