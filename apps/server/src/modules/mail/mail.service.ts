import type { MailPayload } from './mail.types';
import type { Queue } from 'bullmq';

import { InjectQueue } from '@nestjs/bullmq';

import { MAIL_QUEUE_NAME, MailQueueJobs } from './mail.constants';

export abstract class MailService {
  constructor(@InjectQueue(MAIL_QUEUE_NAME) private readonly mailQueue: Queue<MailPayload>) {}

  abstract send(payload: MailPayload): Promise<void>;

  async addToMailQueue(payload: MailPayload): Promise<void> {
    await this.mailQueue.add(MailQueueJobs.SendMail, payload);
  }
}
