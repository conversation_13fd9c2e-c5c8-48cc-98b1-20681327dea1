import type {
  ApiClient as MailchimpClient,
  MessagesSendResponse,
} from '@mailchimp/mailchimp_transactional';
import type { MailPayload } from '../mail.types';
import type { AxiosError } from 'axios';

import { Injectable, Logger } from '@nestjs/common';
import Mailchimp from '@mailchimp/mailchimp_transactional';

import config from '@/common/configs/config';

import { getEmailData } from '../mail.utils';
import { MailService } from '../mail.service';

@Injectable()
export class MailchimpService extends MailService {
  private mailchimpClient: MailchimpClient = Mailchimp(config().mailchimp.apiKey);
  private readonly logger: Logger = new Logger(MailchimpService.name);

  async send(mailPayload: MailPayload): Promise<void> {
    try {
      const { html, subject } = await getEmailData(mailPayload);
      const message = {
        from_name: config().app.name,
        from_email: mailPayload.from || config().app.mails.support,
        to: [{ email: mailPayload.to, name: mailPayload.name }],
        subject,
        html,
      };

      await this.mailchimpClient.messages
        .send({ message })
        .then((resp: MessagesSendResponse[] | AxiosError<unknown, any>) => {
          if ((resp[0] as MessagesSendResponse)?.status === 'sent') {
            this.logger.log(`Email Sent: ${mailPayload.to} - ${mailPayload.subject}`);
          }
        });
    } catch (error) {
      this.logger.log('Email Error:', error);
    }
  }
}
