import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';

import { MAIL_QUEUE_JOB_OPTIONS, MAIL_QUEUE_NAME } from './mail.constants';
import { MailProcessor } from './mail.processor';

@Module({
  imports: [
    BullModule.registerQueue({
      defaultJobOptions: MAIL_QUEUE_JOB_OPTIONS,
      name: MAIL_QUEUE_NAME,
    }),
  ],
  providers: [MailProcessor],
  exports: [BullModule],
})
export class MailQueueModule {}
