import type { MailPayload } from './mail.types';

import { Injectable } from '@nestjs/common';
import previewMail from 'preview-email';

import { getEmailData } from './mail.utils';
import { MailService } from './mail.service';

@Injectable()
export class MailPreviewService extends MailService {
  async send(mailPayload: MailPayload): Promise<any> {
    return getEmailData(mailPayload).then((mailData) => {
      return previewMail(mailData, { openSimulator: false });
    });
  }
}
