import type { MailPayload } from './mail.types';
import type { Job } from 'bullmq';

import { WorkerHost, Processor } from '@nestjs/bullmq';

import { MAIL_QUEUE_NAME } from './mail.constants';
import { MailService } from './mail.service';

@Processor(MAIL_QUEUE_NAME, { concurrency: 10 })
export class MailProcessor extends WorkerHost {
  constructor(private readonly mailService: MailService) {
    super();
  }

  async process({ data }: Job<MailPayload>): Promise<void> {
    return await this.mailService.send(data);
  }
}
