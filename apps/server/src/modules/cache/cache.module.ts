import type { Cache } from 'cache-manager';

import { CacheModule as NestCacheModule, CACHE_MANAGER } from '@nestjs/cache-manager';
import { Global, Inject, Module } from '@nestjs/common';
import { createKeyv } from '@keyv/redis';

import { MILLISECONDS_IN } from '@ps/common/constants/time';
import config from '@/common/configs/config';

import { LocalCacheService } from './local-cache.service';
import { NodeCacheService } from './node-cache/node-cache.service';

@Global()
@Module({
  providers: [NodeCacheService, { provide: LocalCacheService, useExisting: NodeCacheService }],
  exports: [LocalCacheService],
})
export class CacheModule {
  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache) {}

  static forRoot() {
    return {
      module: CacheModule,
      imports: [
        NestCacheModule.register({
          isGlobal: true,
          ttl: config().isProd ? MILLISECONDS_IN.FIFTEEN_MINUTES : 1,
          store: createKeyv({ socket: config().redis }),
        }),
      ],
    };
  }
}
