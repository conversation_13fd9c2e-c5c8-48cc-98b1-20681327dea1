import { Injectable } from '@nestjs/common';
import NodeCache from 'node-cache';

import { LocalCacheService } from '../local-cache.service';

const STANDARD_TTL = 5 * 60;

@Injectable()
export class NodeCacheService implements LocalCacheService {
  nodeCache: NodeCache;

  constructor() {
    this.nodeCache = new NodeCache({ stdTTL: STANDARD_TTL });
  }

  get<T>(key: string): T {
    return this.nodeCache.get(key) as T;
  }

  set<T>(key: string, data: T, timeout = STANDARD_TTL): T {
    this.nodeCache.set(key, data, timeout);
    return data;
  }

  del(key: string): void {
    this.nodeCache.del(key);
  }

  reset(): void {
    this.nodeCache.flushAll();
  }
}
