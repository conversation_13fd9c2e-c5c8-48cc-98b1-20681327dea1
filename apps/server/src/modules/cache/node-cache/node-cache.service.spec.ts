import { Test, TestingModule } from '@nestjs/testing';

import { NodeCacheService } from './node-cache.service';

const testKeys: string[] = ['TEST_KEY_1', 'TEST_KEY_2', 'TEST_KEY_3'];
const testData: string[] = ['TEST_DATA_1', 'TEST_DATA_2', 'TEST_DATA_3'];

describe('NodeCacheService', () => {
  let service: NodeCacheService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [NodeCacheService],
    }).compile();

    service = module.get<NodeCacheService>(NodeCacheService);
  });

  afterAll(() => module.close());

  it('should set and get data from the cache', () => {
    expect(service).toBeDefined();
    expect(service.get).toBeDefined();
    expect(service.set).toBeDefined();
    service.set(testKeys[0]!, testData[0]!);
    const d = service.get(testKeys[0]!);
    expect(d).toBe(testData[0]);
  });

  it('should delete data stored on the cache', () => {
    expect(service.del).toBeDefined();
    service.set(testKeys[0]!, testData[0]!);
    const d1 = service.get(testKeys[0]!);
    expect(d1).toBeDefined();
    service.del(testKeys[0]!);
    const d2 = service.get(testKeys[0]!);
    expect(d2).toBeUndefined();
  });

  it('should clear all stored data on the cache', () => {
    expect(service.reset).toBeDefined();
    testKeys.forEach((tk, i) => {
      service.set(tk, testData[i]);
      const d = service.get(tk);
      expect(d).toBeDefined();
    });
    service.reset();
    testKeys.forEach((tk) => {
      const d = service.get(tk);
      expect(d).toBeUndefined();
    });
  });
});
