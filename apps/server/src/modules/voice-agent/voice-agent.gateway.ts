import { Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { GeminiLiveService } from './gemini-live.service';
import { VoiceAgentService } from './voice-agent.service';

interface VoiceSession {
  sessionId: string;
  userId?: string;
  conversationHistory: string[];
  isProcessing: boolean;
  // Live API specific
  isLiveSession?: boolean;
  liveStreamActive?: boolean;
}

@WebSocketGateway({
  namespace: '/voice-agent',
  cors: {
    origin: ['http://localhost:3000', 'https://localhost:3000'],
    credentials: true,
  },
})
export class VoiceAgentGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(VoiceAgentGateway.name);
  private readonly sessions = new Map<string, VoiceSession>();

  constructor(
    private readonly voiceAgentService: VoiceAgentService,
    private readonly geminiLiveService: GeminiLiveService,
  ) {}

  afterInit(__server: Server) {
    this.logger.log('Voice Agent WebSocket Gateway initialized');
  }

  handleConnection(client: Socket) {
    const sessionId = client.id;
    this.logger.log(`Client connected: ${sessionId}`);

    // Initialize session
    this.sessions.set(sessionId, {
      sessionId,
      conversationHistory: [],
      isProcessing: false,
    });

    client.emit('connected', { sessionId });
  }

  handleDisconnect(client: Socket) {
    const sessionId = client.id;
    this.logger.log(`Client disconnected: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (session?.isLiveSession) {
      // Clean up Live session
      this.geminiLiveService.stopLiveSession(sessionId).catch((error) => {
        this.logger.error(`Error cleaning up Live session on disconnect:`, error);
      });
    }

    this.sessions.delete(sessionId);
  }

  @SubscribeMessage('voice_message')
  async handleVoiceMessage(
    @MessageBody() data: { audioData: string; mimeType: string },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    // 🔥 TIMING LOG: Audio received from client
    this.logger.log(`[${sessionId}] 📥 Audio received from client at ${new Date().toISOString()}`);
    this.logger.log(
      `[${sessionId}] 📊 Audio data size: ${data.audioData.length} chars (base64), MIME: ${data.mimeType}`,
    );

    if (!session) {
      client.emit('error', { message: 'Session not found' });
      return;
    }

    if (session.isProcessing) {
      client.emit('error', { message: 'Already processing a message' });
      return;
    }

    try {
      session.isProcessing = true;
      this.sessions.set(sessionId, session);

      // Convert base64 audio data to buffer
      const audioConversionStart = Date.now();
      const audioBuffer = Buffer.from(data.audioData, 'base64');
      const audioConversionTime = Date.now() - audioConversionStart;

      this.logger.log(`[${sessionId}] 🔄 Audio conversion took: ${audioConversionTime}ms`);
      this.logger.log(`[${sessionId}] 📊 Audio buffer size: ${audioBuffer.length} bytes`);

      // Emit processing status
      client.emit('processing_status', { status: 'transcribing' });

      // 🔥 TIMING LOG: About to send to Gemini
      const geminiStartTime = Date.now();
      this.logger.log(`[${sessionId}] 🚀 Sending to Gemini at ${new Date().toISOString()}`);

      // Process the voice message using the streaming generator
      const processor = this.voiceAgentService.streamVoiceProcessing(
        audioBuffer,
        data.mimeType,
        session.conversationHistory,
        { sessionId, startTime: geminiStartTime }, // Pass timing context
      );

      for await (const step of processor) {
        switch (step.type) {
          case 'response_chunk':
            // Stream text chunks as they arrive from Gemini
            client.emit('ai_response_chunk', { chunk: step.data });
            break;

          case 'response_complete':
            // Complete response received
            client.emit('ai_response', { text: step.data });
            session.conversationHistory.push(`Assistant: ${step.data}`);
            client.emit('processing_status', { status: 'generating_audio' });
            break;

          case 'audio':
            const audioBase64 = step.data.toString('base64');
            client.emit('audio_response', {
              audioData: audioBase64,
              mimeType: 'audio/mpeg',
            });
            client.emit('processing_status', { status: 'completed' });
            break;
        }
      }

      // Limit conversation history to last 10 exchanges
      if (session.conversationHistory.length > 20) {
        session.conversationHistory = session.conversationHistory.slice(-20);
      }
    } catch (error) {
      this.logger.error(`Error processing voice message for ${sessionId}:`, error);
      client.emit('error', {
        message: error instanceof Error ? error.message : 'Failed to process voice message',
      });
    } finally {
      session.isProcessing = false;
      this.sessions.set(sessionId, session);
    }
  }

  @SubscribeMessage('clear_conversation')
  async handleClearConversation(@ConnectedSocket() client: Socket) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (session) {
      session.conversationHistory = [];
      this.sessions.set(sessionId, session);
      client.emit('conversation_cleared');
    }
  }

  @SubscribeMessage('get_conversation_history')
  async handleGetConversationHistory(@ConnectedSocket() client: Socket) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (session) {
      client.emit('conversation_history', {
        history: session.conversationHistory,
      });
    }
  }

  @SubscribeMessage('text_to_speech')
  async handleTextToSpeech(
    @MessageBody() data: { text: string },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;

    try {
      client.emit('processing_status', { status: 'generating_audio' });

      const audioBuffer = await this.voiceAgentService.textToSpeech(data.text);
      const audioBase64 = audioBuffer.toString('base64');

      client.emit('audio_response', {
        audioData: audioBase64,
        mimeType: 'audio/mpeg',
      });

      client.emit('processing_status', { status: 'completed' });
    } catch (error) {
      this.logger.error(`Error generating TTS for ${sessionId}:`, error);
      client.emit('error', {
        message: error instanceof Error ? error.message : 'Failed to generate speech',
      });
    }
  }

  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong');
  }

  @SubscribeMessage('test_event')
  handleTestEvent(@ConnectedSocket() client: Socket) {
    this.logger.log(`[${client.id}] 🧪 Test event received!`);
    client.emit('test_response', { message: 'Test successful!' });
  }

  @SubscribeMessage('start_real_time_audio')
  async handleStartRealTimeAudio(@ConnectedSocket() client: Socket) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (!session) {
      client.emit('error', { message: 'Session not found' });
      return;
    }

    if (session.isProcessing) {
      client.emit('error', { message: 'Already processing' });
      return;
    }

    // Initialize real-time processing state
    session.isProcessing = true;
    session['audioChunks'] = [];
    session['lastChunkTime'] = Date.now();
    session['partialResponse'] = '';

    this.logger.log(`[${sessionId}] 🎙️ Real-time audio processing started`);
    client.emit('real_time_ready', { message: 'Ready for audio chunks' });

    this.sessions.set(sessionId, session);
  }

  @SubscribeMessage('audio_chunk')
  async handleAudioChunk(
    @MessageBody() data: { audioData: string; mimeType: string; chunkIndex: number },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (!session || !session.isProcessing) {
      client.emit('error', { message: 'Real-time session not active' });
      return;
    }

    try {
      const audioBuffer = Buffer.from(data.audioData, 'base64');
      const currentTime = Date.now();

      // Initialize audioChunks if not exists
      if (!session['audioChunks']) {
        session['audioChunks'] = [];
      }

      session['audioChunks'].push(audioBuffer);
      session['lastChunkTime'] = currentTime;

      this.logger.log(`[${sessionId}] 📥 Chunk ${data.chunkIndex}: ${audioBuffer.length} bytes`);

      // Emit chunk received confirmation
      client.emit('chunk_received', {
        chunkIndex: data.chunkIndex,
        totalChunks: session['audioChunks'].length,
        totalSize: session['audioChunks'].reduce(
          (sum: number, chunk: Buffer) => sum + chunk.length,
          0,
        ),
      });

      // Process every 3 chunks for partial results (adjust as needed)
      if (session['audioChunks'].length >= 3) {
        this.processPartialAudio(sessionId, session, data.mimeType, client);
      }

      this.sessions.set(sessionId, session);
    } catch (error) {
      this.logger.error(`[${sessionId}] Error handling audio chunk:`, error);
      client.emit('error', { message: 'Chunk processing error' });
    }
  }

  @SubscribeMessage('end_real_time_audio')
  async handleEndRealTimeAudio(
    @MessageBody() data: { mimeType: string },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (!session || !session['audioChunks']) {
      client.emit('error', { message: 'No active real-time session' });
      return;
    }

    try {
      this.logger.log(`[${sessionId}] 🏁 Finalizing ${session['audioChunks'].length} audio chunks`);

      // Process final audio
      const { response } = await this.voiceAgentService.processAudioChunks(
        session['audioChunks'],
        data.mimeType,
        sessionId,
        false, // Final processing
      );

      // Add to conversation history
      session.conversationHistory.push(`Assistant: ${response}`);

      // Emit final response
      client.emit('final_response', { text: response });

      // Generate TTS
      client.emit('processing_status', { status: 'generating_audio' });
      const audioResponse = await this.voiceAgentService.textToSpeech(response);
      const audioBase64 = audioResponse.toString('base64');

      client.emit('audio_response', {
        audioData: audioBase64,
        mimeType: 'audio/mpeg',
      });

      client.emit('processing_status', { status: 'completed' });

      // Clean up
      session.isProcessing = false;
      delete session['audioChunks'];
      delete session['lastChunkTime'];
      delete session['partialResponse'];

      this.sessions.set(sessionId, session);
    } catch (error) {
      this.logger.error(`[${sessionId}] Error finalizing real-time audio:`, error);
      client.emit('error', { message: 'Finalization error' });

      // Clean up on error
      session.isProcessing = false;
      this.sessions.set(sessionId, session);
    }
  }

  private async processPartialAudio(
    sessionId: string,
    session: VoiceSession,
    mimeType: string,
    client: Socket,
  ) {
    try {
      const { response } = await this.voiceAgentService.processAudioChunks(
        session['audioChunks'],
        mimeType,
        sessionId,
        true, // Partial processing
      );

      // Emit partial response for real-time feedback
      client.emit('partial_response', { text: response });
      session['partialResponse'] = response;

      // Keep only recent chunks to save memory
      if (session['audioChunks'].length > 6) {
        session['audioChunks'] = session['audioChunks'].slice(-3);
        this.logger.log(`[${sessionId}] 🧹 Trimmed to last 3 chunks`);
      }
    } catch (error) {
      this.logger.error(`[${sessionId}] Error in partial processing:`, error);
      // Don't emit error for partial processing failures, just log
    }
  }

  /**
   * 🚀 LIVE API: Start Gemini Live API session with native audio
   */
  @SubscribeMessage('start_live_session')
  async handleStartLiveSession(
    @MessageBody() data: { model?: string; systemInstruction?: string },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;
    this.logger.log(`[${sessionId}] 📨 Received start_live_session event with data:`, data);

    const session = this.sessions.get(sessionId);

    if (!session) {
      this.logger.error(`[${sessionId}] ❌ Session not found`);
      client.emit('error', { message: 'Session not found' });
      return;
    }

    if (session.isProcessing || session.isLiveSession) {
      this.logger.error(`[${sessionId}] ❌ Session already active`);
      client.emit('error', { message: 'Session already active' });
      return;
    }

    try {
      const startTime = Date.now();
      this.logger.log(`[${sessionId}] 🚀 Starting Gemini Live API session`);

      // Check if GeminiLiveService is available
      if (!this.geminiLiveService) {
        throw new Error('GeminiLiveService not available');
      }

      this.logger.log(`[${sessionId}] 🤖 Calling geminiLiveService.startLiveSession...`);

      // Start Live API session
      await this.geminiLiveService.startLiveSession(sessionId, {
        model: data.model,
        systemInstruction: data.systemInstruction,
        responseModalities: ['AUDIO'], // Native audio output
      });

      this.logger.log(
        `[${sessionId}] ✅ GeminiLiveService.startLiveSession completed successfully`,
      );

      // Update session state
      session.isLiveSession = true;
      session.liveStreamActive = true;
      session.isProcessing = true;
      this.sessions.set(sessionId, session);

      const initTime = Date.now() - startTime;
      this.logger.log(`[${sessionId}] ✅ Live session started in ${initTime}ms`);

      // Emit started signal to trigger automatic audio streaming
      this.logger.log(`[${sessionId}] 📡 Emitting live_session_started to client`);
      client.emit('live_session_started', {
        message: 'Live session ready for audio streaming',
        sessionId,
        model: data.model || 'gemini-2.5-flash-preview-native-audio-dialog',
      });

      // Start response streaming
      this.startLiveResponseStreaming(sessionId, client);
    } catch (error) {
      this.logger.error(`[${sessionId}] Error starting Live session:`, error);

      // Clean up on error
      session.isLiveSession = false;
      session.liveStreamActive = false;
      session.isProcessing = false;
      this.sessions.set(sessionId, session);

      client.emit('error', {
        message: error instanceof Error ? error.message : 'Failed to start Live session',
      });
    }
  }

  /**
   * 🎙️ LIVE API: Send real-time audio chunks
   */
  @SubscribeMessage('live_audio_chunk')
  async handleLiveAudioChunk(
    @MessageBody() data: { audioData: string; mimeType?: string },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    this.logger.log(
      `[${sessionId}] 🎙️ Audio chunk received: ${data.audioData?.length || 0} chars, MIME: ${data.mimeType}`,
    );

    // Enhanced session validation with detailed logging
    if (!session) {
      this.logger.error(`[${sessionId}] ❌ No session found for audio chunk`);
      client.emit('error', { message: 'No active live session for audio chunk' });
      return;
    }

    // if (!session.isLiveSession) {
    //   this.logger.error(
    //     `[${sessionId}] ❌ Session exists but not a live session. State: ${JSON.stringify({
    //       isLiveSession: session.isLiveSession,
    //       liveStreamActive: session.liveStreamActive,
    //       isProcessing: session.isProcessing,
    //     })}`,
    //   );
    //   client.emit('error', { message: 'No active live session for audio chunk' });
    //   return;
    // }

    if (!session.liveStreamActive) {
      this.logger.error(
        `[${sessionId}] ❌ Live session exists but stream not active. State: ${JSON.stringify({
          isLiveSession: session.isLiveSession,
          liveStreamActive: session.liveStreamActive,
          isProcessing: session.isProcessing,
        })}`,
      );
      client.emit('error', { message: 'No active live session for audio chunk' });
      return;
    }

    try {
      const audioBuffer = Buffer.from(data.audioData, 'base64');
      const mimeType = data.mimeType || 'audio/pcm;rate=16000';

      this.logger.log(`[${sessionId}] 📤 Sending ${audioBuffer.length} bytes to Gemini Live API`);

      // Send audio to Live API
      await this.geminiLiveService.sendAudioChunk(sessionId, audioBuffer, mimeType);

      this.logger.log(`[${sessionId}] ✅ Audio chunk sent successfully to Gemini`);

      // Emit confirmation
      client.emit('audio_chunk_sent', {
        size: audioBuffer.length,
        mimeType,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`[${sessionId}] Error sending Live audio chunk:`, error);

      // If it's a Gemini Live API error, notify client to stop streaming
      if (error instanceof Error && error.message.includes('Live session')) {
        this.logger.warn(
          `[${sessionId}] 🛑 Live session error detected, stopping client streaming`,
        );
        session.isLiveSession = false;
        session.liveStreamActive = false;
        this.sessions.set(sessionId, session);
        client.emit('live_session_stopped', { message: 'Live session ended due to error' });
      }

      client.emit('error', { message: 'Audio chunk error' });
    }
  }

  /**
   * 🛑 LIVE API: Stop Live session
   */
  @SubscribeMessage('stop_live_session')
  async handleStopLiveSession(@ConnectedSocket() client: Socket) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (!session || !session.isLiveSession) {
      client.emit('error', { message: 'No active Live session' });
      return;
    }

    try {
      this.logger.log(`[${sessionId}] 🛑 Stopping Live session`);

      // Stop Live API session
      await this.geminiLiveService.stopLiveSession(sessionId);

      // Update session state
      session.isLiveSession = false;
      session.liveStreamActive = false;
      session.isProcessing = false;
      this.sessions.set(sessionId, session);

      this.logger.log(`[${sessionId}] ✅ Live session stopped`);
      client.emit('live_session_stopped', { message: 'Live session ended' });
    } catch (error) {
      this.logger.error(`[${sessionId}] Error stopping Live session:`, error);

      // Force cleanup
      session.isLiveSession = false;
      session.liveStreamActive = false;
      session.isProcessing = false;
      this.sessions.set(sessionId, session);

      client.emit('error', { message: 'Stop session error' });
    }
  }

  /**
   * 📊 LIVE API: Get session status
   */
  @SubscribeMessage('live_session_status')
  async handleLiveSessionStatus(@ConnectedSocket() client: Socket) {
    const sessionId = client.id;
    const session = this.sessions.get(sessionId);

    if (!session) {
      client.emit('error', { message: 'Session not found' });
      return;
    }

    const liveStatus = this.geminiLiveService.getSessionStatus(sessionId);

    client.emit('live_session_status_response', {
      sessionId,
      isLiveSession: session.isLiveSession || false,
      liveStreamActive: session.liveStreamActive || false,
      isProcessing: session.isProcessing || false,
      liveApiStatus: liveStatus,
      conversationHistory: session.conversationHistory.length,
    });
  }

  /**
   * 🌊 Start streaming responses from Live API
   */
  private async startLiveResponseStreaming(sessionId: string, client: Socket) {
    try {
      this.logger.log(`[${sessionId}] 🌊 Starting Live response stream`);

      for await (const response of this.geminiLiveService.streamResponses(sessionId)) {
        const session = this.sessions.get(sessionId);
        if (!session || !session.liveStreamActive) {
          this.logger.log(`[${sessionId}] 🔚 Live stream ended - session inactive`);
          break;
        }

        this.processLiveResponse(sessionId, response, client, session);
      }
    } catch (error) {
      this.logger.error(`[${sessionId}] Live response streaming error:`, error);
      client.emit('error', { message: 'Response streaming error' });
    }
  }

  /**
   * 🎤 Process Live API responses (audio and text)
   */
  private processLiveResponse(
    sessionId: string,
    response: any,
    client: Socket,
    session: VoiceSession,
  ) {
    try {
      this.logger.log(
        `[${sessionId}] 📤 Processing Live response:`,
        JSON.stringify(response).substring(0, 500),
      );

      // Handle different response types from Gemini Live API
      if (response.serverContent?.modelTurn?.parts) {
        for (const part of response.serverContent.modelTurn.parts) {
          // Handle audio parts
          if (part.inlineData?.data) {
            const mimeType = part.inlineData.mimeType || 'audio/pcm;rate=24000';
            this.logger.log(
              `[${sessionId}] 🎵 Audio response received - MIME: ${mimeType}, Size: ${part.inlineData.data.length} chars`,
            );
            client.emit('live_audio_response', {
              audioData: part.inlineData.data, // Base64 encoded audio
              mimeType: mimeType,
              timestamp: Date.now(),
            });
          }

          // Handle text parts
          if (part.text) {
            this.logger.log(`[${sessionId}] 💬 Text response: ${part.text.substring(0, 100)}...`);
            client.emit('live_text_response', {
              text: part.text,
              timestamp: Date.now(),
            });

            // Add to conversation history
            session.conversationHistory.push(`Assistant: ${part.text}`);
            this.sessions.set(sessionId, session);
          }
        }
      }

      if (response.serverContent?.turnComplete) {
        // Turn is complete
        client.emit('live_turn_complete', {
          message: 'Turn completed',
          timestamp: Date.now(),
        });
        this.logger.log(`[${sessionId}] ✅ Live turn completed`);
      }
    } catch (error) {
      this.logger.error(`[${sessionId}] Error processing Live response:`, error);
    }
  }
}
