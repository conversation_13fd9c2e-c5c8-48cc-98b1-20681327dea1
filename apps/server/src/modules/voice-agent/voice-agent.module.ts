import { LlmModule } from '@/llm/llm.module';
import { Module } from '@nestjs/common';
import { GeminiLiveService } from './gemini-live.service';
import { VoiceAgentController } from './voice-agent.controller';
import { VoiceAgentGateway } from './voice-agent.gateway';
import { VoiceAgentService } from './voice-agent.service';

@Module({
  imports: [LlmModule],
  controllers: [VoiceAgentController],
  providers: [VoiceAgentService, GeminiLiveService, VoiceAgentGateway],
  exports: [VoiceAgentService, GeminiLiveService],
})
export class VoiceAgentModule {}
