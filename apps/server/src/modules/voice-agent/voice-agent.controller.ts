import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { VoiceAgentService } from './voice-agent.service';

@ApiTags('voice-agent')
@Controller('voice-agent')
export class VoiceAgentController {
  constructor(private readonly voiceAgentService: VoiceAgentService) {}

  @Get('health')
  @ApiOperation({ summary: 'Health check for voice agent service' })
  healthCheck() {
    return { status: 'ok', service: 'voice-agent' };
  }

  @Post('transcribe')
  @ApiOperation({ summary: 'Transcribe audio to text' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('audio'))
  async transcribeAudio(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new HttpException('No audio file provided', HttpStatus.BAD_REQUEST);
    }

    try {
      const response = await this.voiceAgentService.processAudioWithGemini(
        file.buffer,
        file.mimetype,
      );

      return {
        response,
        success: true,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to transcribe audio',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('synthesize')
  @ApiOperation({ summary: 'Convert text to speech' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        text: { type: 'string', description: 'Text to convert to speech' },
      },
      required: ['text'],
    },
  })
  async synthesizeSpeech(@Body() body: { text: string }, @Res() res: Response) {
    if (!body.text) {
      throw new HttpException('No text provided', HttpStatus.BAD_REQUEST);
    }

    try {
      const audioBuffer = await this.voiceAgentService.textToSpeech(body.text);

      res.set({
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.length.toString(),
        'Content-Disposition': 'inline; filename="speech.mp3"',
      });

      res.send(audioBuffer);
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to generate speech',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('process')
  @ApiOperation({ summary: 'Complete voice processing pipeline' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('audio'))
  async processVoiceMessage(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { context?: string },
  ) {
    if (!file) {
      throw new HttpException('No audio file provided', HttpStatus.BAD_REQUEST);
    }

    try {
      const context = body.context ? JSON.parse(body.context) : undefined;

      const result = await this.voiceAgentService.processVoiceMessage(
        file.buffer,
        file.mimetype,
        context,
      );

      return {
        response: result.response,
        audioUrl: `data:${result.mimeType};base64,${result.audioResponse.toString('base64')}`,
        success: true,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to process voice message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
