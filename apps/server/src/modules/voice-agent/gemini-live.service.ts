import { GoogleGenAI } from '@google/genai';
import { Injectable, Logger } from '@nestjs/common';

interface LiveSession {
  session: any;
  isActive: boolean;
  responseQueue: any[];
  sessionId: string;
}

@Injectable()
export class GeminiLiveService {
  private readonly logger = new Logger(GeminiLiveService.name);
  private ai: GoogleGenAI | null = null;
  private activeSessions = new Map<string, LiveSession>();

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      this.logger.warn('Gemini API key not configured. Live API functionality will be disabled.');
      return;
    }

    this.ai = new GoogleGenAI({ apiKey });
    this.logger.log('Gemini Live API service initialized');
  }

  /**
   * Start a new Live API session with native audio capabilities
   */
  async startLiveSession(
    sessionId: string,
    options?: {
      model?: string;
      systemInstruction?: string;
      responseModalities?: string[];
    },
  ): Promise<void> {
    if (!this.ai) {
      throw new Error('Gemini API not configured');
    }

    if (this.activeSessions.has(sessionId)) {
      throw new Error('Session already active');
    }

    const startTime = Date.now();
    this.logger.log(
      `[${sessionId}] 🚀 Starting Gemini Live API session at ${new Date().toISOString()}`,
    );

    try {
      // Use native audio model for best performance and VAD
      const model = options?.model || 'gemini-2.5-flash-preview-native-audio-dialog';

      const config = {
        responseModalities: options?.responseModalities || ['AUDIO', 'TEXT'],
        systemInstruction:
          options?.systemInstruction ||
          'You are a helpful AI voice assistant. Always respond with both audio and text. Respond naturally and conversationally in a clear, friendly voice. Keep responses concise but engaging.',
      } as any;

      this.logger.log(`[${sessionId}] 🤖 Using model: ${model}`);
      this.logger.log(`[${sessionId}] 📊 Config: ${JSON.stringify(config)}`);

      const responseQueue: any[] = [];

      const session = await this.ai.live.connect({
        model: model,
        callbacks: {
          onopen: () => {
            const initTime = Date.now() - startTime;
            this.logger.log(`[${sessionId}] ✅ Live session connected in ${initTime}ms`);
          },
          onmessage: (message) => {
            this.logger.log(
              `[${sessionId}] 📥 Message received: ${JSON.stringify(message).substring(0, 500)}...`,
            );

            // Log specific parts for debugging
            if (message.serverContent?.modelTurn?.parts) {
              for (const part of message.serverContent.modelTurn.parts) {
                if (part.inlineData?.data) {
                  this.logger.log(
                    `[${sessionId}] 🎵 Audio part detected: ${part.inlineData.data.length} chars, MIME: ${part.inlineData.mimeType}`,
                  );
                }
                if (part.text) {
                  this.logger.log(
                    `[${sessionId}] 💬 Text part detected: ${part.text.substring(0, 100)}...`,
                  );
                }
              }
            }

            responseQueue.push(message);
          },
          onerror: (error) => {
            this.logger.error(`[${sessionId}] ❌ Live session error:`, error);
          },
          onclose: (event) => {
            this.logger.log(`[${sessionId}] 🔒 Live session closed: ${event.reason}`);
            this.activeSessions.delete(sessionId);
          },
        },
        config: config,
      });

      // Store active session
      this.activeSessions.set(sessionId, {
        session,
        isActive: true,
        responseQueue,
        sessionId,
      });

      this.logger.log(`[${sessionId}] 🎙️ Live session ready for real-time audio`);
    } catch (error) {
      this.logger.error(`[${sessionId}] Failed to start Live session:`, error);
      throw error;
    }
  }

  /**
   * Send audio chunk to Live API session
   */
  async sendAudioChunk(
    sessionId: string,
    audioData: Buffer,
    mimeType: string = 'audio/pcm;rate=24000',
  ): Promise<void> {
    const liveSession = this.activeSessions.get(sessionId);
    if (!liveSession || !liveSession.isActive) {
      throw new Error('No active Live session found');
    }

    this.logger.log(
      `[${sessionId}] 📤 Sending audio chunk: ${audioData.length} bytes, ${mimeType}`,
    );

    try {
      // Convert buffer to base64 for the Live API
      const base64Audio = audioData.toString('base64');

      await liveSession.session.sendRealtimeInput({
        audio: {
          data: base64Audio,
          mimeType: mimeType,
        },
      });

      this.logger.log(`[${sessionId}] ✅ Audio chunk sent successfully`);
    } catch (error) {
      this.logger.error(`[${sessionId}] Error sending audio chunk:`, error);
      throw error;
    }
  }

  /**
   * Get responses from the Live API session
   */
  async getResponses(sessionId: string, timeout: number = 5000): Promise<any[]> {
    const liveSession = this.activeSessions.get(sessionId);
    if (!liveSession) {
      throw new Error('No active Live session found');
    }

    const startTime = Date.now();
    this.logger.log(`[${sessionId}] 📥 Waiting for responses (timeout: ${timeout}ms)`);

    return new Promise((resolve) => {
      const checkForResponses = () => {
        if (liveSession.responseQueue.length > 0) {
          const responses = [...liveSession.responseQueue];
          liveSession.responseQueue.length = 0; // Clear the queue

          const responseTime = Date.now() - startTime;
          this.logger.log(
            `[${sessionId}] ✅ Got ${responses.length} responses in ${responseTime}ms`,
          );

          resolve(responses);
        } else if (Date.now() - startTime > timeout) {
          this.logger.log(`[${sessionId}] ⏰ Response timeout after ${timeout}ms`);
          resolve([]); // Return empty array on timeout
        } else {
          setTimeout(checkForResponses, 100); // Check every 100ms
        }
      };

      checkForResponses();
    });
  }

  /**
   * Stream responses from Live API session
   */
  async *streamResponses(sessionId: string): AsyncGenerator<any, void, unknown> {
    const liveSession = this.activeSessions.get(sessionId);
    if (!liveSession) {
      throw new Error('No active Live session found');
    }

    this.logger.log(`[${sessionId}] 🌊 Starting response stream`);

    let lastCheckTime = Date.now();

    while (liveSession.isActive) {
      if (liveSession.responseQueue.length > 0) {
        const response = liveSession.responseQueue.shift();
        const responseTime = Date.now();

        this.logger.log(
          `[${sessionId}] 📤 Streaming response: ${JSON.stringify(response).substring(0, 100)}...`,
        );

        yield response;
        lastCheckTime = responseTime;
      } else {
        // Check if session is still active and hasn't been idle too long
        if (Date.now() - lastCheckTime > 30000) {
          // 30 seconds idle timeout
          this.logger.log(`[${sessionId}] ⏰ Stream timeout - no activity for 30s`);
          break;
        }

        // Wait a bit before checking again
        await new Promise((resolve) => setTimeout(resolve, 50));
      }
    }

    this.logger.log(`[${sessionId}] 🔚 Response stream ended`);
  }

  /**
   * Stop and cleanup Live API session
   */
  async stopLiveSession(sessionId: string): Promise<void> {
    const liveSession = this.activeSessions.get(sessionId);
    if (!liveSession) {
      this.logger.warn(`[${sessionId}] No active session to stop`);
      return;
    }

    this.logger.log(`[${sessionId}] 🛑 Stopping Live API session`);

    try {
      liveSession.isActive = false;
      await liveSession.session.close();
      this.activeSessions.delete(sessionId);

      this.logger.log(`[${sessionId}] ✅ Live session stopped and cleaned up`);
    } catch (error) {
      this.logger.error(`[${sessionId}] Error stopping Live session:`, error);
      this.activeSessions.delete(sessionId); // Force cleanup
    }
  }

  /**
   * Get session status
   */
  getSessionStatus(sessionId: string): { isActive: boolean; queueLength: number } | null {
    const liveSession = this.activeSessions.get(sessionId);
    if (!liveSession) {
      return null;
    }

    return {
      isActive: liveSession.isActive,
      queueLength: liveSession.responseQueue.length,
    };
  }

  /**
   * Get all active sessions (for monitoring)
   */
  getActiveSessions(): string[] {
    return Array.from(this.activeSessions.keys());
  }
}
