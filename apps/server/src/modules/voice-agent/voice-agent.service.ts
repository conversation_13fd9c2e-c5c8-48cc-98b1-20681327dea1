import { AudioChunkInfo, AudioUtils } from '@/common/utils/audio.utils';
import { AiSdk } from '@/llm/sdk/AiSdk';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class VoiceAgentService {
  private readonly logger = new Logger(VoiceAgentService.name);
  private openaiApiKey: string;

  constructor(private readonly aiSdk: AiSdk) {
    this.openaiApiKey = process.env.OPENAI_API_KEY || '';

    if (!this.openaiApiKey) {
      this.logger.warn('OpenAI API key not configured. TTS functionality will be disabled.');
    }
  }

  /**
   * Process audio directly with <PERSON> (audio input -> text output)
   * This eliminates the need for separate speech-to-text processing
   * Enhanced with audio optimization
   */
  async processAudioWithGemini(
    audioBuffer: Buffer,
    mimeType: string = 'audio/webm',
    conversationContext?: string[],
  ): Promise<string> {
    try {
      const geminiStartTime = Date.now();
      this.logger.log(`🤖 Processing audio with Gemini at ${new Date().toISOString()}`);
      this.logger.log(`📊 Audio buffer size: ${audioBuffer.length} bytes, MIME: ${mimeType}`);

      // Optimize audio chunk for better processing
      const audioChunkInfo = AudioUtils.standardizeAudioChunk(audioBuffer, mimeType);
      const optimizedChunk = AudioUtils.optimizeAudioChunk(audioChunkInfo);

      // Use optimized buffer if different from original
      const processedBuffer = optimizedChunk.buffer.equals(audioBuffer)
        ? audioBuffer
        : optimizedChunk.buffer;

      const contextPrompt =
        conversationContext && conversationContext.length > 0
          ? `Previous conversation context:\n${conversationContext.join('\n')}\n\n`
          : '';

      // Use the proper messages format with file content for audio input
      const result = await this.aiSdk.generateTextWithMessages({
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `${contextPrompt} You are a helpful AI voice assistant. Please provide a natural, conversational response suitable for speech.`,
              },
              {
                type: 'file',
                data: processedBuffer,
                mimeType: mimeType,
              },
            ],
          },
        ],
        provider: 'google',
        model: 'gemini-2.0-flash', // This model supports audio input
        identifierName: 'voice-agent',
        identifierValue: 'audio-processing',
        providerOptions: {
          google: {
            responseModalities: ['TEXT'], // Gemini will only send back text
          },
        },
      });

      // Since we're not using structured format anymore, the whole response is the AI's answer
      const responseText = result.text;

      const geminiEndTime = Date.now();
      const processingTime = geminiEndTime - geminiStartTime;

      this.logger.log(`✅ Gemini processing completed in: ${processingTime}ms`);
      this.logger.log(`📊 Response length: ${responseText.length} characters`);
      this.logger.log(
        `📝 Response preview: "${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}"`,
      );

      return responseText;
    } catch (error) {
      this.logger.error('Error processing audio with Gemini:', error);
      throw new Error('Failed to process audio');
    }
  }

  /**
   * Convert text to speech using OpenAI TTS
   */
  async textToSpeech(
    text: string,
    options?: {
      voice?: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';
      model?: 'tts-1' | 'tts-1-hd';
      speed?: number;
    },
  ): Promise<Buffer> {
    if (!this.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const ttsStartTime = Date.now();
      this.logger.log(`🎵 Starting OpenAI TTS at ${new Date().toISOString()}`);
      this.logger.log(`📊 Text length: ${text.length} characters`);
      this.logger.log(
        `🎤 Voice: ${options?.voice || 'alloy'}, Model: ${options?.model || 'tts-1'}`,
      );

      const response = await axios.post(
        'https://api.openai.com/v1/audio/speech',
        {
          model: options?.model || 'tts-1', // or 'tts-1-hd' for higher quality
          input: text,
          voice: options?.voice || 'alloy', // Available voices: alloy, echo, fable, onyx, nova, shimmer
          response_format: 'mp3',
          speed: options?.speed || 1.0, // 0.25 to 4.0
        },
        {
          headers: {
            Authorization: `Bearer ${this.openaiApiKey}`,
            'Content-Type': 'application/json',
          },
          responseType: 'arraybuffer',
        },
      );

      const ttsEndTime = Date.now();
      const ttsTime = ttsEndTime - ttsStartTime;
      const audioBuffer = Buffer.from(response.data);

      this.logger.log(`✅ OpenAI TTS completed in: ${ttsTime}ms`);
      this.logger.log(`📊 Audio buffer size: ${audioBuffer.length} bytes`);

      return audioBuffer;
    } catch (error: any) {
      this.logger.error('Error converting text to speech:', error);
      if (error.response?.status === 401) {
        throw new Error('Invalid OpenAI API key');
      } else if (error.response?.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (error.response?.data) {
        throw new Error(
          `OpenAI API error: ${error.response.data.error?.message || 'Unknown error'}`,
        );
      }
      throw new Error('Failed to generate speech');
    }
  }

  /**
   * Complete voice agent pipeline: audio -> Gemini (with audio input) -> speech
   * Streamlined approach using Gemini's native audio processing
   */
  async processVoiceMessage(
    audioBuffer: Buffer,
    mimeType: string,
    conversationContext?: string[],
  ): Promise<{
    response: string;
    audioResponse: Buffer;
    mimeType: string;
  }> {
    try {
      // Step 1: Process audio directly with Gemini (transcription + response)
      const response = await this.processAudioWithGemini(
        audioBuffer,
        mimeType,
        conversationContext,
      );

      // Step 2: Convert response to speech
      const audioResponse = await this.textToSpeech(response);

      return {
        response,
        audioResponse,
        mimeType: 'audio/mpeg',
      };
    } catch (error) {
      this.logger.error('Error in voice agent pipeline:', error);
      throw error;
    }
  }

  /**
   * Stream processing for real-time conversation using Gemini's audio input
   * This streams the text response as it's generated, then creates audio from the complete response
   */
  async *streamVoiceProcessing(
    audioBuffer: Buffer,
    mimeType: string,
    conversationContext?: string[],
    timingContext?: { sessionId: string; startTime: number },
  ): AsyncGenerator<
    { type: 'response_chunk' | 'response_complete' | 'audio'; data: any },
    void,
    unknown
  > {
    try {
      const sessionId = timingContext?.sessionId || 'unknown';
      const startTime = timingContext?.startTime || Date.now();

      const contextPrompt =
        conversationContext && conversationContext.length > 0
          ? `Previous conversation context:\n${conversationContext.join('\n')}\n\n`
          : '';

      // 🔥 TIMING LOG: About to call Gemini
      const geminiCallStart = Date.now();
      const totalElapsed = geminiCallStart - startTime;
      this.logger.log(`[${sessionId}] ⏱️ Time to Gemini call: ${totalElapsed}ms`);
      this.logger.log(`[${sessionId}] 🤖 Calling Gemini 2.0 Flash at ${new Date().toISOString()}`);

      // Stream the response from Gemini in real-time
      const stream = await this.aiSdk.streamTextWithMessages({
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `${contextPrompt} You are a helpful AI voice assistant. Please provide a natural, conversational response suitable for speech.`,
              },
              {
                type: 'file',
                data: audioBuffer,
                mimeType: mimeType,
              },
            ],
          },
        ],
        provider: 'google',
        model: 'gemini-2.0-flash',
        identifierName: 'voice-agent',
        identifierValue: 'audio-processing',
        providerOptions: {
          google: {
            responseModalities: ['TEXT'],
          },
        },
      });

      // 🔥 TIMING LOG: Stream obtained from Gemini
      const streamObtainedTime = Date.now();
      const geminiInitTime = streamObtainedTime - geminiCallStart;
      this.logger.log(`[${sessionId}] 🚀 Gemini stream initialized in: ${geminiInitTime}ms`);

      let completeResponse = '';
      let firstChunkTime: number | null = null;
      let chunkCount = 0;

      // Stream text chunks as they arrive
      for await (const chunk of stream.textStream) {
        if (!firstChunkTime) {
          firstChunkTime = Date.now();
          const timeToFirstChunk = firstChunkTime - geminiCallStart;
          this.logger.log(`[${sessionId}] 🎯 First chunk received in: ${timeToFirstChunk}ms`);
          this.logger.log(
            `[${sessionId}] 🎯 Total time to first response: ${firstChunkTime - startTime}ms`,
          );
        }

        chunkCount++;
        completeResponse += chunk;
        this.logger.log(
          `[${sessionId}] 📝 Chunk ${chunkCount}: "${chunk.substring(0, 50)}${chunk.length > 50 ? '...' : ''}"`,
        );
        yield { type: 'response_chunk', data: chunk };
      }

      // 🔥 TIMING LOG: Complete response received
      const responseCompleteTime = Date.now();
      const totalGeminiTime = responseCompleteTime - geminiCallStart;
      const totalResponseTime = responseCompleteTime - startTime;

      this.logger.log(
        `[${sessionId}] ✅ Complete response received in: ${totalGeminiTime}ms (Gemini processing)`,
      );
      this.logger.log(`[${sessionId}] ✅ Total response time: ${totalResponseTime}ms (end-to-end)`);
      this.logger.log(
        `[${sessionId}] 📊 Response stats: ${chunkCount} chunks, ${completeResponse.length} chars`,
      );

      // Yield complete response
      yield { type: 'response_complete', data: completeResponse };

      // 🔥 TIMING LOG: Starting TTS
      const ttsStartTime = Date.now();
      this.logger.log(`[${sessionId}] 🎵 Starting TTS at ${new Date().toISOString()}`);

      // Generate audio from complete response (TTS needs complete text)
      const audioResponse = await this.textToSpeech(completeResponse);

      // 🔥 TIMING LOG: TTS completed
      const ttsCompleteTime = Date.now();
      const ttsTime = ttsCompleteTime - ttsStartTime;
      const totalPipelineTime = ttsCompleteTime - startTime;

      this.logger.log(`[${sessionId}] 🎵 TTS completed in: ${ttsTime}ms`);
      this.logger.log(`[${sessionId}] 🏁 Total pipeline time: ${totalPipelineTime}ms`);
      this.logger.log(`[${sessionId}] 📊 Audio response size: ${audioResponse.length} bytes`);

      yield { type: 'audio', data: audioResponse };
    } catch (error) {
      this.logger.error('Error in streaming voice processing:', error);
      throw error;
    }
  }

  /**
   * Process audio chunks in real-time with reduced latency
   * Optimized for streaming audio processing with proper WAV merging
   */
  async processAudioChunks(
    audioChunks: Buffer[],
    mimeType: string,
    sessionId: string,
    isPartial: boolean = true,
  ): Promise<{ response: string; processingTime: number }> {
    const startTime = Date.now();

    this.logger.log(
      `[${sessionId}] 🎯 Processing ${audioChunks.length} chunks - ${isPartial ? 'PARTIAL' : 'FINAL'}`,
    );

    try {
      // Convert buffers to AudioChunkInfo for proper processing
      const audioChunkInfos: AudioChunkInfo[] = audioChunks.map((buffer) =>
        AudioUtils.standardizeAudioChunk(buffer, mimeType),
      );

      // Use proper audio merging instead of simple Buffer.concat
      const combinedAudio = AudioUtils.mergeAudioChunks(audioChunkInfos);

      this.logger.log(
        `[${sessionId}] 🔧 Merged ${audioChunks.length} chunks into ${combinedAudio.length} bytes using AudioUtils`,
      );

      const response = await this.processAudioWithGemini(combinedAudio, mimeType);
      const processingTime = Date.now() - startTime;

      this.logger.log(
        `[${sessionId}] ✅ ${isPartial ? 'Partial' : 'Final'} processing completed in: ${processingTime}ms`,
      );

      return { response, processingTime };
    } catch (error) {
      this.logger.error(`[${sessionId}] Error processing audio chunks:`, error);
      throw error;
    }
  }
}
