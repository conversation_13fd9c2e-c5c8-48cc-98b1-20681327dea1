import { Modu<PERSON> } from '@nestjs/common';

import { CommonServicesModule } from '@/common/services/common-services.module';
import { ImgModule } from '@/img';
import { VectorStoreModule } from '@/modules/vector-store';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { KnowledgeBaseService } from './knowledge-base.service';
import { TextExtractorService } from './text-extractor.service';

@Module({
  imports: [VectorStoreModule, CommonServicesModule, ImgModule],
  controllers: [KnowledgeBaseController],
  providers: [KnowledgeBaseService, TextExtractorService],
  exports: [KnowledgeBaseService],
})
export class KnowledgeBaseModule {}
