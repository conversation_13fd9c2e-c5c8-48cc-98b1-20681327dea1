import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

import config from '@/common/configs/config';

@Injectable()
export class TextExtractorService {
  private readonly logger = new Logger(TextExtractorService.name);
  private readonly blogifyApiKey: string;

  constructor() {
    this.blogifyApiKey = config().blogifyApi?.apiKey || '';
  }

  async extractTextFromUrl(url: string): Promise<string> {
    this.logger.log(`Extracting text from URL: ${url}`);

    try {
      // Determine the appropriate endpoint based on URL
      const endpoint = this.getUrlExtractorEndpoint(url);

      // Use the Blogify text extractor for URL content
      const extractedText = await this.callBlogifyTextExtractor({
        url,
        endpoint,
      });

      return extractedText;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`URL text extraction failed for ${url}: ${errorMessage}`);
      throw new Error(`URL text extraction failed for ${url}: ${errorMessage}`);
    }
  }

  async extractTextFromFile(fileUrl: string, fileType: string): Promise<string> {
    this.logger.log(`Extracting text from file: ${fileUrl} (${fileType})`);

    try {
      const endpoint = this.getFileExtractorEndpoint(fileType);

      const extractedText = await this.callBlogifyTextExtractor({
        url: fileUrl,
        endpoint,
      });

      return extractedText;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`File text extraction failed for ${fileUrl}: ${errorMessage}`);
      throw new Error(`File text extraction failed for ${fileUrl}: ${errorMessage}`);
    }
  }

  private async callBlogifyTextExtractor(request: {
    url: string;
    endpoint: string;
  }): Promise<string> {
    try {
      this.logger.log(`Calling Blogify text extractor for: ${request.url}`);

      const payload = {
        url: request.url,
      };

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (this.blogifyApiKey) {
        headers['x-api-key'] = this.blogifyApiKey;
      }

      const response = await axios.post(request.endpoint, payload, {
        headers,
        timeout: 6000000, // 5 minutes timeout for large files
      });

      if (!response.data.text) {
        throw new Error('No text content extracted from file');
      }

      this.logger.log(`Text extraction completed for: ${request.url}`);
      return response.data.text;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Blogify text extraction failed for ${request.url}: ${errorMessage}`);
      throw new Error(`Blogify text extraction failed for ${request.url}: ${errorMessage}`);
    }
  }

  private getUrlExtractorEndpoint(url: string): string {
    const baseUrl = config().blogifyApi?.textExtraction?.url || '/text-extractor';
    const urlLower = url.toLowerCase();

    // Media URLs (YouTube, Vimeo, Spotify, etc.)
    if (
      urlLower.includes('youtube.com') ||
      urlLower.includes('youtu.be') ||
      urlLower.includes('vimeo.com') ||
      urlLower.includes('spotify.com') ||
      urlLower.includes('soundcloud.com') ||
      urlLower.includes('twitch.tv') ||
      urlLower.includes('tiktok.com') ||
      urlLower.includes('instagram.com/reel') ||
      urlLower.includes('facebook.com/watch') ||
      urlLower.includes('dailymotion.com') ||
      urlLower.includes('bilibili.com') ||
      urlLower.includes('reddit.com/r/') ||
      urlLower.includes('twitter.com/') ||
      urlLower.includes('x.com/')
    ) {
      return `${baseUrl}/media`;
    }

    // Document URLs (Google Docs, PDFs, etc.)
    if (
      urlLower.includes('docs.google.com') ||
      urlLower.includes('drive.google.com') ||
      urlLower.endsWith('.pdf') ||
      urlLower.endsWith('.txt') ||
      urlLower.endsWith('.doc') ||
      urlLower.endsWith('.docx') ||
      urlLower.includes('dropbox.com') ||
      urlLower.includes('onedrive.live.com') ||
      urlLower.includes('box.com')
    ) {
      return `${baseUrl}/doc`;
    }

    // Default to HTML scraping for web pages
    return `${baseUrl}/html`;
  }

  private getFileExtractorEndpoint(fileType: string): string {
    const baseUrl = config().blogifyApi?.textExtraction?.url || '/text-extractor';
    const fileTypeLower = fileType.toLowerCase();

    // Media files (video, audio)
    if (fileTypeLower.includes('video') || fileTypeLower.includes('audio')) {
      return `${baseUrl}/media`;
    }

    // Documents (PDF, DOC, DOCX, TXT)
    if (
      fileTypeLower.includes('pdf') ||
      fileTypeLower.includes('msword') ||
      fileTypeLower.includes('document') ||
      fileTypeLower.includes('text/plain')
    ) {
      return `${baseUrl}/doc`;
    }

    // Default to HTML for web scraping
    return `${baseUrl}/html`;
  }
}
