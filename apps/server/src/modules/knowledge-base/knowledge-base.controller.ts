import { Body, Controller, Delete, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import 'multer';

import { Auth } from '@/auth/guards/auth.guard';
import { ReqUser } from '@/common/decorators/req-user.decorator';
import { Team } from '@/resources/team/team.model';
import { User } from '@/resources/user/user.model';
import {
  CreateKnowledgeBaseDto,
  DeleteKnowledgeBaseResponseDto,
  KnowledgeBaseResponseDto,
  KnowledgeBaseStatsResponseDto,
  ListKnowledgeBaseQueryDto,
  ListKnowledgeBaseResponseDto,
} from './dto/knowledge-base.dto';
import { KnowledgeBaseService } from './knowledge-base.service';

@ApiTags('Knowledge Base')
@Controller('knowledge-base')
@UseGuards(Auth)
@ApiBearerAuth()
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new knowledge base entry (JSON)',
    description: `Add different types of data to the knowledge base using JSON format.

Examples:
- Text: { "type": "text", "text": "Your content here" }
- URL: { "type": "url", "url": "https://example.com" }
- File: { "type": "upload", "fileUrl": "https://example.com/files/document.pdf" }

For file uploads, you can either:
1. Use this endpoint with a pre-uploaded file URL
2. Use the /multipart endpoint below for direct file upload

Data is stored in the team's knowledge base namespace for isolation.`,
  })
  @ApiResponse({
    status: 201,
    description: 'Knowledge base entry created successfully',
    type: KnowledgeBaseResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async createKnowledgeBase(
    @Body() dto: CreateKnowledgeBaseDto,
    @ReqUser() user: User,
  ): Promise<KnowledgeBaseResponseDto> {
    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('User must belong to a team to create knowledge base entries');
    }
    return this.knowledgeBaseService.createKnowledgeBaseWithNamespace(dto, teamId);
  }

  @Get()
  @ApiOperation({
    summary: 'List knowledge base entries',
    description:
      "Retrieve a list of knowledge base entries from the team's knowledge base with optional filtering and search capabilities",
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search query for full-text search' })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['text', 'url', 'upload'],
    description: 'Filter by knowledge base entry type',
  })
  @ApiQuery({ name: 'tags', required: false, type: [String], description: 'Filter by tags' })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of results to return (1-100)',
    minimum: 1,
    maximum: 100,
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of results to skip',
    minimum: 0,
  })
  @ApiResponse({
    status: 200,
    description: 'List of knowledge base entries retrieved successfully',
    type: ListKnowledgeBaseResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid query parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async listKnowledgeBase(
    @Query() query: ListKnowledgeBaseQueryDto,
    @ReqUser() user: User,
  ): Promise<ListKnowledgeBaseResponseDto> {
    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('User must belong to a team to list knowledge base entries');
    }
    return this.knowledgeBaseService.listKnowledgeBase(query, teamId);
  }

  @Get('stats')
  @ApiOperation({
    summary: 'Get knowledge base statistics',
    description: 'Retrieve statistics about knowledge base entries grouped by type for the team',
  })
  @ApiResponse({
    status: 200,
    description: 'Knowledge base statistics retrieved successfully',
    type: KnowledgeBaseStatsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getKnowledgeBaseStats(@ReqUser() user: User): Promise<KnowledgeBaseStatsResponseDto> {
    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('User must belong to a team to get knowledge base statistics');
    }
    return this.knowledgeBaseService.getKnowledgeBaseStats(teamId);
  }

  @Delete(':knowledgeBaseId')
  @ApiOperation({
    summary: 'Delete a knowledge base entry',
    description:
      "Delete a knowledge base entry and all its associated documents from the team's vector store namespace",
  })
  @ApiParam({
    name: 'knowledgeBaseId',
    description: 'The ID of the knowledge base entry to delete',
  })
  @ApiResponse({
    status: 200,
    description: 'Knowledge base entry deleted successfully',
    type: DeleteKnowledgeBaseResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Knowledge base entry not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteKnowledgeBase(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @ReqUser() user: User,
  ): Promise<DeleteKnowledgeBaseResponseDto> {
    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('User must belong to a team to delete knowledge base entries');
    }
    return this.knowledgeBaseService.deleteKnowledgeBase(knowledgeBaseId, teamId);
  }

  @Delete()
  @ApiOperation({
    summary: 'Delete all knowledge base entries',
    description: 'Delete all knowledge base entries from the team',
  })
  @ApiResponse({
    status: 200,
    description: 'All knowledge base entries deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteAllKnowledgeBase(@ReqUser() user: User): Promise<number> {
    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('User must belong to a team to delete knowledge base entries');
    }
    return this.knowledgeBaseService.deleteNamespace(teamId);
  }

  @Get('debug/:teamId')
  @ApiOperation({
    summary: 'Debug knowledge base documents (temporary)',
    description: 'Debug endpoint to check what documents exist in the vector store',
  })
  async debugKnowledgeBase(@Param('teamId') teamId: string): Promise<any> {
    return this.knowledgeBaseService.debugDocuments(teamId);
  }
}
