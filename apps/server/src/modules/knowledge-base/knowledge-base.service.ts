import { Injectable, Logger } from '@nestjs/common';
import 'multer';

import { S3Service } from '@/common/services/s3.service';
import { UrlService } from '@/common/services/url.service';
import { generateKnowledgeBaseId } from '@/common/utils/id.utils';
import { AiSdk } from '@/llm/sdk/AiSdk';
import {
  VectorDocument,
  VectorSearchResult,
  VectorSearchService,
  VectorStoreService,
} from '@/modules/vector-store';
import { VisionService } from '../../img';
import {
  CreateKnowledgeBaseDto,
  DeleteKnowledgeBaseResponseDto,
  KnowledgeBaseItemDto,
  KnowledgeBaseResponseDto,
  KnowledgeBaseStatsItemDto,
  KnowledgeBaseStatsResponseDto,
  KnowledgeBaseType,
  ListKnowledgeBaseQueryDto,
  ListKnowledgeBaseResponseDto,
} from './dto/knowledge-base.dto';
import { TextExtractorService } from './text-extractor.service';

@Injectable()
export class KnowledgeBaseService {
  private readonly logger = new Logger(KnowledgeBaseService.name);
  private readonly aiSdk: AiSdk;

  constructor(
    private readonly s3Service: S3Service,
    private readonly urlService: UrlService,
    private readonly textExtractorService: TextExtractorService,
    private readonly vectorStoreService: VectorStoreService,
    private readonly vectorSearchService: VectorSearchService,
    private readonly visionService: VisionService,
  ) {
    this.aiSdk = new AiSdk({
      provider: 'openai',
      model: 'gpt-4o-mini',
      identifierName: 'knowledge-base-service',
      identifierValue: 'default',
    });
  }

  async createKnowledgeBase(dto: CreateKnowledgeBaseDto): Promise<KnowledgeBaseResponseDto> {
    const knowledgeBaseId = this.generateId();

    try {
      this.logger.log(
        `Starting knowledge base creation for type: ${dto.type}, id: ${knowledgeBaseId}`,
      );

      // Extract text based on type
      let extractedText = '';
      let derivedName = '';
      const finalType = dto.type;
      const metadata: VectorDocument['metadata'] = {
        sourceType: finalType,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      switch (dto.type) {
        case KnowledgeBaseType.TEXT:
          extractedText = dto.text || '';
          derivedName = dto.fileName || 'Text Entry';
          metadata.title = derivedName;
          metadata.description = dto.description;
          metadata.tags = dto.tags;
          break;
        case KnowledgeBaseType.URL:
          extractedText = await this.extractTextFromUrl(dto.url || '');
          if (dto.fileName) {
            derivedName = dto.fileName;
          } else {
            // Get URL metadata to derive name from title
            try {
              const urlMeta = await this.urlService.getUrlMeta(dto.url || '');
              derivedName = urlMeta.title || new URL(dto.url || '').hostname || 'URL Entry';
            } catch {
              derivedName = new URL(dto.url || '').hostname || 'URL Entry';
            }
          }
          metadata.title = derivedName;
          metadata.description = dto.description;
          metadata.tags = dto.tags;
          metadata.url = dto.url;
          break;
        case KnowledgeBaseType.UPLOAD:
        case KnowledgeBaseType.VIDEO:
        case KnowledgeBaseType.AUDIO:
        case KnowledgeBaseType.IMAGE:
        case KnowledgeBaseType.PDF:
        case KnowledgeBaseType.DOC:
          const result = await this.processFileFromUrl(dto.fileUrl, knowledgeBaseId);
          extractedText = result.text;
          derivedName = dto.fileName || result.title || 'Uploaded File';

          metadata.title = derivedName;
          metadata.description = dto.description;
          metadata.tags = dto.tags;
          metadata.url = result.s3Url; // S3 storage URL
          metadata.fileExtension = result.fileExtension;
          break;
        default:
          throw new Error(`Unsupported knowledge base type: ${dto.type}`);
      }

      if (!extractedText.trim()) {
        throw new Error('No text content extracted from the provided data');
      }

      // Create embeddings
      const embeddingResult = await this.aiSdk.embedText({
        text: extractedText,
        identifierName: 'knowledge-base-service',
        identifierValue: knowledgeBaseId,
      });

      // Store in vector store
      const vectorId = await this.storeInVectorStore(
        extractedText,
        embeddingResult.embedding,
        metadata,
        knowledgeBaseId,
        this.vectorStoreService.getNamespaceForTeam('default'), // Default namespace for backward compatibility
      );

      const response: KnowledgeBaseResponseDto = {
        id: knowledgeBaseId,
        type: finalType,
        name: derivedName,
        status: 'completed',
        vectorId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.logger.log(`Knowledge base created successfully for id: ${knowledgeBaseId}`);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Knowledge base creation failed for id: ${knowledgeBaseId}: ${errorMessage}`,
      );

      const response: KnowledgeBaseResponseDto = {
        id: knowledgeBaseId,
        type: dto.type,
        status: 'failed',
        message: errorMessage,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return response;
    }
  }

  private async processFileFromUrl(
    fileUrl: string | undefined,
    knowledgeBaseId: string,
    isImageType: boolean = false,
  ): Promise<{
    text: string;
    filename: string;
    fileExtension: string;
    s3Url: string;
    title?: string;
    description?: string;
  }> {
    if (!fileUrl) {
      throw new Error('No file URL provided');
    }

    // Extract file info from URL
    const { mimeType, filename, fileExtension } = this.extractFileInfoFromUrl(fileUrl);

    this.logger.log(
      `Processing file from URL: ${filename}, type: ${mimeType}, extension: ${fileExtension}`,
    );

    try {
      // Download the file from URL
      this.logger.log(`Downloading file from URL: ${fileUrl}`);
      const response = await fetch(fileUrl);

      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.status} ${response.statusText}`);
      }

      const buffer = Buffer.from(await response.arrayBuffer());
      const actualMimeType = response.headers.get('content-type') || mimeType;

      // Generate S3 key and upload file to S3
      const fileType = this.getFileTypeFromMimeType(actualMimeType);
      const s3Key = this.s3Service.generateKey(filename, knowledgeBaseId, fileType);

      const s3Url = await this.s3Service.uploadFile(s3Key, buffer, actualMimeType, {
        originalFilename: filename,
        originalUrl: fileUrl,
        knowledgeBaseId,
        knowledgeBaseType: fileType,
        uploadedAt: new Date().toISOString(),
      });

      this.logger.log(`File uploaded to S3: ${s3Url}`);

      // Check if it's an image file and use vision service for enhanced processing
      if (this.isImageFile(fileExtension) || isImageType) {
        try {
          this.logger.log(`Using vision service for image analysis: ${filename}`);

          // Use vision service to analyze the image using the original URL
          const visionResult = await this.visionService.analyzeImage({
            imageUrl: fileUrl,
            includeTextExtraction: true,
          });

          return {
            text: visionResult.description || 'Image content could not be analyzed',
            filename,
            fileExtension,
            s3Url,
            title: visionResult.title,
            description: visionResult.description,
          };
        } catch (error) {
          this.logger.error(`Vision analysis failed for image ${filename}:`, error);
          // Fall back to regular text extraction using S3 URL
        }
      }

      // For non-image files or if vision analysis fails, use regular text extraction
      const extractedText = await this.textExtractorService.extractTextFromFile(
        s3Url,
        actualMimeType,
      );

      return {
        text: extractedText,
        filename,
        fileExtension,
        s3Url,
      };
    } catch (error) {
      this.logger.error(`Failed to process file from URL ${fileUrl}:`, error);

      // Fallback: try to extract text directly from URL without S3 storage
      this.logger.warn(`Attempting fallback text extraction for ${fileUrl}`);

      let extractedText = '';

      if (this.isImageFile(fileExtension) || isImageType) {
        try {
          const visionResult = await this.visionService.analyzeImage({
            imageUrl: fileUrl,
            includeTextExtraction: true,
          });
          extractedText = visionResult.description || 'Image content could not be analyzed';
        } catch {
          extractedText = 'Failed to analyze image content';
        }
      } else {
        try {
          extractedText = await this.textExtractorService.extractTextFromUrl(fileUrl);
        } catch {
          extractedText = 'Failed to extract text content';
        }
      }

      // Return without S3 URL if upload failed
      return {
        text: extractedText,
        filename,
        fileExtension,
        s3Url: '', // Empty S3 URL indicates fallback was used
      };
    }
  }

  private extractFileInfoFromUrl(fileUrl: string): {
    mimeType: string;
    filename: string;
    fileExtension: string;
  } {
    try {
      // Parse the URL to extract filename and extension
      const url = new URL(fileUrl);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop() || 'unknown-file';

      // Extract file extension
      const fileExtension = this.getFileExtensionFromFilename(filename);

      // Determine MIME type from extension or default
      let mimeType = 'application/octet-stream';
      if (fileExtension) {
        // Use the extension to determine MIME type
        const extWithoutDot = fileExtension.replace(/^\./, '');
        mimeType = this.getMimeTypeFromExtension(extWithoutDot);
      }

      return {
        mimeType,
        filename,
        fileExtension: fileExtension.replace(/^\./, ''), // Remove leading dot if present
      };
    } catch (error) {
      // If URL parsing fails, try to extract from the path
      const filename = fileUrl.split('/').pop()?.split('?')[0] || 'unknown-file';
      const fileExtension = this.getFileExtensionFromFilename(filename);
      const extWithoutDot = fileExtension.replace(/^\./, '');
      const mimeType = this.getMimeTypeFromExtension(extWithoutDot);

      return {
        mimeType,
        filename,
        fileExtension: extWithoutDot,
      };
    }
  }

  private getMimeTypeFromExtension(extension: string): string {
    const ext = extension.toLowerCase();
    const mimeMap: Record<string, string> = {
      // Documents
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      // Images
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      // Videos
      mp4: 'video/mp4',
      avi: 'video/x-msvideo',
      // Audio
      mp3: 'audio/mpeg',
      wav: 'audio/wav',
    };

    return mimeMap[ext] || 'application/octet-stream';
  }

  private getFileExtensionFromFilename(filename: string): string {
    const match = filename.match(/\.[^/.]+$/);
    if (match) {
      return match[0];
    }

    // If no extension found, return empty string
    // The extension will be handled by the S3 service based on file type
    return '';
  }

  private async extractTextFromUrl(url: string): Promise<string> {
    return this.textExtractorService.extractTextFromUrl(url);
  }

  private async storeInVectorStore(
    text: string,
    embedding: number[],
    metadata: VectorDocument['metadata'],
    knowledgeBaseId: string,
    namespace: string,
  ): Promise<string> {
    try {
      const vectorDocument: VectorDocument = {
        id: this.generateId(),
        vector: embedding,
        text,
        metadata: {
          ...metadata,
          sourceType: metadata.sourceType,
          knowledgeBaseId,
          isChunk: false, // Mark as non-chunked document
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      };

      const vectorId = await this.vectorStoreService.storeDocument(vectorDocument, namespace);

      this.logger.log(`Vector stored in vector store with namespace: ${namespace}`);
      return vectorId;
    } catch (error) {
      this.logger.error(`Failed to store vector in vector store: ${error}`);
      throw new Error(`Failed to store vector: ${error}`);
    }
  }

  private generateId(): string {
    return generateKnowledgeBaseId();
  }

  /**
   * Chunk text into smaller pieces for better embedding and retrieval
   */
  private chunkText(text: string, chunkSize: number = 512, overlap: number = 50): string[] {
    if (!text || text.trim().length === 0) {
      return [];
    }

    // Simple word-based chunking (approximating tokens)
    // In a production system, you'd use a proper tokenizer
    const words = text.split(/\s+/);
    const chunks: string[] = [];

    if (words.length <= chunkSize) {
      // If text is smaller than chunk size, return as single chunk
      return [text];
    }

    let startIndex = 0;
    while (startIndex < words.length) {
      const endIndex = Math.min(startIndex + chunkSize, words.length);
      const chunkWords = words.slice(startIndex, endIndex);
      const chunk = chunkWords.join(' ');

      chunks.push(chunk);

      // Move start index forward, accounting for overlap
      if (endIndex >= words.length) {
        break;
      }
      startIndex = endIndex - overlap;
    }

    return chunks;
  }

  /**
   * Create embeddings for multiple text chunks
   */
  private async createChunkEmbeddings(
    chunks: string[],
    knowledgeBaseId: string,
  ): Promise<{ text: string; embedding: number[] }[]> {
    const embeddingPromises = chunks.map(async (chunk, index) => {
      this.logger.debug(`Creating embedding for chunk ${index + 1}/${chunks.length}`);

      const embeddingResult = await this.aiSdk.embedText({
        text: chunk,
        identifierName: 'knowledge-base-service',
        identifierValue: `${knowledgeBaseId}_chunk_${index}`,
      });

      return {
        text: chunk,
        embedding: embeddingResult.embedding,
      };
    });

    return Promise.all(embeddingPromises);
  }

  /**
   * Store multiple chunks as separate vector documents
   */
  private async storeChunksInVectorStore(
    chunks: { text: string; embedding: number[] }[],
    baseMetadata: VectorDocument['metadata'],
    knowledgeBaseId: string,
    namespace: string,
  ): Promise<string[]> {
    const vectorDocuments: VectorDocument[] = chunks.map((chunk, index) => ({
      id: `${knowledgeBaseId}_chunk_${index}`,
      vector: chunk.embedding,
      text: chunk.text,
      metadata: {
        ...baseMetadata,
        knowledgeBaseId,
        chunkIndex: index,
        totalChunks: chunks.length,
        isChunk: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    }));

    try {
      // Store all chunks in batch for better performance
      const vectorIds = await this.vectorStoreService.storeDocumentsBatch(
        vectorDocuments,
        namespace,
      );

      this.logger.log(
        `Stored ${vectorIds.length} chunks for knowledge base ${knowledgeBaseId} in namespace: ${namespace}`,
      );

      return vectorIds;
    } catch (error) {
      this.logger.error(`Failed to store chunks in vector store: ${error}`);
      throw new Error(`Failed to store chunks: ${error}`);
    }
  }

  async listKnowledgeBase(
    query: ListKnowledgeBaseQueryDto,
    namespace: string,
  ): Promise<ListKnowledgeBaseResponseDto> {
    try {
      this.logger.log(
        `Listing knowledge base with query: ${JSON.stringify(query)} in namespace: ${namespace}`,
      );

      // Get distinct knowledge base documents by querying for unique knowledgeBaseIds
      const distinctDocuments = await this.getDistinctKnowledgeBaseDocuments(query, namespace);

      // Convert to KnowledgeBaseItemDto format
      const items: KnowledgeBaseItemDto[] = distinctDocuments.map((doc) => {
        const filename = doc.metadata.title || '';
        const type = doc.metadata.sourceType;

        // Show chunk info if document has multiple chunks
        const textPreview =
          doc.totalChunks > 1
            ? `${doc.text?.slice(0, 80) || ''}... [${doc.totalChunks} chunks]`
            : doc.text?.slice(0, 100) || '';

        return {
          id: doc.knowledgeBaseId,
          type,
          name: doc.metadata.title || filename || 'Untitled',
          text: textPreview,
          score: doc.score,
          metadata: {
            description: doc.metadata.description,
            tags: doc.metadata.tags || [],
            url: doc.metadata.url,
            fileExtension: doc.metadata.fileExtension,
            filename: doc.metadata.title,
            sourceType: doc.metadata.sourceType,
            knowledgeBaseId: doc.knowledgeBaseId,
            createdAt: doc.metadata.createdAt,
            updatedAt: doc.metadata.updatedAt,
            status: 'completed', // Default status since these are already processed
            totalChunks: doc.totalChunks, // Add info about chunk count
          },
        };
      });

      const response: ListKnowledgeBaseResponseDto = {
        items,
        total: items.length,
        limit: query.limit || 20,
        offset: query.offset || 0,
        hasMore: items.length === (query.limit || 20), // Estimate if there are more results
      };

      this.logger.log(
        `Found ${items.length} distinct knowledge base items in namespace: ${namespace}`,
      );
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to list knowledge base: ${errorMessage}`);
      throw new Error(`Failed to list knowledge base: ${errorMessage}`);
    }
  }

  async deleteKnowledgeBase(
    knowledgeBaseId: string,
    namespace: string,
  ): Promise<DeleteKnowledgeBaseResponseDto> {
    try {
      this.logger.log(
        `Deleting knowledge base with ID: ${knowledgeBaseId} in namespace: ${namespace}`,
      );

      // Delete from vector store (support both new knowledgeBaseId and old ingestionId for backward compatibility)
      // This will delete all chunks associated with this knowledgeBaseId
      const deletedCount = await this.vectorStoreService.deleteByKnowledgeBaseId(
        knowledgeBaseId,
        namespace,
      );

      const response: DeleteKnowledgeBaseResponseDto = {
        success: deletedCount > 0,
        deletedCount,
        message:
          deletedCount > 0
            ? `Successfully deleted ${deletedCount} document(s) for knowledge base ${knowledgeBaseId}`
            : `No documents found for knowledge base ${knowledgeBaseId}`,
      };

      this.logger.log(
        `Deletion completed for knowledge base ${knowledgeBaseId}: ${deletedCount} documents deleted`,
      );
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to delete knowledge base ${knowledgeBaseId}: ${errorMessage}`);

      return {
        success: false,
        deletedCount: 0,
        message: `Failed to delete knowledge base: ${errorMessage}`,
      };
    }
  }

  private getFileTypeFromMimeType(mimeType: string): string {
    if (mimeType.startsWith('application/pdf')) return 'pdf';
    if (mimeType.startsWith('application/vnd.openxmlformats-officedocument.wordprocessingml'))
      return 'doc';
    if (mimeType.startsWith('application/msword')) return 'doc';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return 'doc'; // default
  }

  async createKnowledgeBaseWithNamespace(
    dto: CreateKnowledgeBaseDto,
    namespace: string,
  ): Promise<KnowledgeBaseResponseDto> {
    const knowledgeBaseId = this.generateId();

    try {
      this.logger.log(
        `Creating knowledge base with ID: ${knowledgeBaseId} in namespace: ${namespace}`,
      );

      let text = '';
      let derivedName = '';
      const finalType = dto.type;
      const metadata: any = {
        description: dto.description,
        tags: dto.tags,
        sourceType: finalType,
      };

      if (dto.type === KnowledgeBaseType.TEXT && dto.text) {
        text = dto.text;
        derivedName = dto.fileName || 'Text Entry';
        metadata.name = derivedName;
      } else if (dto.type === KnowledgeBaseType.URL && dto.url) {
        text = await this.extractTextFromUrl(dto.url);
        if (dto.fileName) {
          derivedName = dto.fileName;
        } else {
          // Get URL metadata to derive name from title
          try {
            const urlMeta = await this.urlService.getUrlMeta(dto.url);
            derivedName = urlMeta.title || new URL(dto.url).hostname || 'URL Entry';
          } catch {
            derivedName = new URL(dto.url).hostname || 'URL Entry';
          }
        }
        metadata.url = dto.url;
        metadata.name = derivedName;
      } else if (
        dto.type === KnowledgeBaseType.UPLOAD ||
        dto.type === KnowledgeBaseType.VIDEO ||
        dto.type === KnowledgeBaseType.AUDIO ||
        dto.type === KnowledgeBaseType.IMAGE ||
        dto.type === KnowledgeBaseType.PDF ||
        dto.type === KnowledgeBaseType.DOC
      ) {
        if (!dto.fileUrl) {
          throw new Error('File URL is required for file uploads');
        }

        const isImageType = dto.type === KnowledgeBaseType.IMAGE;
        const fileResult = await this.processFileFromUrl(dto.fileUrl, knowledgeBaseId, isImageType);
        text = fileResult.text;
        derivedName =
          dto.fileName || (fileResult.title as string) || fileResult.filename || 'Uploaded File';

        metadata.title = derivedName;
        metadata.fileExtension = fileResult.fileExtension;
        metadata.url = fileResult.s3Url; // S3 storage URL
      } else {
        throw new Error('Invalid knowledge base type or missing required data');
      }

      if (!text.trim()) {
        throw new Error('No text content found to create embeddings');
      }

      // Chunk the text into smaller pieces for better embedding and retrieval
      this.logger.log(`Chunking text for knowledge base ${knowledgeBaseId}`);
      const textChunks = this.chunkText(text, 512, 50); // 512 tokens with 50-word overlap

      this.logger.log(`Created ${textChunks.length} chunks for knowledge base ${knowledgeBaseId}`);

      // Create embeddings for all chunks
      const chunkEmbeddings = await this.createChunkEmbeddings(textChunks, knowledgeBaseId);

      // Store all chunks in vector store with namespace
      const vectorIds = await this.storeChunksInVectorStore(
        chunkEmbeddings,
        metadata,
        knowledgeBaseId,
        namespace,
      );

      // For backward compatibility, return the first vector ID as the main vectorId
      const vectorId = vectorIds[0] || knowledgeBaseId;

      const response: KnowledgeBaseResponseDto = {
        id: knowledgeBaseId,
        type: finalType,
        name: derivedName,
        status: 'completed',
        vectorId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.logger.log(
        `Knowledge base created successfully with ID: ${knowledgeBaseId} in namespace: ${namespace}`,
      );
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to create knowledge base ${knowledgeBaseId}: ${errorMessage}`);

      return {
        id: knowledgeBaseId,
        type: dto.type,
        status: 'failed',
        message: errorMessage,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }
  }

  private isImageFile(fileExtension: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
    return imageExtensions.includes(fileExtension.toLowerCase());
  }

  async getKnowledgeBaseStats(namespace: string): Promise<KnowledgeBaseStatsResponseDto> {
    try {
      // Get all documents and group them by knowledgeBaseId in code for better reliability
      const allDocuments = await this.vectorSearchService.listDocuments(
        1000, // Set a high limit to get all documents for stats
        undefined, // No filters - get all documents
        'created_at', // Order by created_at
        'desc', // Descending order
        namespace,
      );

      // Group by knowledgeBaseId to count unique documents
      const groupedByKnowledgeBaseId = new Map<string, VectorSearchResult[]>();

      allDocuments.forEach((doc) => {
        const knowledgeBaseId = doc.metadata.knowledgeBaseId;
        if (knowledgeBaseId) {
          if (!groupedByKnowledgeBaseId.has(knowledgeBaseId)) {
            groupedByKnowledgeBaseId.set(knowledgeBaseId, []);
          }
          groupedByKnowledgeBaseId.get(knowledgeBaseId)!.push(doc);
        }
      });

      // Get representative document for each knowledgeBaseId (prioritize non-chunk documents)
      const uniqueDocuments = Array.from(groupedByKnowledgeBaseId.entries()).map(
        ([, documents]) => {
          documents.sort((a, b) => {
            const aIsChunk = a.metadata.isChunk ?? false;
            const bIsChunk = b.metadata.isChunk ?? false;

            // Prioritize non-chunk documents
            if (!aIsChunk && bIsChunk) return -1;
            if (aIsChunk && !bIsChunk) return 1;

            // If both are chunks, prioritize chunk 0
            if (aIsChunk && bIsChunk) {
              const aChunkIndex = a.metadata.chunkIndex ?? -1;
              const bChunkIndex = b.metadata.chunkIndex ?? -1;
              if (aChunkIndex === 0 && bChunkIndex !== 0) return -1;
              if (aChunkIndex !== 0 && bChunkIndex === 0) return 1;
            }

            return 0;
          });

          return documents[0]!;
        },
      );

      // Group by file type and count
      const typeCounts = new Map<KnowledgeBaseType, number>();

      // Initialize all types with 0
      Object.values(KnowledgeBaseType).forEach((type) => {
        typeCounts.set(type, 0);
      });

      uniqueDocuments.forEach((doc) => {
        const type = doc.metadata.sourceType;
        this.logger.debug(
          `Processing document - ID: ${doc.metadata.knowledgeBaseId}, sourceType: ${type}, totalChunks: ${doc.metadata.totalChunks || 1}`,
        );
        if (type) {
          typeCounts.set(type, (typeCounts.get(type) || 0) + 1);
        } else {
          this.logger.warn(
            `Document ${doc.metadata.knowledgeBaseId} has no sourceType in metadata`,
          );
        }
      });

      // Create response with display names
      const typeDisplayNames: Record<KnowledgeBaseType, string> = {
        [KnowledgeBaseType.VIDEO]: 'Video',
        [KnowledgeBaseType.AUDIO]: 'Audio',
        [KnowledgeBaseType.IMAGE]: 'Image',
        [KnowledgeBaseType.PDF]: 'PDF',
        [KnowledgeBaseType.DOC]: 'Doc',
        [KnowledgeBaseType.URL]: 'Link',
        [KnowledgeBaseType.TEXT]: 'Text',
        [KnowledgeBaseType.UPLOAD]: 'File',
      };

      const stats: KnowledgeBaseStatsItemDto[] = Object.values(KnowledgeBaseType).map((type) => ({
        type,
        count: typeCounts.get(type) || 0,
        displayName: typeDisplayNames[type],
      }));

      const total = uniqueDocuments.length;

      this.logger.log(
        `Retrieved knowledge base stats for namespace: ${namespace}, total unique documents: ${total} (from ${allDocuments.length} total documents)`,
      );

      // Add debug information to the logs
      this.logger.debug(`Type counts: ${JSON.stringify(Object.fromEntries(typeCounts))}`);
      this.logger.debug(
        `Sample documents: ${JSON.stringify(
          uniqueDocuments.slice(0, 3).map((doc) => ({
            id: doc.id,
            knowledgeBaseId: doc.metadata.knowledgeBaseId,
            sourceType: doc.metadata.sourceType,
            totalChunks: doc.metadata.totalChunks || 1,
            metadata: doc.metadata,
          })),
        )}`,
      );

      return {
        stats,
        total,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Failed to get knowledge base stats for namespace ${namespace}: ${errorMessage}`,
      );

      // Return empty stats on error
      const emptyStats: KnowledgeBaseStatsItemDto[] = Object.values(KnowledgeBaseType).map(
        (type) => ({
          type,
          count: 0,
          displayName: type.charAt(0).toUpperCase() + type.slice(1),
        }),
      );

      return {
        stats: emptyStats,
        total: 0,
      };
    }
  }

  async deleteNamespace(namespace: string): Promise<number> {
    return await this.vectorStoreService.deleteNamespace(namespace);
  }

  /**
   * Get distinct knowledge base documents by querying for the first chunk of each document
   */
  private async getDistinctKnowledgeBaseDocuments(
    query: ListKnowledgeBaseQueryDto,
    namespace: string,
  ): Promise<
    Array<{
      knowledgeBaseId: string;
      text: string;
      score: number;
      totalChunks: number;
      metadata: VectorSearchResult['metadata'];
    }>
  > {
    // Build filters based on query parameters
    const filters: any[] = [];

    // Filter by type if specified
    if (query.type) {
      filters.push(['source_type', 'In', query.type]);
    }

    // Filter by tags if specified
    if (query.tags && query.tags.length > 0) {
      filters.push(['tags', 'In', query.tags]);
    }

    // Don't filter by chunk info at database level to avoid issues with null handling
    // We'll handle chunk filtering in code instead

    // Combine filters with AND
    const finalFilters =
      filters.length > 1 ? ['And', filters] : filters.length === 1 ? filters[0] : undefined;

    let results: VectorSearchResult[];
    if (query.search) {
      // Use text search if search query is provided
      const searchResult = await this.vectorSearchService.textSearch({
        query: query.search,
        topK: (query.limit || 20) * 3, // Get more results to account for filtering
        filters: finalFilters,
        namespace,
      });
      results = searchResult.items;
    } else {
      // Use the listDocuments method for ordered listing
      results = await this.vectorSearchService.listDocuments(
        (query.limit || 20) * 3, // Get more results to account for potential duplicates
        finalFilters,
        'created_at',
        'desc',
        namespace,
      );
    }

    // Group by knowledgeBaseId and select the representative document for each group
    const groupedByKnowledgeBaseId = new Map<string, VectorSearchResult[]>();

    results.forEach((result) => {
      const knowledgeBaseId = result.metadata.knowledgeBaseId;
      if (knowledgeBaseId) {
        if (!groupedByKnowledgeBaseId.has(knowledgeBaseId)) {
          groupedByKnowledgeBaseId.set(knowledgeBaseId, []);
        }
        groupedByKnowledgeBaseId.get(knowledgeBaseId)!.push(result);
      }
    });

    // Select the representative document for each knowledgeBaseId
    const distinctDocuments = Array.from(groupedByKnowledgeBaseId.entries())
      .map(([knowledgeBaseId, documents]) => {
        // Sort documents to prioritize:
        // 1. Non-chunk documents (isChunk = false or undefined)
        // 2. First chunk (chunkIndex = 0)
        // 3. Fallback to any document
        documents.sort((a, b) => {
          const aIsChunk = a.metadata.isChunk ?? false;
          const bIsChunk = b.metadata.isChunk ?? false;
          const aChunkIndex = a.metadata.chunkIndex ?? -1;
          const bChunkIndex = b.metadata.chunkIndex ?? -1;

          // Prioritize non-chunk documents
          if (!aIsChunk && bIsChunk) return -1;
          if (aIsChunk && !bIsChunk) return 1;

          // If both are chunks, prioritize chunk 0
          if (aIsChunk && bIsChunk) {
            if (aChunkIndex === 0 && bChunkIndex !== 0) return -1;
            if (aChunkIndex !== 0 && bChunkIndex === 0) return 1;
            return aChunkIndex - bChunkIndex;
          }

          // Default to creation date (most recent first)
          return (
            new Date(b.metadata.createdAt).getTime() - new Date(a.metadata.createdAt).getTime()
          );
        });

        const representative = documents[0]!;
        return {
          knowledgeBaseId,
          text: representative.text,
          score: representative.score || 0,
          totalChunks: representative.metadata.totalChunks || documents.length,
          metadata: representative.metadata,
        };
      })
      .slice(0, query.limit || 20); // Apply final limit

    this.logger.log(
      `Retrieved ${distinctDocuments.length} distinct documents (from ${results.length} total results) in namespace: ${namespace}`,
    );

    return distinctDocuments;
  }

  async debugDocuments(namespace: string): Promise<any> {
    try {
      this.logger.log(`Debugging documents in namespace: ${namespace}`);

      // Get raw documents from vector store
      const allDocuments = await this.vectorSearchService.listDocuments(
        50, // Limit to 50 for debugging
        undefined, // No filters
        'created_at', // Order by created_at
        'desc', // Descending order
        namespace,
      );

      this.logger.log(`Found ${allDocuments.length} documents in namespace: ${namespace}`);

      // Return detailed information about each document
      const debugInfo = {
        namespace,
        totalDocuments: allDocuments.length,
        documents: allDocuments.map((doc) => ({
          id: doc.id,
          sourceType: doc.metadata.sourceType,
          hasSourceType: !!doc.metadata.sourceType,
          metadata: doc.metadata,
          textPreview: doc.text?.slice(0, 100) + '...',
        })),
        typeCountsRaw: allDocuments.reduce(
          (acc, doc) => {
            const type = doc.metadata.sourceType || 'unknown';
            acc[type] = (acc[type] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        ),
      };

      return debugInfo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to debug documents in namespace ${namespace}: ${errorMessage}`);
      return {
        error: errorMessage,
        namespace,
        totalDocuments: 0,
        documents: [],
      };
    }
  }
}
