import { VectorDocument } from '@/modules/vector-store';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateIf,
} from 'class-validator';

export enum KnowledgeBaseType {
  TEXT = 'text',
  URL = 'url',
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
  PDF = 'pdf',
  DOC = 'doc',
  UPLOAD = 'upload',
}

export class CreateKnowledgeBaseDto {
  @ApiProperty({
    enum: KnowledgeBaseType,
    description: 'Type of knowledge base entry',
    example: KnowledgeBaseType.TEXT,
  })
  @IsEnum(KnowledgeBaseType)
  type!: KnowledgeBaseType;

  @ApiPropertyOptional({
    description: 'Description of the knowledge base entry',
    example: 'Important company documentation',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    type: [String],
    description: 'Tags to categorize the knowledge base entry',
    example: ['documentation', 'company', 'important'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description:
      'Optional name for the knowledge base entry. If not provided, will be derived from URL meta or file meta',
    example: 'Company Policy Document',
  })
  @IsOptional()
  @IsString()
  fileName?: string;

  @ApiPropertyOptional({
    description:
      'File URL (required when type is "upload", "video", "audio", "image", "pdf", or "doc"). Should be a publicly accessible URL to the file.',
    example: 'https://example.com/files/document.pdf',
  })
  @ValidateIf(
    (o) =>
      o.type === KnowledgeBaseType.UPLOAD ||
      o.type === KnowledgeBaseType.VIDEO ||
      o.type === KnowledgeBaseType.AUDIO ||
      o.type === KnowledgeBaseType.IMAGE ||
      o.type === KnowledgeBaseType.PDF ||
      o.type === KnowledgeBaseType.DOC,
  )
  @IsString()
  fileUrl?: string;

  @ApiPropertyOptional({
    description: 'Text content to be added to knowledge base (required when type is "text")',
    example: 'This is some important text content that should be stored in the knowledge base.',
  })
  @ValidateIf((o) => o.type === KnowledgeBaseType.TEXT)
  @IsString()
  text?: string;

  @ApiPropertyOptional({
    description: 'URL to extract content from (required when type is "url")',
    example: 'https://example.com/article',
  })
  @ValidateIf((o) => o.type === KnowledgeBaseType.URL)
  @IsString()
  url?: string;
}

export class CreateKnowledgeBaseMultipartDto {
  @ApiProperty({
    enum: KnowledgeBaseType,
    description: 'Type of knowledge base entry',
    example: KnowledgeBaseType.UPLOAD,
  })
  @IsEnum(KnowledgeBaseType)
  type!: KnowledgeBaseType;

  @ApiPropertyOptional({
    description: 'Description of the knowledge base entry',
    example: 'Important company documentation',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Comma-separated tags to categorize the knowledge base entry',
    example: 'documentation,company,important',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({
    description:
      'Optional name for the knowledge base entry. If not provided, will be derived from URL meta or file meta',
    example: 'Company Policy Document',
  })
  @IsOptional()
  @IsString()
  fileName?: string;

  @ApiPropertyOptional({
    type: 'string',
    format: 'binary',
    description:
      'File to upload (required when type is "upload", "video", "audio", "image", "pdf", or "doc")',
  })
  file?: any;

  @ApiPropertyOptional({
    description: 'Text content to be added to knowledge base (required when type is "text")',
    example: 'This is some important text content that should be stored in the knowledge base.',
  })
  @ValidateIf((o) => o.type === KnowledgeBaseType.TEXT)
  @IsString()
  text?: string;

  @ApiPropertyOptional({
    description: 'URL to extract content from (required when type is "url")',
    example: 'https://example.com/article',
  })
  @ValidateIf((o) => o.type === KnowledgeBaseType.URL)
  @IsString()
  url?: string;
}

export class KnowledgeBaseResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the knowledge base entry',
    example: '507f1f77bcf86cd799439011',
  })
  id!: string;

  @ApiProperty({
    enum: KnowledgeBaseType,
    description: 'Type of knowledge base entry',
    example: KnowledgeBaseType.TEXT,
  })
  type!: KnowledgeBaseType;

  @ApiPropertyOptional({
    description: 'Name of the knowledge base entry',
    example: 'Company Policy Document',
  })
  name?: string;

  @ApiProperty({
    enum: ['processing', 'completed', 'failed'],
    description: 'Processing status of the knowledge base entry',
    example: 'completed',
  })
  status!: 'processing' | 'completed' | 'failed';

  @ApiPropertyOptional({
    description: 'Status message or error details',
    example: 'Successfully processed and added to knowledge base',
  })
  message?: string;

  @ApiPropertyOptional({
    description: 'Vector store ID for the processed content',
    example: 'vec_507f1f77bcf86cd799439011',
  })
  vectorId?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:35:00Z',
  })
  updatedAt!: Date;
}

export class ListKnowledgeBaseQueryDto {
  @ApiPropertyOptional({
    description: 'Search query for full-text search',
    example: 'company policy',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: KnowledgeBaseType,
    description: 'Filter by knowledge base entry type',
    example: KnowledgeBaseType.TEXT,
  })
  @IsOptional()
  @IsEnum(KnowledgeBaseType)
  type?: KnowledgeBaseType;

  @ApiPropertyOptional({
    type: [String],
    description: 'Filter by tags',
    example: ['documentation', 'important'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of results to return (1-100)',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of results to skip',
    minimum: 0,
    default: 0,
    example: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number = 0;
}

export class KnowledgeBaseItemDto {
  @ApiProperty({
    description: 'Unique identifier for the knowledge base item',
    example: '507f1f77bcf86cd799439011',
  })
  id!: string;

  @ApiProperty({
    enum: KnowledgeBaseType,
    description: 'Type of knowledge base entry',
    example: KnowledgeBaseType.TEXT,
  })
  type!: KnowledgeBaseType;

  @ApiPropertyOptional({
    description: 'Name of the knowledge base entry',
    example: 'Company Policy Document',
  })
  name?: string;

  @ApiProperty({
    description: 'Text content of the knowledge base item',
    example: 'This is the extracted text content from the document.',
  })
  text!: string;

  @ApiPropertyOptional({
    type: Number,
    description: 'Relevance score for search results',
    example: 0.85,
  })
  score?: number;

  @ApiProperty({
    description: 'Metadata associated with the knowledge base item',
    example: {
      description: 'Company policy document',
      tags: ['policy', 'hr'],
      sourceType: 'upload',
      createdAt: '2024-01-15T10:30:00Z',
      status: 'completed',
    },
  })
  metadata!: VectorDocument['metadata'];
}

export class ListKnowledgeBaseResponseDto {
  @ApiProperty({
    type: [KnowledgeBaseItemDto],
    description: 'List of knowledge base items',
  })
  items!: KnowledgeBaseItemDto[];

  @ApiProperty({
    type: Number,
    description: 'Total number of items available',
    example: 150,
  })
  total!: number;

  @ApiProperty({
    type: Number,
    description: 'Number of items returned in this response',
    example: 20,
  })
  limit!: number;

  @ApiProperty({
    type: Number,
    description: 'Number of items skipped',
    example: 0,
  })
  offset!: number;

  @ApiProperty({
    type: Boolean,
    description: 'Whether there are more items available',
    example: true,
  })
  hasMore!: boolean;
}

export class DeleteKnowledgeBaseResponseDto {
  @ApiProperty({
    type: Boolean,
    description: 'Whether the deletion was successful',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    type: Number,
    description: 'Number of items deleted',
    example: 5,
  })
  deletedCount!: number;

  @ApiProperty({
    description: 'Deletion confirmation message',
    example: 'Knowledge base entry and 5 associated documents deleted successfully',
  })
  message!: string;
}

export class KnowledgeBaseStatsItemDto {
  @ApiProperty({
    enum: KnowledgeBaseType,
    description: 'Type of knowledge base entry',
    example: KnowledgeBaseType.PDF,
  })
  type!: KnowledgeBaseType;

  @ApiProperty({
    type: Number,
    description: 'Number of items of this type',
    example: 25,
  })
  count!: number;

  @ApiProperty({
    description: 'Display name for the type',
    example: 'PDF',
  })
  displayName!: string;
}

export class KnowledgeBaseStatsResponseDto {
  @ApiProperty({
    type: [KnowledgeBaseStatsItemDto],
    description: 'Statistics for each knowledge base type',
  })
  stats!: KnowledgeBaseStatsItemDto[];

  @ApiProperty({
    type: Number,
    description: 'Total number of knowledge base items',
    example: 150,
  })
  total!: number;
}
