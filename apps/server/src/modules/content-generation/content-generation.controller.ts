import { Auth } from '@/auth/guards/auth.guard';
import { Team } from '@/resources/team/team.model';
import { User } from '@/resources/user/user.model';
import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ReqUser } from '../../common/decorators/req-user.decorator';
import { ContentGenerationService } from './content-generation.service';
import {
  ContentFormat,
  ContentGenerationListQueryDto,
  ContentGenerationListResponseDto,
  ContentGenerationResponseDto,
  GenerateContentDto,
} from './dto/content-generation.dto';

@ApiTags('Content Generation')
@UseGuards(Auth)
@ApiBearerAuth()
@Controller('content-generation')
export class ContentGenerationController {
  constructor(private readonly contentGenerationService: ContentGenerationService) {}

  @Post(':snippetId')
  @ApiParam({
    name: 'snippetId',
    description: 'Unique identifier for the snippet',
    example: 'snippet_123',
  })
  @ApiOperation({
    summary: 'Generate curriculum content using RAG',
    description: `Generate educational content based on curriculum materials using RAG (Retrieval-Augmented Generation).

This endpoint:
- Uses teamId from authentication for knowledge base namespace
- Uses snippetId from URL parameter for temporary file storage
- Uses knowledge base content when knowledgeBase is true
- Performs web search when webSearch is true
- Applies LLM generation with specified parameters
- Returns structured content with flags
- Cleans up temporary namespace after 2 hours

Example request:
{
  "category": "Mathematics",
  "snippetName": "Linear Equations",
  "snippetDescription": "Introduction to solving linear equations",
  "knowledgeBase": true,
  "webSearch": false,
  "grade": "9th Grade",
  "userInstructions": "Include step-by-step examples",
  "prompt": "Create a lesson plan for linear equations",
  "fileUrls": ["https://example.com/files/document.pdf"],
  "urls": ["https://wikipedia.org/wiki/Linear_equation"],
  "customProperties": [
    {"key": "duration", "value": "45 minutes"},
    {"key": "difficulty", "value": "beginner"}
  ]
}`,
  })
  @ApiResponse({
    status: 201,
    description: 'Content generated successfully',
    type: ContentGenerationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during content generation',
  })
  async generateContent(
    @Param('snippetId') snippetId: string,
    @Body() dto: GenerateContentDto,
    @ReqUser() user: User,
  ): Promise<ContentGenerationResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    if (!user.team) {
      throw new Error('User must belong to a team to generate content');
    }

    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('Invalid team ID');
    }

    return this.contentGenerationService.generateContent(teamId, snippetId, dto);
  }

  @Get()
  @ApiOperation({
    summary: 'List generated content',
    description:
      'Retrieve a list of previously generated content for the authenticated team with optional filtering',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search within content text' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'grade', required: false, description: 'Filter by grade level' })
  @ApiQuery({
    name: 'format',
    required: false,
    enum: ContentFormat,
    description: 'Filter by content format',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of results to return (1-100)',
    minimum: 1,
    maximum: 100,
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of results to skip',
    minimum: 0,
  })
  @ApiResponse({
    status: 200,
    description: 'List of generated content retrieved successfully',
    // type: ContentGenerationListResponseDto, // Removed problematic type reference
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async listContent(
    @Query() query: ContentGenerationListQueryDto,
    @ReqUser() user: User,
  ): Promise<ContentGenerationListResponseDto> {
    if (!user) {
      throw new Error('User authentication failed');
    }

    if (!user.team) {
      throw new Error('User must belong to a team to list content');
    }

    const teamId = (user.team as Team)._id.toString();
    if (!teamId) {
      throw new Error('Invalid team ID');
    }

    return this.contentGenerationService.listContent(teamId, query);
  }
}
