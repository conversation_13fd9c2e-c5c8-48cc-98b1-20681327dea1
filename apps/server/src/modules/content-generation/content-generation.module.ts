import { ImgModule } from '@/img';
import { Modu<PERSON> } from '@nestjs/common';
import { LlmModule } from '../../llm/llm.module';
import { RagModule } from '../rag/rag.module';
import { ContentGenerationController } from './content-generation.controller';
import { ContentGenerationService } from './content-generation.service';
import {
  ChartGenerationService,
  CitationGenerationService,
  DiagramGenerationService,
  GraphGenerationService,
  PlaceholderReplacementService,
} from './services';

@Module({
  imports: [RagModule, LlmModule, ImgModule],
  controllers: [ContentGenerationController],
  providers: [
    ContentGenerationService,
    ChartGenerationService,
    GraphGenerationService,
    DiagramGenerationService,
    CitationGenerationService,
    PlaceholderReplacementService,
  ],
  exports: [ContentGenerationService],
})
export class ContentGenerationModule {}
