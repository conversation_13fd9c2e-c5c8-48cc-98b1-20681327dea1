import { Injectable, Logger } from '@nestjs/common';
import { ImageArtifact, ImageGenerationService } from '../../img/services';
import { AiSdk } from '../../llm/sdk/AiSdk';
import { RagService } from '../rag/rag.service';
import {
  ContentFormat,
  ContentGenerationListQueryDto,
  ContentGenerationListResponseDto,
  ContentGenerationResponseDto,
  GenerateContentDto,
} from './dto/content-generation.dto';
import {
  ChartArtifact,
  ChartGenerationService,
  CitationGenerationService,
  CitationPlaceholder,
  DiagramArtifact,
  DiagramGenerationService,
  GraphArtifact,
  GraphGenerationService,
  PlaceholderReplacementService,
} from './services';

@Injectable()
export class ContentGenerationService {
  private readonly logger = new Logger(ContentGenerationService.name);
  private readonly generatedContent: Map<string, ContentGenerationResponseDto> = new Map();
  private readonly sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

  constructor(
    private readonly aiSdk: AiSdk,
    private readonly ragService: RagService,
    private readonly chartGenerationService: ChartGenerationService,
    private readonly graphGenerationService: GraphGenerationService,
    private readonly diagramGenerationService: DiagramGenerationService,
    private readonly imageGenerationService: ImageGenerationService,
    private readonly citationGenerationService: CitationGenerationService,
    private readonly placeholderReplacementService: PlaceholderReplacementService,
  ) {}

  async generateContent(
    teamId: string,
    snippetId: string,
    dto: GenerateContentDto,
  ): Promise<ContentGenerationResponseDto> {
    const startTime = Date.now();
    const uniqueRequestId = `${this.sessionId}_${teamId}_${snippetId}_${Date.now()}`;
    this.logger.log(
      `Starting content generation for snippet: ${snippetId}, team: ${teamId}, requestId: ${uniqueRequestId}`,
    );

    try {
      // Process temporary files if provided
      if (dto.fileUrls || dto.urls) {
        this.logger.log(
          `Processing ${dto.fileUrls?.length || 0} files and ${dto.urls?.length || 0} urls`,
        );
        await this.ragService.processTemporaryFiles(teamId, snippetId, dto.fileUrls, dto.urls);
        this.logger.log(
          `Temporary files and urls processed in ${(Date.now() - startTime) / 1000}s`,
        );
      }

      const ragStartTime = Date.now();
      // Build RAG context
      const ragContext = await this.ragService.buildRagContext(
        teamId,
        snippetId,
        dto.category,
        dto.snippetName,
        dto.snippetDescription,
        dto.grade,
        dto.knowledgeBase,
        dto.webSearch,
      );
      const ragTime = (Date.now() - ragStartTime) / 1000;
      this.logger.log(`RAG context built in ${ragTime}s`);

      // Build the system prompt with output settings
      const systemPrompt = this.buildSystemPrompt(dto, ragContext.content);

      // Generate initial content using AI
      const result = await this.aiSdk.generateText({
        prompt: dto.prompt,
        systemPrompt,
        provider: 'openai',
        model: 'gpt-4.1',
        identifierName: 'content-generation',
        identifierValue: 'curriculum-materials',
      });

      let generatedContent = result.text;
      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.log(`Content generation (text) completed in ${processingTime}s`);

      // Extract artifact placeholders mapping and clean content
      const extractionStartTime = Date.now();
      const { mapping: artifactMapping, cleanedContent } =
        this.extractArtifactPlaceholdersMapping(generatedContent);
      generatedContent = cleanedContent;
      const extractionTime = (Date.now() - extractionStartTime) / 1000;
      this.logger.debug(`Artifact placeholders extracted in ${extractionTime}s`);

      // Analyze content for visual elements
      const contentAnalysis = this.analyzeGeneratedContent(generatedContent);

      // Generate artifacts for visual elements in parallel with unique request context
      const artifactsGenerationStartTime = Date.now();
      const totalArtifactsToGenerate = Object.values(contentAnalysis).filter(Boolean).length;
      this.logger.log(`Total artifacts to generate: ${totalArtifactsToGenerate}`);
      const artifacts = await this.generateArtifacts(
        dto,
        generatedContent,
        contentAnalysis,
        teamId,
        snippetId,
        uniqueRequestId, // Pass unique request ID
        artifactMapping, // Pass the extracted mapping
      );
      const artifactsGenerationEndTime = (Date.now() - artifactsGenerationStartTime) / 1000;
      this.logger.debug(`Visual Artifacts generated in ${artifactsGenerationEndTime}s`);

      // Replace placeholders with actual content (images, citations, etc.)
      const outputFormat = this.getOutputFormatForReplacement(dto.outputSettings.format);

      // Validate and log placeholder information before replacement
      this.validatePlaceholders(artifacts);

      const replacementResult =
        await this.placeholderReplacementService.replacePlaceholdersWithImages(
          generatedContent,
          artifacts,
          outputFormat,
        );

      generatedContent = replacementResult.content;

      if (replacementResult.errors.length > 0) {
        this.logger.warn(`Placeholder replacement had ${replacementResult.errors.length} errors:`, {
          errors: replacementResult.errors,
        });
      }

      this.logger.debug(`Successfully replaced ${replacementResult.replacedCount} placeholders`);

      // Calculate total cost
      const totalCost = this.calculateTotalCost(artifacts);

      // Create response using output settings
      const response: ContentGenerationResponseDto = {
        content: generatedContent,
        length: generatedContent.length,
        format: dto.outputSettings.format,
        language: dto.outputSettings.language,
        totalCost,
      };

      // Store in memory (in a real app, this would be saved to database)
      const storageKey = `${teamId}_${snippetId}`;
      this.generatedContent.set(storageKey, response);

      this.logger.log(
        `Content generation completed for snippet: ${snippetId} in ${processingTime}s, total cost: $${totalCost.toFixed(6)}`,
      );
      return response;
    } catch (error) {
      this.logger.error(`Error generating content for snippet ${snippetId}:`, error);
      throw error;
    }
  }

  private async generateArtifacts(
    dto: GenerateContentDto,
    content: string,
    contentAnalysis: any,
    _teamId: string,
    _snippetId: string,
    _uniqueRequestId: string,
    artifactMapping: {
      images: Map<string, string>;
      graphs: Map<string, string>;
      charts: Map<string, string>;
      diagrams: Map<string, string>;
      citations: Map<string, string>;
    },
  ): Promise<{
    charts: ChartArtifact[];
    graphs: GraphArtifact[];
    diagrams: DiagramArtifact[];
    images: ImageArtifact[];
    citations: CitationPlaceholder | null;
  }> {
    const startTime = Date.now();
    this.logger.debug('Starting parallel visual placeholder generation');

    // Create array of generation tasks to run in parallel
    const generationTasks: Promise<any>[] = [];
    const taskTypes: string[] = [];

    // Chart generation task
    if (contentAnalysis.hasCharts) {
      taskTypes.push('charts');
      generationTasks.push(
        this.generateChartArtifacts(
          dto.snippetName,
          content,
          dto.grade,
          _uniqueRequestId,
          artifactMapping.charts,
        ).catch((error) => {
          this.logger.error('Chart generation failed:', error);
          return [] as ChartArtifact[];
        }),
      );
    }

    // Graph generation task
    if (contentAnalysis.hasGraphs) {
      taskTypes.push('graphs');
      generationTasks.push(
        this.generateGraphArtifacts(
          dto.snippetName,
          content,
          dto.grade,
          _uniqueRequestId,
          artifactMapping.graphs,
        ).catch((error) => {
          this.logger.error('Graph generation failed:', error);
          return [] as GraphArtifact[];
        }),
      );
    }

    // Diagram generation task
    if (contentAnalysis.hasDiagrams) {
      taskTypes.push('diagrams');
      generationTasks.push(
        this.generateDiagramArtifacts(
          dto.snippetName,
          content,
          dto.grade,
          _uniqueRequestId,
          artifactMapping.diagrams,
        ).catch((error) => {
          this.logger.error('Diagram generation failed:', error);
          return [] as DiagramArtifact[];
        }),
      );
    }

    // Image generation task
    if (contentAnalysis.hasImages) {
      taskTypes.push('images');
      generationTasks.push(
        this.generateImageArtifacts(
          dto.snippetName,
          content,
          dto.grade,
          _uniqueRequestId,
          artifactMapping.images,
        ).catch((error) => {
          this.logger.error('Image generation failed:', error);
          return [] as ImageArtifact[];
        }),
      );
    }

    // Citation generation task
    if (contentAnalysis.hasCitations) {
      taskTypes.push('citations');
      generationTasks.push(
        this.generateCitationPlaceholders(
          dto.snippetName,
          content,
          dto.grade,
          _uniqueRequestId,
          artifactMapping.citations,
        ).catch((error) => {
          this.logger.error('Citation generation failed:', error);
          return null as CitationPlaceholder | null;
        }),
      );
    }

    this.logger.debug(
      `Running ${generationTasks.length} placeholder generation tasks in parallel: ${taskTypes.join(', ')}`,
    );

    // Execute all tasks in parallel with Promise.allSettled for better error handling
    const results = await Promise.allSettled(generationTasks);

    // Process results and map them back to the correct placeholder types
    const placeholders = {
      charts: [] as ChartArtifact[],
      graphs: [] as GraphArtifact[],
      diagrams: [] as DiagramArtifact[],
      images: [] as ImageArtifact[],
      citations: null as CitationPlaceholder | null,
    };

    for (let i = 0; i < taskTypes.length; i++) {
      const taskType = taskTypes[i];
      const result = results[i];

      if (result && result.status === 'fulfilled') {
        switch (taskType) {
          case 'charts':
            placeholders.charts = (result as PromiseFulfilledResult<ChartArtifact[]>).value || [];
            break;
          case 'graphs':
            placeholders.graphs = (result as PromiseFulfilledResult<GraphArtifact[]>).value || [];
            break;
          case 'diagrams':
            placeholders.diagrams =
              (result as PromiseFulfilledResult<DiagramArtifact[]>).value || [];
            break;
          case 'images':
            placeholders.images = (result as PromiseFulfilledResult<ImageArtifact[]>).value || [];
            break;
          case 'citations':
            placeholders.citations = (
              result as PromiseFulfilledResult<CitationPlaceholder | null>
            ).value;
            break;
        }
        this.logger.debug(`✓ ${taskType} generation completed successfully`);
      } else {
        this.logger.error(
          `✗ ${taskType} generation failed:`,
          (result as PromiseRejectedResult).reason,
        );
      }
    }

    const processingTime = (Date.now() - startTime) / 1000;
    const totalGenerated =
      placeholders.charts.length +
      placeholders.graphs.length +
      placeholders.diagrams.length +
      placeholders.images.length +
      (placeholders.citations ? 1 : 0);

    this.logger.log(
      `Parallel placeholder generation completed in ${processingTime}s. Generated ${totalGenerated} placeholders across ${taskTypes.length} types.`,
    );

    return placeholders;
  }

  private calculateTotalCost(placeholders: {
    charts: ChartArtifact[];
    graphs: GraphArtifact[];
    diagrams: DiagramArtifact[];
    images: ImageArtifact[];
    citations: CitationPlaceholder | null;
  }): number {
    let totalCost = 0;

    placeholders.charts.forEach((chart) => (totalCost += chart.cost));
    placeholders.graphs.forEach((graph) => (totalCost += graph.cost));
    placeholders.diagrams.forEach((diagram) => (totalCost += diagram.cost));
    placeholders.images.forEach((image) => (totalCost += image.cost));
    if (placeholders.citations) totalCost += placeholders.citations.cost;

    return totalCost;
  }

  private async generateChartArtifacts(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    _uniqueRequestId: string,
    chartMapping: Map<string, string>,
  ): Promise<ChartArtifact[]> {
    try {
      if (chartMapping.size === 0) {
        return [];
      }

      const charts: ChartArtifact[] = [];

      // Generate charts based on the extracted prompt mappings
      for (const [placeholderId, promptPlaceholder] of chartMapping.entries()) {
        this.logger.debug(
          `Generating chart for placeholder: ${placeholderId} with prompt: ${promptPlaceholder}`,
        );

        const chartData = {
          data: [], // Will be filled by the service based on prompt
          labels: [],
          description: promptPlaceholder,
        };

        // Generate single chart with correct placeholder format
        const chart = await this.chartGenerationService.generateChart(
          sectionHeading,
          chartData,
          content,
          gradeLevel,
        );

        if (chart) {
          // Override the placeholder to match content format ({{CHARTS_1}} instead of {{CHART_1}})
          chart.placeholder = `{{${placeholderId}}}`;
          charts.push(chart);
        }
      }

      return charts;
    } catch (error) {
      this.logger.error('Failed to generate chart placeholders', {
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  private async generateGraphArtifacts(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    _uniqueRequestId: string,
    graphMapping: Map<string, string>,
  ): Promise<GraphArtifact[]> {
    try {
      if (graphMapping.size === 0) {
        return [];
      }

      const graphs: GraphArtifact[] = [];

      // Generate graphs based on the extracted prompt mappings
      for (const [placeholderId, promptPlaceholder] of graphMapping.entries()) {
        this.logger.debug(
          `Generating graph for placeholder: ${placeholderId} with prompt: ${promptPlaceholder}`,
        );

        // Generate single graph with correct placeholder format
        const graph = await this.graphGenerationService.generateGraph(
          sectionHeading,
          promptPlaceholder,
          content,
          gradeLevel,
        );

        if (graph) {
          // Override the placeholder to match content format ({{GRAPHS_1}} instead of {{GRAPH_1}})
          graph.placeholder = `{{${placeholderId}}}`;
          graphs.push(graph);
        }
      }

      return graphs;
    } catch (error) {
      this.logger.error('Failed to generate graph placeholders', {
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  private async generateDiagramArtifacts(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    _uniqueRequestId: string,
    diagramMapping: Map<string, string>,
  ): Promise<DiagramArtifact[]> {
    try {
      if (diagramMapping.size === 0) {
        return [];
      }

      const diagrams: DiagramArtifact[] = [];

      // Generate diagrams based on the extracted prompt mappings
      for (const [placeholderId, promptPlaceholder] of diagramMapping.entries()) {
        this.logger.debug(
          `Generating diagram for placeholder: ${placeholderId} with prompt: ${promptPlaceholder}`,
        );

        // Generate single diagram with correct placeholder format
        const diagram = await this.diagramGenerationService.generateDiagram(
          sectionHeading,
          promptPlaceholder,
          content,
          gradeLevel,
        );

        if (diagram) {
          // Override the placeholder to match content format ({{DIAGRAMS_1}} instead of {{DIAGRAM_1}})
          diagram.placeholder = `{{${placeholderId}}}`;
          diagrams.push(diagram);
        }
      }

      return diagrams;
    } catch (error) {
      this.logger.error('Failed to generate diagram placeholders', {
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  private async generateImageArtifacts(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    __uniqueRequestId: string,
    imageMapping: Map<string, string>,
  ): Promise<ImageArtifact[]> {
    try {
      if (imageMapping.size === 0) {
        return [];
      }

      const images: ImageArtifact[] = [];

      // Generate images based on the extracted prompt mappings
      for (const [placeholderId, promptPlaceholder] of imageMapping.entries()) {
        this.logger.debug(
          `Generating image for placeholder: ${placeholderId} with prompt: ${promptPlaceholder}`,
        );

        // Generate single image with correct placeholder format
        const image = await this.imageGenerationService.generateImage(
          sectionHeading,
          promptPlaceholder,
          content,
          gradeLevel,
        );

        if (image) {
          // Override the placeholder to match content format ({{IMAGES_1}} instead of {{IMAGE_1}})
          image.placeholder = `{{${placeholderId}}}`;
          images.push(image);
        }
      }

      return images;
    } catch (error) {
      this.logger.error('Failed to generate image placeholders', {
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  private async generateCitationPlaceholders(
    sectionHeading: string,
    __content: string,
    gradeLevel: string,
    _uniqueRequestId: string,
    citationMapping: Map<string, string>,
  ): Promise<CitationPlaceholder | null> {
    try {
      if (citationMapping.size === 0) {
        return null;
      }

      // Use the first citation mapping entry (typically there's only one)
      const firstEntry = citationMapping.entries().next().value;
      if (!firstEntry) {
        return null;
      }
      const [placeholderId, promptPlaceholder] = firstEntry;

      this.logger.debug(
        `Generating citations for placeholder: ${placeholderId} with prompt: ${promptPlaceholder}`,
      );

      const citation = await this.citationGenerationService.generateCitations(
        sectionHeading,
        promptPlaceholder,
        gradeLevel,
        'APA',
      );

      if (citation) {
        // Override the placeholder to match content format ({{CITATIONS_1}} instead of {{CITATION_1}})
        citation.placeholder = `{{${placeholderId}}}`;
      }

      return citation;
    } catch (error) {
      this.logger.error('Failed to generate citation placeholders', {
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  async listContent(
    teamId: string,
    query: ContentGenerationListQueryDto,
  ): Promise<ContentGenerationListResponseDto> {
    // Filter content by team
    const teamContent = Array.from(this.generatedContent.entries())
      .filter(([key]) => key.startsWith(teamId))
      .map(([_, content]) => content);

    // Apply filters
    let filteredContent = teamContent;

    if (query.search) {
      const searchLower = query.search.toLowerCase();
      filteredContent = filteredContent.filter((content) =>
        content.content.toLowerCase().includes(searchLower),
      );
    }

    if (query.format) {
      filteredContent = filteredContent.filter((content) => content.format === query.format);
    }

    // Apply pagination
    const total = filteredContent.length;
    const offset = query.offset || 0;
    const limit = query.limit || 20;
    const paginatedContent = filteredContent.slice(offset, offset + limit);

    return {
      items: paginatedContent,
      total,
      limit,
      offset,
      hasMore: offset + limit < total,
    };
  }

  private buildSystemPrompt(dto: GenerateContentDto, context: string): string {
    let systemPrompt = `You are an expert content creator specializing in ${dto.category} content.

    Your task is to generate high-quality ${dto.category} content based on the following specifications:

    Category: ${dto.category}
    Content Name: ${dto.snippetName}
    Description: ${dto.snippetDescription}
    ${dto.grade ? `Grade Level: ${dto.grade}` : ''}

    Output Requirements:
    - Length: ${dto.outputSettings.length}
    - Format: ${dto.outputSettings.format}
    - Language: ${dto.outputSettings.language}`;

    // Add custom properties if provided
    if (dto.customProperties && dto.customProperties.length > 0) {
      systemPrompt += `Additional Requirements:\n`;
      dto.customProperties.forEach((prop) => {
        systemPrompt += `- ${prop.key}: ${prop.value}\n`;
      });
      systemPrompt += '\n';
    }

    // Add context if available
    if (context) {
      systemPrompt += `Available Context you should use to generate the content delimited by <context> and </context>:\n<context>${context}</context>\n`;
    }

    if (dto.userInstructions) {
      systemPrompt += `\nMust follow User Instructions: ${dto.userInstructions}`;
    }

    // Add visual element requirements
    systemPrompt += `\n Artifacts Instructions:
    After carefully analyzing the Content Name, Description and User Instructions if you are confident that you need to generate any of the following artifacts:
    - Images
    - Graphs
    - Charts
    - Diagrams
    - Citations

    Then you should:
    1. Add a placeholder like {{IMAGES_1}}, {{GRAPHS_1}}, {{CHARTS_1}}, {{DIAGRAMS_1}}, {{CITATIONS_1}} inside the content at appropriate places
    2. At the end of your response, provide detailed prompts for each artifact using XML-like tags:
       - <IMAGE_1_PROMPT>Detailed description of what image should show</IMAGE_1_PROMPT>
       - <GRAPH_1_PROMPT>Detailed description of what graph should display</GRAPH_1_PROMPT>
       - <CHART_1_PROMPT>Detailed description of what chart should show</CHART_1_PROMPT>
       - <DIAGRAM_1_PROMPT>Detailed description of what diagram should illustrate</DIAGRAM_1_PROMPT>
       - <CITATION_1_PROMPT>Detailed description of what should be cited</CITATION_1_PROMPT>

    Example format:
    Your main content with {{IMAGES_1}} placeholder here...

    <IMAGE_1_PROMPT>Generate a detailed scientific illustration showing the process of photosynthesis in a plant cell, including chloroplasts, sunlight rays, CO2 molecules entering, and O2 molecules being released. The image should be educational and suitable for [grade level] students.</IMAGE_1_PROMPT>

    Most content may need only one type of artifact for a single time.
    But some may not need any artifacts at all.
    And some may need to use multiple types of artifacts.`;

    systemPrompt += '\n\n';

    // Format-specific instructions
    let formatInstructions = '';
    switch (dto.outputSettings.format) {
      case ContentFormat.TEXT:
        formatInstructions =
          'Format the content as structured text with clear headings, paragraphs, and bullet points where appropriate.';
        break;
      case ContentFormat.SLIDE:
        formatInstructions =
          'Format the content as presentation slides with clear titles, bullet points, and slide breaks indicated by "--- SLIDE BREAK ---".';
        break;
      case ContentFormat.HTML_CANVAS:
        formatInstructions =
          'Format the content as HTML with canvas elements for interactive components. Include HTML structure and canvas JavaScript code.';
        break;
      case ContentFormat.WEBGL:
        formatInstructions =
          'Format the content with WebGL components for 3D visualizations. Include WebGL shader code and 3D scene descriptions.';
        break;
      case ContentFormat.LATEX:
        formatInstructions = `Format the content in clean, properly structured LaTeX markup. Requirements:
- Use proper LaTeX document structure with \\documentclass, \\begin{document}, \\end{document}
- Use appropriate sectioning commands: \\section{}, \\subsection{}, \\subsubsection{}
- For mathematics: Use \\[ \\] for display equations, \\( \\) for inline math
- Use proper LaTeX environments: itemize, enumerate, align, equation, etc.
- Escape special characters properly: \\$, \\%, \\&, \\#, \\_, \\{, \\}
- Use \\textbf{} for bold, \\textit{} for italics, \\emph{} for emphasis
- For code: use \\texttt{} or verbatim environment
- Ensure all LaTeX syntax is valid and compilable
- DO NOT use excessive line breaks or \\n characters - let LaTeX handle formatting
- Structure content with proper paragraphs using double line breaks`;
        break;
    }

    systemPrompt += `Format Instructions: ${formatInstructions}\n\n`;

    systemPrompt += `Please generate comprehensive, age-appropriate ${dto.category} content that:
1. Follows ${dto.category} best practices
2. Includes clear explanations and examples
3. Engages with interactive elements where appropriate
4. Provides practical applications of the concepts
5. Use ${dto.outputSettings.language} language
6. ${dto.grade ? `Is age appropriate and suitable for ${dto.grade} students` : ''}

Remember to maintain ${dto.category} integrity and ensure all content is accurate and pedagogically sound.`;

    return systemPrompt;
  }

  private analyzeGeneratedContent(content: string): {
    hasImages: boolean;
    hasGraphs: boolean;
    hasCharts: boolean;
    hasDiagrams: boolean;
    hasCitations: boolean;
  } {
    return {
      hasImages: /{{IMAGES_[\w\d]+}}/i.test(content),
      hasGraphs: /{{GRAPHS_[\w\d]+}}/i.test(content),
      hasCharts: /{{CHARTS_[\w\d]+}}/i.test(content),
      hasDiagrams: /{{DIAGRAMS_[\w\d]+}}/i.test(content),
      hasCitations: /{{CITATIONS_[\w\d]+}}/i.test(content),
    };
  }

  private extractArtifactPlaceholdersMapping(content: string): {
    mapping: {
      images: Map<string, string>;
      graphs: Map<string, string>;
      charts: Map<string, string>;
      diagrams: Map<string, string>;
      citations: Map<string, string>;
    };
    cleanedContent: string;
  } {
    const mapping = {
      images: new Map<string, string>(),
      graphs: new Map<string, string>(),
      charts: new Map<string, string>(),
      diagrams: new Map<string, string>(),
      citations: new Map<string, string>(),
    };

    let cleanedContent = content;

    // Extract IMAGE prompts - extract content between <IMAGE_X_PROMPT></IMAGE_X_PROMPT> tags
    const imagePromptMatches = content.matchAll(/<IMAGE_(\w+)_PROMPT>(.*?)<\/IMAGE_\1_PROMPT>/gis);
    for (const match of imagePromptMatches) {
      const id = match[1];
      const promptContent = match[2]?.trim() || '';
      const fullMatch = match[0];
      if (id && promptContent) {
        mapping.images.set(`IMAGES_${id}`, promptContent);
        cleanedContent = cleanedContent.replace(fullMatch, '');
      }
    }

    // Extract GRAPH prompts - extract content between <GRAPH_X_PROMPT></GRAPH_X_PROMPT> tags
    const graphPromptMatches = content.matchAll(/<GRAPH_(\w+)_PROMPT>(.*?)<\/GRAPH_\1_PROMPT>/gis);
    for (const match of graphPromptMatches) {
      const id = match[1];
      const promptContent = match[2]?.trim() || '';
      const fullMatch = match[0];
      if (id && promptContent) {
        mapping.graphs.set(`GRAPHS_${id}`, promptContent);
        cleanedContent = cleanedContent.replace(fullMatch, '');
      }
    }

    // Extract CHART prompts - extract content between <CHART_X_PROMPT></CHART_X_PROMPT> tags
    const chartPromptMatches = content.matchAll(/<CHART_(\w+)_PROMPT>(.*?)<\/CHART_\1_PROMPT>/gis);
    for (const match of chartPromptMatches) {
      const id = match[1];
      const promptContent = match[2]?.trim() || '';
      const fullMatch = match[0];
      if (id && promptContent) {
        mapping.charts.set(`CHARTS_${id}`, promptContent);
        cleanedContent = cleanedContent.replace(fullMatch, '');
      }
    }

    // Extract DIAGRAM prompts - extract content between <DIAGRAM_X_PROMPT></DIAGRAM_X_PROMPT> tags
    const diagramPromptMatches = content.matchAll(
      /<DIAGRAM_(\w+)_PROMPT>(.*?)<\/DIAGRAM_\1_PROMPT>/gis,
    );
    for (const match of diagramPromptMatches) {
      const id = match[1];
      const promptContent = match[2]?.trim() || '';
      const fullMatch = match[0];
      if (id && promptContent) {
        mapping.diagrams.set(`DIAGRAMS_${id}`, promptContent);
        cleanedContent = cleanedContent.replace(fullMatch, '');
      }
    }

    // Extract CITATION prompts - extract content between <CITATION_X_PROMPT></CITATION_X_PROMPT> tags
    const citationPromptMatches = content.matchAll(
      /<CITATION_(\w+)_PROMPT>(.*?)<\/CITATION_\1_PROMPT>/gis,
    );
    for (const match of citationPromptMatches) {
      const id = match[1];
      const promptContent = match[2]?.trim() || '';
      const fullMatch = match[0];
      if (id && promptContent) {
        mapping.citations.set(`CITATIONS_${id}`, promptContent);
        cleanedContent = cleanedContent.replace(fullMatch, '');
      }
    }

    this.logger.debug('Extracted artifact placeholders mapping:', {
      images: Array.from(mapping.images.entries()),
      graphs: Array.from(mapping.graphs.entries()),
      charts: Array.from(mapping.charts.entries()),
      diagrams: Array.from(mapping.diagrams.entries()),
      citations: Array.from(mapping.citations.entries()),
    });

    return { mapping, cleanedContent };
  }

  private getOutputFormatForReplacement(contentFormat: ContentFormat): 'markdown' | 'html' {
    switch (contentFormat) {
      case ContentFormat.HTML_CANVAS:
      case ContentFormat.WEBGL:
        return 'html';
      case ContentFormat.TEXT:
      case ContentFormat.SLIDE:
      case ContentFormat.LATEX:
      default:
        return 'markdown';
    }
  }

  private validatePlaceholders(placeholders: {
    charts: ChartArtifact[];
    graphs: GraphArtifact[];
    diagrams: DiagramArtifact[];
    images: ImageArtifact[];
    citations: CitationPlaceholder | null;
  }): void {
    const allPlaceholders = [
      ...placeholders.charts,
      ...placeholders.graphs,
      ...placeholders.diagrams,
      ...placeholders.images,
    ];

    // Track image URLs for duplication detection
    const imageUrlMap = new Map<string, string[]>();
    const missingUrls: string[] = [];
    const duplicateUrls: string[] = [];

    // Validate each placeholder
    allPlaceholders.forEach((placeholder) => {
      if (!placeholder.imageUrl) {
        missingUrls.push(placeholder.placeholder);
      } else {
        if (imageUrlMap.has(placeholder.imageUrl)) {
          imageUrlMap.get(placeholder.imageUrl)!.push(placeholder.placeholder);
          if (!duplicateUrls.includes(placeholder.imageUrl)) {
            duplicateUrls.push(placeholder.imageUrl);
          }
        } else {
          imageUrlMap.set(placeholder.imageUrl, [placeholder.placeholder]);
        }
      }
    });

    // Log validation results
    const totalPlaceholders = allPlaceholders.length + (placeholders.citations ? 1 : 0);
    const validPlaceholders = allPlaceholders.filter((p) => p.imageUrl).length;

    this.logger.log(`Placeholder validation summary:`, {
      total: totalPlaceholders,
      valid: validPlaceholders,
      missingUrls: missingUrls.length,
      duplicateUrls: duplicateUrls.length,
    });

    // Warn about missing URLs
    if (missingUrls.length > 0) {
      this.logger.warn(`Placeholders missing image URLs:`, missingUrls);
    }

    // Warn about duplicate URLs
    if (duplicateUrls.length > 0) {
      this.logger.warn(`Duplicate image URLs detected:`, {
        duplicates: duplicateUrls.map((url) => ({
          url,
          placeholders: imageUrlMap.get(url),
        })),
      });
    }

    // Validate citation placeholder
    if (placeholders.citations) {
      if (!placeholders.citations.citations || placeholders.citations.citations.length === 0) {
        this.logger.warn(
          `Citation placeholder ${placeholders.citations.placeholder} has no citations`,
        );
      } else {
        this.logger.debug(
          `Citation placeholder validated: ${placeholders.citations.citations.length} citations`,
        );
      }
    }

    // Log detailed placeholder information
    this.logger.debug('Detailed placeholder information:', {
      charts: placeholders.charts.map((p) => ({
        placeholder: p.placeholder,
        hasUrl: !!p.imageUrl,
        title: p.title,
      })),
      graphs: placeholders.graphs.map((p) => ({
        placeholder: p.placeholder,
        hasUrl: !!p.imageUrl,
        title: p.title,
      })),
      diagrams: placeholders.diagrams.map((p) => ({
        placeholder: p.placeholder,
        hasUrl: !!p.imageUrl,
        title: p.title,
      })),
      images: placeholders.images.map((p) => ({
        placeholder: p.placeholder,
        hasUrl: !!p.imageUrl,
        title: p.title,
      })),
      citations: placeholders.citations
        ? {
            placeholder: placeholders.citations.placeholder,
            citationCount: placeholders.citations.citations?.length || 0,
          }
        : null,
    });
  }
}
