import { S3Service } from '@/common/services/s3.service';
import { Injectable, Logger } from '@nestjs/common';
import pRetry from 'p-retry';
import * as puppeteer from 'puppeteer';
import { AiSdk } from '../../../llm/sdk/AiSdk';

export interface GraphArtifact {
  id: string;
  type: 'graph';
  title: string;
  description: string;
  graphType: string;
  equation?: string;
  data?: any;
  cost: number;
  placeholder: string; // e.g., "{{GRAPH_1}}"
  imageUrl?: string; // S3 URL of the generated graph image
  s3Key?: string; // S3 key for the image
}

@Injectable()
export class GraphGenerationService {
  private readonly logger = new Logger(GraphGenerationService.name);
  private browser: puppeteer.Browser | null = null;
  private graphCounter = 0;

  constructor(
    private readonly aiSdk: AiSdk,
    private readonly s3Service: S3Service,
  ) {}

  private async getBrowser(): Promise<puppeteer.Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
        ],
      });
    }
    return this.browser;
  }

  async generateGraph(
    sectionHeading: string,
    graphDescription: string,
    content: string,
    gradeLevel: string,
  ): Promise<GraphArtifact> {
    this.graphCounter++;
    const graphId = `graph_${Date.now()}_${this.graphCounter}`;
    const placeholder = `{{GRAPH_${this.graphCounter}}}`;

    this.logger.log(`Generating graph: ${graphId}`);
    this.logger.debug(`Section: ${sectionHeading}`);
    this.logger.debug(`Description: ${graphDescription}`);

    try {
      // Generate Chart.js graph configuration with retry mechanism
      const result = await pRetry(
        async () => {
          const graphResult = await this.generateChartjsGraph(
            sectionHeading,
            graphDescription,
            content,
            gradeLevel,
          );

          if (!graphResult.htmlCode?.trim()) {
            throw new Error('Generated empty Chart.js HTML');
          }

          return graphResult;
        },
        {
          retries: 3,
          minTimeout: 1000,
          maxTimeout: 3000,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Graph generation attempt ${error.attemptNumber} failed: ${error.message}`,
            );
          },
        },
      );

      // Render to image with retry mechanism
      const imageBuffer = await pRetry(
        async () => {
          const buffer = await this.renderGraphToPNG(result.htmlCode);

          if (!buffer || buffer.length === 0) {
            throw new Error('Generated empty image buffer');
          }

          return buffer;
        },
        {
          retries: 2,
          minTimeout: 2000,
          maxTimeout: 5000,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Graph rendering attempt ${error.attemptNumber} failed: ${error.message}`,
            );
          },
        },
      );

      // Upload to S3 only if we have a valid image
      const s3Key = this.generateS3Key(graphId, 'graph');
      const imageUrl = await this.s3Service.uploadFile(s3Key, imageBuffer, 'image/png', {
        type: 'graph',
        graphId,
        sectionHeading: this.sanitizeMetadata(sectionHeading),
        gradeLevel: this.sanitizeMetadata(gradeLevel),
        description: this.sanitizeMetadata(graphDescription),
      });

      const GraphArtifact: GraphArtifact = {
        id: graphId,
        type: 'graph',
        title: sectionHeading,
        description: graphDescription,
        graphType: 'mathematical',
        equation: result.equation,
        cost: result.cost,
        placeholder,
        imageUrl,
        s3Key,
      };

      this.logger.log(`Successfully generated graph: ${graphId}`);
      return GraphArtifact;
    } catch (error) {
      this.logger.error(`Failed to generate graph after retries: ${graphId}`, {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
        graphDescription,
      });

      // Return empty placeholder - DO NOT upload to S3
      return this.createEmptyPlaceholder(graphId, placeholder, sectionHeading, graphDescription);
    }
  }

  private async generateChartjsGraph(
    sectionHeading: string,
    graphDescription: string,
    content: string,
    gradeLevel: string,
  ): Promise<{ htmlCode: string; equation?: string; cost: number }> {
    const systemPrompt = `
You are a mathematical graph specialist using Chart.js. Create clear, educational mathematical graphs for ${gradeLevel} students.

IMPORTANT GUIDELINES:
1. Always use Chart.js v4.x only - no other libraries
2. Generate complete HTML with Chart.js CDN
3. Use canvas element with width="800" height="600"
4. Focus on mathematical functions and concepts
5. Always include proper axis labels, grid lines, and scales
6. Use scatter or line charts for mathematical functions
7. Generate data points for smooth curves (at least 50-100 points for functions)
8. Include responsive: true and maintainAspectRatio: false
9. Use proper mathematical formatting for labels

Required color palette:
Primary: '#00E2B1', Secondary: '#54D3DA', Accent: '#41B8DD'

Common mathematical graphs to create:
- Linear functions: y = mx + b
- Quadratic functions: y = ax² + bx + c
- Exponential functions: y = a^x
- Trigonometric functions: sin, cos, tan
- Logarithmic functions
- Polynomial functions
- Rational functions

Structure your HTML like this:
<!DOCTYPE html>
<html>
<head>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4"></script>
</head>
<body style="margin: 0; padding: 20px; background: white;">
  <canvas id="graph" width="800" height="600"></canvas>
  <script>
    // Your Chart.js mathematical graph code here
    // Include proper scales, grid lines, and mathematical formatting
  </script>
</body>
</html>
`;

    const prompt = `
Section: ${sectionHeading}
Graph Description: ${graphDescription}
Context: ${content.substring(0, 400)}
Grade Level: ${gradeLevel}

Create a mathematical graph using Chart.js that:
1. Illustrates the mathematical concept clearly
2. Has proper axis labels and scales
3. Shows grid lines for readability
4. Uses appropriate domain and range
5. Is educational for ${gradeLevel} students
6. Has smooth curves with enough data points

If the description mentions specific equations or functions, implement them accurately.
Common examples: linear equations, parabolas, sine/cosine waves, exponential curves.

Return ONLY the complete HTML code with Chart.js, no explanations.
`;

    try {
      const result = await this.aiSdk.generateText({
        provider: 'anthropic',
        model: 'claude-sonnet-4-20250514',
        prompt,
        systemPrompt,
        identifierName: 'GraphGenerationService_MathGraph',
        identifierValue: graphDescription,
      });

      let htmlCode = result.text.trim();

      // Clean up the response - remove markdown code blocks if present
      if (htmlCode.startsWith('```html')) {
        htmlCode = htmlCode
          .replace(/```html\n?/, '')
          .replace(/```$/, '')
          .trim();
      } else if (htmlCode.startsWith('```')) {
        htmlCode = htmlCode
          .replace(/```\n?/, '')
          .replace(/```$/, '')
          .trim();
      }

      if (!htmlCode || !htmlCode.includes('Chart.js') || !htmlCode.includes('<canvas')) {
        throw new Error('Generated invalid Chart.js HTML');
      }

      // Try to extract equation from the description or code
      const equation = this.extractEquation(graphDescription, htmlCode);

      const cost = 0; // Calculate based on usage if needed

      this.logger.debug(`Generated Chart.js graph for ${sectionHeading}:`, {
        codeLength: htmlCode.length,
        hasCanvas: htmlCode.includes('<canvas'),
        hasChart: htmlCode.includes('Chart.js'),
        equation,
      });

      return { htmlCode, equation, cost };
    } catch (error) {
      this.logger.error('Failed to generate Chart.js graph', {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
        graphDescription,
      });
      throw error;
    }
  }

  private extractEquation(description: string, htmlCode: string): string | undefined {
    // Try to extract mathematical equations from description or code
    const equationPatterns = [
      /y\s*=\s*[^,\n]+/gi,
      /f\(x\)\s*=\s*[^,\n]+/gi,
      /\w+\(x\)\s*=\s*[^,\n]+/gi,
    ];

    for (const pattern of equationPatterns) {
      const match = description.match(pattern) || htmlCode.match(pattern);
      if (match) {
        return match[0].trim();
      }
    }

    return undefined;
  }

  private async renderGraphToPNG(htmlCode: string): Promise<Buffer> {
    const browser = await this.getBrowser();
    const page = await browser.newPage();

    try {
      // Set viewport for graphs
      await page.setViewport({ width: 900, height: 700 });

      // Set content
      await page.setContent(htmlCode, {
        waitUntil: 'networkidle0',
      });

      // Wait for graph to render (mathematical graphs may take longer)
      await new Promise((resolve) => setTimeout(resolve, 4000));

      // Find the canvas element and take screenshot
      const canvasElement = await page.$('canvas');
      if (!canvasElement) {
        throw new Error('No canvas element found in generated HTML');
      }

      const screenshot = await canvasElement.screenshot({
        type: 'png',
      });

      return Buffer.from(screenshot);
    } finally {
      await page.close();
    }
  }

  private generateS3Key(graphId: string, type: string): string {
    // Add more entropy to ensure uniqueness
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const timestamp = Date.now();
    return `generated-content/${type}/${timestamp}-${graphId}-${randomSuffix}.png`;
  }

  private createEmptyPlaceholder(
    id: string,
    placeholder: string,
    title: string,
    description: string,
  ): GraphArtifact {
    return {
      id,
      type: 'graph',
      title,
      description,
      graphType: 'mathematical',
      cost: 0,
      placeholder,
      // No imageUrl or s3Key - will be handled by placeholder replacement
    };
  }

  async generateMultipleGraphs(
    sectionHeading: string,
    graphDescriptions: string[],
    content: string,
    gradeLevel: string,
  ): Promise<GraphArtifact[]> {
    const graphs: GraphArtifact[] = [];

    for (const description of graphDescriptions) {
      try {
        const graph = await this.generateGraph(sectionHeading, description, content, gradeLevel);
        graphs.push(graph);
      } catch (error) {
        this.logger.error(`Failed to generate graph for: ${description}`, {
          error: error instanceof Error ? error.message : String(error),
        });
        // Continue with other graphs
      }
    }

    return graphs;
  }

  async onModuleDestroy() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  private sanitizeMetadata(value: string): string {
    if (!value) return '';
    return value
      .replace(/[\r\n\t]/g, ' ')
      .replace(/[^\x20-\x7E]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 255);
  }
}
