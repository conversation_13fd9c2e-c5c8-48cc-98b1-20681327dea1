import { Injectable, Logger } from '@nestjs/common';
import { ImageArtifact } from '../../../img/services';
import { ChartArtifact, CitationPlaceholder, DiagramArtifact, GraphArtifact } from './';

export interface PlaceholderReplacementResult {
  content: string;
  replacedCount: number;
  errors: string[];
}

@Injectable()
export class PlaceholderReplacementService {
  private readonly logger = new Logger(PlaceholderReplacementService.name);

  constructor() {}

  async replacePlaceholdersWithImages(
    content: string,
    placeholders: {
      charts: ChartArtifact[];
      graphs: GraphArtifact[];
      diagrams: DiagramArtifact[];
      images: ImageArtifact[];
      citations: CitationPlaceholder | null;
    },
    format: 'markdown' | 'html' = 'markdown',
  ): Promise<PlaceholderReplacementResult> {
    try {
      this.logger.debug('Starting placeholder replacement');

      let updatedContent = content;
      let replacedCount = 0;
      const errors: string[] = [];
      const processedPlaceholders = new Set<string>(); // Track processed placeholders

      // Replace chart placeholders
      for (const chart of placeholders.charts) {
        try {
          // Skip if already processed (prevents duplicates)
          if (processedPlaceholders.has(chart.placeholder)) {
            this.logger.debug(`Skipping duplicate chart placeholder: ${chart.placeholder}`);
            continue;
          }

          const replacement = this.createImageReplacement(chart, format);
          const placeholderRegex = new RegExp(this.escapeRegex(chart.placeholder), 'g');
          const matches = updatedContent.match(placeholderRegex);

          if (matches && matches.length > 0) {
            updatedContent = updatedContent.replace(placeholderRegex, replacement);
            replacedCount += matches.length;
            processedPlaceholders.add(chart.placeholder);
            this.logger.debug(
              `Replaced chart placeholder: ${chart.placeholder} (${matches.length} times)`,
            );
          }
        } catch (error) {
          const errorMsg = `Failed to replace chart ${chart.placeholder}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // Replace graph placeholders
      for (const graph of placeholders.graphs) {
        try {
          // Skip if already processed (prevents duplicates)
          if (processedPlaceholders.has(graph.placeholder)) {
            this.logger.debug(`Skipping duplicate graph placeholder: ${graph.placeholder}`);
            continue;
          }

          const replacement = this.createImageReplacement(graph, format);
          const placeholderRegex = new RegExp(this.escapeRegex(graph.placeholder), 'g');
          const matches = updatedContent.match(placeholderRegex);

          if (matches && matches.length > 0) {
            updatedContent = updatedContent.replace(placeholderRegex, replacement);
            replacedCount += matches.length;
            processedPlaceholders.add(graph.placeholder);
            this.logger.debug(
              `Replaced graph placeholder: ${graph.placeholder} (${matches.length} times)`,
            );
          }
        } catch (error) {
          const errorMsg = `Failed to replace graph ${graph.placeholder}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // Replace diagram placeholders
      for (const diagram of placeholders.diagrams) {
        try {
          // Skip if already processed (prevents duplicates)
          if (processedPlaceholders.has(diagram.placeholder)) {
            this.logger.debug(`Skipping duplicate diagram placeholder: ${diagram.placeholder}`);
            continue;
          }

          const replacement = this.createImageReplacement(diagram, format);
          const placeholderRegex = new RegExp(this.escapeRegex(diagram.placeholder), 'g');
          const matches = updatedContent.match(placeholderRegex);

          if (matches && matches.length > 0) {
            updatedContent = updatedContent.replace(placeholderRegex, replacement);
            replacedCount += matches.length;
            processedPlaceholders.add(diagram.placeholder);
            this.logger.debug(
              `Replaced diagram placeholder: ${diagram.placeholder} (${matches.length} times)`,
            );
          }
        } catch (error) {
          const errorMsg = `Failed to replace diagram ${diagram.placeholder}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // Replace image placeholders
      for (const image of placeholders.images) {
        try {
          // Skip if already processed (prevents duplicates)
          if (processedPlaceholders.has(image.placeholder)) {
            this.logger.debug(`Skipping duplicate image placeholder: ${image.placeholder}`);
            continue;
          }

          const replacement = this.createImageReplacement(image, format);
          const placeholderRegex = new RegExp(this.escapeRegex(image.placeholder), 'g');
          const matches = updatedContent.match(placeholderRegex);

          if (matches && matches.length > 0) {
            updatedContent = updatedContent.replace(placeholderRegex, replacement);
            replacedCount += matches.length;
            processedPlaceholders.add(image.placeholder);
            this.logger.debug(
              `Replaced image placeholder: ${image.placeholder} (${matches.length} times)`,
            );
          }
        } catch (error) {
          const errorMsg = `Failed to replace image ${image.placeholder}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // Replace citation placeholder
      if (placeholders.citations) {
        try {
          // Skip if already processed (prevents duplicates)
          if (!processedPlaceholders.has(placeholders.citations.placeholder)) {
            const replacement = this.createCitationReplacement(placeholders.citations, format);
            const placeholderRegex = new RegExp(
              this.escapeRegex(placeholders.citations.placeholder),
              'g',
            );
            const matches = updatedContent.match(placeholderRegex);

            if (matches && matches.length > 0) {
              updatedContent = updatedContent.replace(placeholderRegex, replacement);
              replacedCount += matches.length;
              processedPlaceholders.add(placeholders.citations.placeholder);
              this.logger.debug(
                `Replaced citation placeholder: ${placeholders.citations.placeholder} (${matches.length} times)`,
              );
            }
          }
        } catch (error) {
          const errorMsg = `Failed to replace citation ${placeholders.citations.placeholder}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // Clean up any remaining generic placeholders
      const cleanupResult = this.cleanupGenericPlaceholders(updatedContent, format);
      if (cleanupResult.count > 0) {
        updatedContent = cleanupResult.content;
        replacedCount += cleanupResult.count;
        this.logger.debug(`Cleaned up ${cleanupResult.count} generic placeholders`);
      }

      this.logger.log(
        `Placeholder replacement completed. Replaced: ${replacedCount}, Errors: ${errors.length}`,
      );

      return {
        content: updatedContent,
        replacedCount,
        errors,
      };
    } catch (error) {
      this.logger.error('Failed to replace placeholders', {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        content,
        replacedCount: 0,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  private createImageReplacement(
    placeholder: ChartArtifact | GraphArtifact | DiagramArtifact | ImageArtifact,
    format: 'markdown' | 'html',
  ): string {
    // Log placeholder info for debugging
    this.logger.debug(`Creating replacement for ${placeholder.placeholder}:`, {
      type: placeholder.type,
      title: placeholder.title,
      hasImageUrl: !!placeholder.imageUrl,
      imageUrl: placeholder.imageUrl || 'missing',
    });

    // Check for valid image URL
    if (
      !placeholder.imageUrl ||
      placeholder.imageUrl === 'undefined' ||
      placeholder.imageUrl === '' ||
      placeholder.imageUrl === 'null'
    ) {
      // If no valid image URL, return empty string to completely hide the placeholder
      this.logger.warn(
        `Missing or invalid imageUrl for placeholder ${placeholder.placeholder} - hiding completely`,
        {
          type: placeholder.type,
          title: placeholder.title,
          description: placeholder.description,
          imageUrl: placeholder.imageUrl,
        },
      );

      return ''; // Return empty string to completely remove the placeholder
    }

    // Validate URL format
    try {
      new URL(placeholder.imageUrl);
    } catch (error) {
      this.logger.warn(
        `Invalid URL format for placeholder ${placeholder.placeholder} - hiding completely`,
        {
          imageUrl: placeholder.imageUrl,
          error: error instanceof Error ? error.message : String(error),
        },
      );
      return '';
    }

    if (format === 'markdown') {
      return `![${placeholder.title}](${placeholder.imageUrl})`;
    } else {
      return `<img src="${placeholder.imageUrl}" alt="${placeholder.title}" title="${placeholder.description}" style="max-width: 100%; height: auto;" />`;
    }
  }

  private createCitationReplacement(
    citation: CitationPlaceholder,
    format: 'markdown' | 'html',
  ): string {
    if (!citation.citations || citation.citations.length === 0) {
      return format === 'markdown'
        ? '\n## References\n*No citations available*\n'
        : '\n<h2>References</h2>\n<p><em>No citations available</em></p>\n';
    }

    const citationList = citation.citations
      .map((cite, index) => {
        if (format === 'markdown') {
          return `${index + 1}. ${cite}`;
        } else {
          return `<li>${cite}</li>`;
        }
      })
      .join('\n');

    if (format === 'markdown') {
      return `\n## References\n\n${citationList}\n`;
    } else {
      return `\n<h2>References</h2>\n<ol>\n${citationList}\n</ol>\n`;
    }
  }

  /**
   * Clean up any remaining generic placeholders that weren't processed
   * This is a fallback for placeholders like {{SOMETHING_NUMBER}} that might be leftover
   */
  private cleanupGenericPlaceholders(
    content: string,
    format: 'markdown' | 'html',
  ): { content: string; count: number } {
    let updatedContent = content;
    let count = 0;

    // Find all placeholder patterns like {{SOMETHING_NUMBER}}
    const placeholderPattern = /\{\{([A-Z_]+)_(\d+)\}\}/g;
    const matches = [...content.matchAll(placeholderPattern)];

    this.logger.debug(`Found ${matches.length} generic placeholders to clean up`);

    for (const match of matches) {
      const [fullMatch, type, number] = match;

      this.logger.debug(`Cleaning up generic placeholder: ${fullMatch} (${type}_${number})`);

      // For any remaining visual placeholders, remove them completely
      switch (type) {
        case 'IMAGE':
        case 'DIAGRAM':
        case 'GRAPH':
        case 'CHART':
          // Remove completely - these should have been processed by now
          updatedContent = updatedContent.replace(new RegExp(this.escapeRegex(fullMatch), 'g'), '');
          count++;
          this.logger.debug(`Removed unprocessed ${type.toLowerCase()} placeholder: ${fullMatch}`);
          break;
        case 'CITATION':
          // Provide a fallback citation section
          const citationFallback =
            format === 'markdown'
              ? '\n## References\n*Citations are being processed*\n'
              : '\n<h2>References</h2>\n<p><em>Citations are being processed</em></p>\n';

          updatedContent = updatedContent.replace(
            new RegExp(this.escapeRegex(fullMatch), 'g'),
            citationFallback,
          );
          count++;
          this.logger.debug(`Replaced citation placeholder: ${fullMatch}`);
          break;
        default:
          // For unknown placeholders, remove them
          updatedContent = updatedContent.replace(new RegExp(this.escapeRegex(fullMatch), 'g'), '');
          count++;
          this.logger.debug(`Removed unknown placeholder: ${fullMatch}`);
      }
    }

    this.logger.debug(`Completed generic placeholder cleanup. Cleaned ${count} placeholders.`);
    return { content: updatedContent, count };
  }

  /**
   * Escapes special regex characters in placeholder strings
   */
  private escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}
