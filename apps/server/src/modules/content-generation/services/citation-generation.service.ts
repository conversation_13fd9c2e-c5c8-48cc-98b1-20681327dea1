import { Injectable, Logger } from '@nestjs/common';
import { AiSdk } from '../../../llm/sdk/AiSdk';

interface Citation {
  type: 'book' | 'journal' | 'website' | 'video' | 'article' | 'academic';
  title: string;
  authors: string[];
  year: number;
  source?: string;
  url?: string;
  publisher?: string;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  accessDate?: string;
}

interface CitationDecision {
  citations: Citation[];
  bibliography: string;
  inTextCitations: string[];
}

export interface CitationPlaceholder {
  id: string;
  type: 'citation';
  title: string;
  description: string;
  format: string; // APA, MLA, Chicago, Harvard
  citations: string[];
  cost: number;
  placeholder: string; // e.g., "{{CITATION_1}}"
}

@Injectable()
export class CitationGenerationService {
  private readonly logger = new Logger(CitationGenerationService.name);
  private citationCounter = 0;

  constructor(private readonly aiSdk: AiSdk) {}

  async generateCitations(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    format: string = 'APA',
  ): Promise<CitationPlaceholder> {
    try {
      this.logger.debug(`Generating citations for section: ${sectionHeading}`);
      this.logger.debug(`Citation format: ${format}`);

      // Generate citations using AI
      const citationConfig = await this.generateCitationConfiguration(
        sectionHeading,
        content,
        gradeLevel,
        format,
      );

      // Generate unique citation ID and placeholder
      this.citationCounter++;
      const citationId = `citation_${Date.now()}_${this.citationCounter}`;
      const placeholder = `{{CITATION_${this.citationCounter}}}`;

      // Calculate cost (simplified for now)
      const cost = 0.003; // Cost for citation generation

      const citationPlaceholder: CitationPlaceholder = {
        id: citationId,
        type: 'citation',
        title: `Citations for ${sectionHeading}`,
        description: `Academic citations in ${format} format`,
        format,
        citations: citationConfig.citations || [],
        cost,
        placeholder,
      };

      this.logger.debug(`Citation placeholder created: ${placeholder}`);
      return citationPlaceholder;
    } catch (error) {
      this.logger.error('Failed to generate citation placeholder', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        sectionHeading,
        format,
      });
      return this.createEmptyPlaceholder();
    }
  }

  private async generateCitationConfiguration(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    format: string,
  ): Promise<any> {
    try {
      const contextSnippet = content.substring(0, 1000);

      const prompt = `Generate academic citations for educational content designed for ${gradeLevel} students.

Section: ${sectionHeading}
Content: ${contextSnippet}
Citation Format: ${format}

Please create appropriate academic citations that would support this educational content. Include:
1. Relevant textbooks
2. Educational websites
3. Academic papers (if appropriate for grade level)
4. Government/institutional sources

Format all citations according to ${format} style.
Return as JSON with a "citations" array containing the formatted citation strings.`;

      const result = await this.aiSdk.generateText({
        prompt,
        provider: 'openai',
        model: 'gpt-4o-mini',
        identifierName: 'CitationGenerationService',
        identifierValue: 'citation-config',
      });

      // Parse the AI response
      try {
        return JSON.parse(result.text);
      } catch (parseError) {
        this.logger.warn('Failed to parse AI citation configuration, using defaults');
        return {
          citations: [`Educational Resource. (2024). ${sectionHeading}. Educational Publisher.`],
        };
      }
    } catch (error) {
      this.logger.error('Failed to generate citation configuration', {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        citations: [`Educational Resource. (2024). ${sectionHeading}. Educational Publisher.`],
      };
    }
  }

  private createEmptyPlaceholder(): CitationPlaceholder {
    this.citationCounter++;
    return {
      id: `empty_citation_${Date.now()}`,
      type: 'citation',
      title: 'Citations',
      description: 'No citations available',
      format: 'APA',
      citations: [],
      cost: 0,
      placeholder: `{{CITATION_${this.citationCounter}}}`,
    };
  }

  async generateMultipleFormats(
    sectionHeading: string,
    content: string,
    gradeLevel: string,
    formats: string[] = ['APA', 'MLA'],
  ): Promise<CitationPlaceholder[]> {
    try {
      if (!formats || formats.length === 0) {
        return [];
      }

      const citationPromises = formats.map((format) =>
        this.generateCitations(sectionHeading, content, gradeLevel, format),
      );

      return await Promise.all(citationPromises);
    } catch (error) {
      this.logger.error('Failed to generate multiple citation formats', {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
      });
      return [];
    }
  }

  async formatCitation(
    citation: Citation,
    style: 'APA' | 'MLA' | 'Chicago' | 'Harvard' = 'APA',
  ): Promise<string> {
    try {
      switch (style) {
        case 'APA':
          return this.formatAPACitation(citation);
        case 'MLA':
          return this.formatMLACitation(citation);
        case 'Chicago':
          return this.formatChicagoCitation(citation);
        case 'Harvard':
          return this.formatHarvardCitation(citation);
        default:
          return this.formatAPACitation(citation);
      }
    } catch (error) {
      this.logger.error('Failed to format citation', {
        error: error instanceof Error ? error.message : String(error),
        citation,
        style,
      });
      return 'Citation formatting error';
    }
  }

  private formatAPACitation(citation: Citation): string {
    const authors = citation.authors.length > 0 ? citation.authors.join(', ') : 'Unknown Author';
    const year = citation.year;
    const title = citation.title;

    switch (citation.type) {
      case 'book':
        return `${authors} (${year}). ${title}. ${citation.publisher}.`;
      case 'journal':
        return `${authors} (${year}). ${title}. ${citation.journal}, ${citation.volume}(${citation.issue}), ${citation.pages}.`;
      case 'website':
        return `${authors} (${year}). ${title}. Retrieved from ${citation.url}`;
      case 'video':
        return `${authors} (${year}). ${title} [Video]. ${citation.source}.`;
      default:
        return `${authors} (${year}). ${title}.`;
    }
  }

  private formatMLACitation(citation: Citation): string {
    const authors = citation.authors.length > 0 ? citation.authors[0] : 'Unknown Author';
    const title = citation.title;
    const year = citation.year;

    switch (citation.type) {
      case 'book':
        return `${authors}. ${title}. ${citation.publisher}, ${year}.`;
      case 'journal':
        return `${authors}. "${title}." ${citation.journal}, vol. ${citation.volume}, no. ${citation.issue}, ${year}, pp. ${citation.pages}.`;
      case 'website':
        return `${authors}. "${title}." ${citation.source}, ${year}, ${citation.url}.`;
      default:
        return `${authors}. ${title}. ${year}.`;
    }
  }

  private formatChicagoCitation(citation: Citation): string {
    const authors = citation.authors.length > 0 ? citation.authors.join(', ') : 'Unknown Author';
    const title = citation.title;
    const year = citation.year;

    switch (citation.type) {
      case 'book':
        return `${authors}. ${title}. ${citation.publisher}, ${year}.`;
      case 'journal':
        return `${authors}. "${title}." ${citation.journal} ${citation.volume}, no. ${citation.issue} (${year}): ${citation.pages}.`;
      case 'website':
        return `${authors}. "${title}." ${citation.source}, ${year}. ${citation.url}.`;
      default:
        return `${authors}. ${title}. ${year}.`;
    }
  }

  private formatHarvardCitation(citation: Citation): string {
    const authors = citation.authors.length > 0 ? citation.authors.join(', ') : 'Unknown Author';
    const title = citation.title;
    const year = citation.year;

    switch (citation.type) {
      case 'book':
        return `${authors} ${year}, ${title}, ${citation.publisher}.`;
      case 'journal':
        return `${authors} ${year}, '${title}', ${citation.journal}, vol. ${citation.volume}, no. ${citation.issue}, pp. ${citation.pages}.`;
      case 'website':
        return `${authors} ${year}, ${title}, viewed ${citation.accessDate}, ${citation.url}.`;
      default:
        return `${authors} ${year}, ${title}.`;
    }
  }
}
