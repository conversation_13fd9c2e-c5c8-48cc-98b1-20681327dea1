import { S3Service } from '@/common/services/s3.service';
import { Injectable, Logger } from '@nestjs/common';
import { exec } from 'child_process';
import * as fs from 'fs/promises';
import pRetry from 'p-retry';
import * as path from 'path';
import { promisify } from 'util';
import { AiSdk } from '../../../llm/sdk/AiSdk';

const execAsync = promisify(exec);

export interface DiagramArtifact {
  id: string;
  type: 'diagram';
  title: string;
  description: string;
  diagramType: string;
  mermaidCode?: string;
  cost: number;
  placeholder: string; // e.g., "{{DIAGRAM_1}}"
  imageUrl?: string; // S3 URL of the generated diagram image
  s3Key?: string; // S3 key for the image
}

@Injectable()
export class DiagramGenerationService {
  private readonly logger = new Logger(DiagramGenerationService.name);
  private diagramCounter = 0;

  constructor(
    private readonly aiSdk: AiSdk,
    private readonly s3Service: S3Service,
  ) {}

  async generateDiagram(
    sectionHeading: string,
    diagramDescription: string,
    content: string,
    gradeLevel: string,
  ): Promise<DiagramArtifact> {
    this.diagramCounter++;
    const diagramId = `diagram_${Date.now()}_${this.diagramCounter}`;
    const placeholder = `{{DIAGRAM_${this.diagramCounter}}}`;

    this.logger.log(`Generating diagram: ${diagramId}`);
    this.logger.debug(`Section: ${sectionHeading}`);
    this.logger.debug(`Description: ${diagramDescription}`);

    try {
      // Generate Mermaid code with retry mechanism
      const result = await pRetry(
        async () => {
          const mermaidResult = await this.generateMermaidCode(
            sectionHeading,
            diagramDescription,
            content,
            gradeLevel,
          );

          if (!mermaidResult.mermaidCode?.trim()) {
            throw new Error('Generated empty Mermaid code');
          }

          return mermaidResult;
        },
        {
          retries: 3,
          minTimeout: 1000,
          maxTimeout: 3000,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Diagram generation attempt ${error.attemptNumber} failed: ${error.message}`,
            );
          },
        },
      );

      // Render to image with retry mechanism
      const imageBuffer = await pRetry(
        async () => {
          const buffer = await this.renderMermaidToPNG(result.mermaidCode);

          if (!buffer || buffer.length === 0) {
            throw new Error('Generated empty image buffer');
          }

          return buffer;
        },
        {
          retries: 2,
          minTimeout: 2000,
          maxTimeout: 5000,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Image rendering attempt ${error.attemptNumber} failed: ${error.message}`,
            );
          },
        },
      );

      // Upload to S3 only if we have a valid image
      const s3Key = this.generateS3Key(diagramId, 'diagram');
      const imageUrl = await this.s3Service.uploadFile(s3Key, imageBuffer, 'image/png', {
        type: 'diagram',
        diagramId,
        sectionHeading: this.sanitizeMetadata(sectionHeading),
        gradeLevel: this.sanitizeMetadata(gradeLevel),
        description: this.sanitizeMetadata(diagramDescription),
      });

      const DiagramArtifact: DiagramArtifact = {
        id: diagramId,
        type: 'diagram',
        title: sectionHeading,
        description: diagramDescription,
        diagramType: 'mermaid',
        mermaidCode: result.mermaidCode,
        cost: result.cost,
        placeholder,
        imageUrl,
        s3Key,
      };

      this.logger.log(`Successfully generated diagram: ${diagramId}`);
      return DiagramArtifact;
    } catch (error) {
      this.logger.error(`Failed to generate diagram after retries: ${diagramId}`, {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
        diagramDescription,
      });

      // Return empty placeholder - DO NOT upload to S3
      return this.createEmptyPlaceholder(
        diagramId,
        placeholder,
        sectionHeading,
        diagramDescription,
      );
    }
  }

  private async generateMermaidCode(
    sectionHeading: string,
    diagramDescription: string,
    content: string,
    gradeLevel: string,
  ): Promise<{ mermaidCode: string; cost: number }> {
    const systemPrompt = `
You are a Mermaid diagram specialist. Create clear, educational diagrams that fit within a 4:3 aspect ratio (800x600 pixels).

IMPORTANT GUIDELINES:
1. Always use Mermaid.js syntax only
2. Keep diagrams simple and educational for ${gradeLevel} level
3. Use appropriate diagram types: flowchart, sequenceDiagram, stateDiagram, classDiagram, erDiagram, journey, gantt, pie, gitgraph
4. For 4:3 ratio compatibility, limit:
   - Flowcharts: max 4-5 nodes per row, max 3-4 rows
   - Sequence diagrams: max 4 participants
   - State diagrams: max 6-8 states
   - Class diagrams: max 4-5 classes
5. Use clear, concise labels
6. Prefer TD (top-down) or LR (left-right) layouts for better 4:3 fit

Color palette: #D1D8DC, #00E2B1, #CCD7E6, #54D3DA, #A2ACE0, #41B8DD, #FBD0B9

Examples:
- For processes: flowchart TD
- For relationships: graph LR
- For time-based: sequenceDiagram
- For states: stateDiagram-v2
- For data: erDiagram
- For proportions: pie title "Title"
`;

    const prompt = `
Section: ${sectionHeading}
Description: ${diagramDescription}
Context: ${content.substring(0, 400)}

Create a Mermaid diagram that:
1. Fits 4:3 aspect ratio (800x600)
2. Is educational for ${gradeLevel}
3. Clearly illustrates the concept
4. Uses appropriate Mermaid syntax

Return ONLY the Mermaid code, no explanations.
`;

    try {
      const result = await this.aiSdk.generateText({
        provider: 'anthropic',
        model: 'claude-sonnet-4-20250514',
        prompt,
        systemPrompt,
        identifierName: 'DiagramGenerationService_MermaidCode',
        identifierValue: diagramDescription,
      });

      let mermaidCode = result.text.trim();

      // Clean up the response - remove markdown code blocks if present
      if (mermaidCode.startsWith('```mermaid')) {
        mermaidCode = mermaidCode
          .replace(/```mermaid\n?/, '')
          .replace(/```$/, '')
          .trim();
      } else if (mermaidCode.startsWith('```')) {
        mermaidCode = mermaidCode
          .replace(/```\n?/, '')
          .replace(/```$/, '')
          .trim();
      }

      if (!mermaidCode) {
        throw new Error('Empty Mermaid code generated');
      }

      // Basic validation - ensure it starts with a valid Mermaid keyword
      const validStarters = [
        'flowchart',
        'graph',
        'sequenceDiagram',
        'stateDiagram',
        'classDiagram',
        'erDiagram',
        'journey',
        'gantt',
        'pie',
        'gitgraph',
      ];

      const isValid = validStarters.some((starter) =>
        mermaidCode.toLowerCase().startsWith(starter.toLowerCase()),
      );

      if (!isValid) {
        this.logger.warn('Generated Mermaid code may be invalid', {
          mermaidCode: mermaidCode.substring(0, 100),
        });
      }

      const cost = 0; // Calculate based on usage if needed

      this.logger.debug(`Generated Mermaid code for ${sectionHeading}:`, {
        codeLength: mermaidCode.length,
        firstLine: mermaidCode.split('\n')[0],
      });

      return { mermaidCode, cost };
    } catch (error) {
      this.logger.error('Failed to generate Mermaid code', {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
        diagramDescription,
      });
      throw error;
    }
  }

  private async renderMermaidToPNG(mermaidCode: string): Promise<Buffer> {
    const tempDir = path.join(process.cwd(), 'tmp');
    await fs.mkdir(tempDir, { recursive: true });

    const inputFile = path.join(
      tempDir,
      `diagram-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.mmd`,
    );
    const outputFile = path.join(
      tempDir,
      `diagram-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.png`,
    );

    try {
      await fs.writeFile(inputFile, mermaidCode, 'utf8');

      // Use mermaid-cli with 4:3 aspect ratio (800x600)
      await execAsync(
        `npx mmdc -i "${inputFile}" -o "${outputFile}" -w 800 -H 600 -b white -t neutral`,
      );

      const buffer = await fs.readFile(outputFile);

      if (!buffer || buffer.length === 0) {
        throw new Error('Generated empty image buffer');
      }

      return buffer;
    } finally {
      // Clean up temp files
      await fs.rm(inputFile, { force: true });
      await fs.rm(outputFile, { force: true });
    }
  }

  private generateS3Key(diagramId: string, type: string): string {
    // Add more entropy to ensure uniqueness
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const timestamp = Date.now();
    return `generated-content/${type}/${timestamp}-${diagramId}-${randomSuffix}.png`;
  }

  private createEmptyPlaceholder(
    id: string,
    placeholder: string,
    title: string,
    description: string,
  ): DiagramArtifact {
    return {
      id,
      type: 'diagram',
      title,
      description,
      diagramType: 'mermaid',
      cost: 0,
      placeholder,
      // No imageUrl or s3Key - will be handled by placeholder replacement
    };
  }

  async generateMultipleDiagrams(
    sectionHeading: string,
    diagramDescriptions: string[],
    content: string,
    gradeLevel: string,
  ): Promise<DiagramArtifact[]> {
    const diagrams: DiagramArtifact[] = [];

    for (const description of diagramDescriptions) {
      try {
        const diagram = await this.generateDiagram(
          sectionHeading,
          description,
          content,
          gradeLevel,
        );
        diagrams.push(diagram);
      } catch (error) {
        this.logger.error(`Failed to generate diagram for: ${description}`, {
          error: error instanceof Error ? error.message : String(error),
        });
        // Continue with other diagrams
      }
    }

    return diagrams;
  }

  private sanitizeMetadata(value: string): string {
    if (!value) return '';
    return value
      .replace(/[\r\n\t]/g, ' ')
      .replace(/[^\x20-\x7E]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 255);
  }
}
