import { S3Service } from '@/common/services/s3.service';
import { Injectable, Logger } from '@nestjs/common';
import pRetry from 'p-retry';
import * as puppeteer from 'puppeteer';
import { AiSdk } from '../../../llm/sdk/AiSdk';

export interface ChartArtifact {
  id: string;
  type: 'chart';
  title: string;
  description: string;
  chartType: string;
  data: any;
  cost: number;
  placeholder: string; // e.g., "{{CHART_1}}"
  imageUrl?: string; // S3 URL of the generated chart image
  s3Key?: string; // S3 key for the image
}

@Injectable()
export class ChartGenerationService {
  private readonly logger = new Logger(ChartGenerationService.name);
  private browser: puppeteer.Browser | null = null;
  private chartCounter = 0;

  constructor(
    private readonly aiSdk: AiSdk,
    private readonly s3Service: S3Service,
  ) {}

  private async getBrowser(): Promise<puppeteer.Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
        ],
      });
    }
    return this.browser;
  }

  async generateChart(
    sectionHeading: string,
    chartData: any,
    content: string,
    gradeLevel: string,
  ): Promise<ChartArtifact> {
    this.chartCounter++;
    const chartId = `chart_${Date.now()}_${this.chartCounter}`;
    const placeholder = `{{CHART_${this.chartCounter}}}`;

    this.logger.log(`Generating chart: ${chartId}`);
    this.logger.debug(`Section: ${sectionHeading}`);
    this.logger.debug(`Data:`, { chartData });

    try {
      // Generate Chart.js configuration with retry mechanism
      const result = await pRetry(
        async () => {
          const chartResult = await this.generateChartjsConfig(
            sectionHeading,
            chartData,
            content,
            gradeLevel,
          );

          if (!chartResult.htmlCode?.trim()) {
            throw new Error('Generated empty Chart.js HTML');
          }

          return chartResult;
        },
        {
          retries: 3,
          minTimeout: 1000,
          maxTimeout: 3000,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Chart generation attempt ${error.attemptNumber} failed: ${error.message}`,
            );
          },
        },
      );

      // Render to image with retry mechanism
      const imageBuffer = await pRetry(
        async () => {
          const buffer = await this.renderChartToPNG(result.htmlCode);

          if (!buffer || buffer.length === 0) {
            throw new Error('Generated empty image buffer');
          }

          return buffer;
        },
        {
          retries: 2,
          minTimeout: 2000,
          maxTimeout: 5000,
          onFailedAttempt: (error) => {
            this.logger.warn(
              `Chart rendering attempt ${error.attemptNumber} failed: ${error.message}`,
            );
          },
        },
      );

      // Upload to S3 only if we have a valid image
      const s3Key = this.generateS3Key(chartId, 'chart');
      const imageUrl = await this.s3Service.uploadFile(s3Key, imageBuffer, 'image/png', {
        type: 'chart',
        chartId,
        sectionHeading,
        gradeLevel,
      });

      const ChartArtifact: ChartArtifact = {
        id: chartId,
        type: 'chart',
        title: sectionHeading,
        description: `Chart showing ${chartData.labels?.join(', ') || 'data'}`,
        chartType: 'chartjs',
        data: chartData,
        cost: result.cost,
        placeholder,
        imageUrl,
        s3Key,
      };

      this.logger.log(`Successfully generated chart: ${chartId}`);
      return ChartArtifact;
    } catch (error) {
      this.logger.error(`Failed to generate chart after retries: ${chartId}`, {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
        chartData,
      });

      // Return empty placeholder - DO NOT upload to S3
      return this.createEmptyPlaceholder(chartId, placeholder, sectionHeading, chartData);
    }
  }

  private async generateChartjsConfig(
    sectionHeading: string,
    chartData: any,
    content: string,
    gradeLevel: string,
  ): Promise<{ htmlCode: string; cost: number }> {
    // Determine best chart type based on data structure
    const chartType = this.determineChartType(chartData);

    const systemPrompt = `
You are a Chart.js specialist. Create clear, educational charts using Chart.js v4.x.

CRITICAL REQUIREMENTS:
1. ALWAYS generate COMPLETE HTML - never truncate or use "..." or "…"
2. The HTML must be fully functional and well-formed
3. Always include closing tags: </script>, </body>, </html>
4. Use Chart.js v4.x with CDN: https://cdn.jsdelivr.net/npm/chart.js@4
5. Include all chart configuration - never abbreviate with ellipsis

IMPORTANT GUIDELINES:
1. Always use Chart.js only - no other libraries
2. Generate complete HTML with Chart.js CDN (v4.x)
3. Use canvas element with width="800" height="600" for proper sizing
4. Charts must be educational for ${gradeLevel} level
5. Use proper Chart.js configuration syntax
6. Always include responsive: true and maintainAspectRatio: false
7. Use clear, readable fonts and proper axis labels
8. Include title, legend, and proper formatting

Required color palette (use these colors in order):
['#D1D8DC', '#00E2B1', '#CCD7E6', '#54D3DA', '#A2ACE0', '#41B8DD', '#FBD0B9']

Chart type to use: ${chartType}
Available data: ${JSON.stringify(chartData)}

Structure your HTML like this:
<!DOCTYPE html>
<html>
<head>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4"></script>
</head>
<body style="margin: 0; padding: 20px; background: white;">
  <canvas id="chart" width="800" height="600"></canvas>
  <script>
    // Your Chart.js code here - COMPLETE configuration required
  </script>
</body>
</html>

REMEMBER: Never use "..." or "…" - always provide complete code!
`;

    const prompt = `
Section: ${sectionHeading}
Chart Type: ${chartType}
Data: ${JSON.stringify(chartData)}
Context: ${content.substring(0, 300)}

Create a Chart.js visualization that:
1. Uses the data provided
2. Is appropriate for ${gradeLevel} students
3. Has clear labels and title
4. Uses the specified color palette
5. Fits 800x600 canvas size

CRITICAL: Return ONLY the complete HTML code with Chart.js, no explanations.
- Must be complete HTML with all closing tags
- Never use "..." or "…" - provide full configuration
- Include all data points and labels
- Ensure the chart renders properly
`;

    try {
      const result = await this.aiSdk.generateText({
        provider: 'anthropic',
        model: 'claude-sonnet-4-20250514',
        prompt,
        systemPrompt,
        identifierName: 'ChartGenerationService_ChartjsCode',
        identifierValue: sectionHeading,
      });

      let htmlCode = result.text.trim();

      // Clean up the response - remove markdown code blocks if present
      if (htmlCode.startsWith('```html')) {
        htmlCode = htmlCode
          .replace(/```html\n?/, '')
          .replace(/```$/, '')
          .trim();
      } else if (htmlCode.startsWith('```')) {
        htmlCode = htmlCode
          .replace(/```\n?/, '')
          .replace(/```$/, '')
          .trim();
      }

      // Check for truncated responses (common with AI responses)
      if (htmlCode.includes('…') || htmlCode.includes('...')) {
        this.logger.warn('Detected truncated HTML response', {
          sectionHeading,
          codeLength: htmlCode.length,
          hasTruncationMarker: htmlCode.includes('…') || htmlCode.includes('...'),
        });
        throw new Error('Generated HTML appears to be truncated');
      }

      // Ensure the HTML is well-formed
      if (!htmlCode.includes('</html>') || !htmlCode.includes('</script>')) {
        this.logger.warn('Generated HTML appears incomplete', {
          sectionHeading,
          hasClosingHtml: htmlCode.includes('</html>'),
          hasClosingScript: htmlCode.includes('</script>'),
          codeLength: htmlCode.length,
        });
        throw new Error('Generated HTML is incomplete');
      }

      // More flexible validation for Chart.js HTML
      const hasChartJs =
        htmlCode.toLowerCase().includes('chart.js') ||
        htmlCode.includes('Chart(') ||
        htmlCode.includes('new Chart');
      const hasCanvas = htmlCode.includes('<canvas') || htmlCode.includes('canvas');
      const hasBasicHtml = htmlCode.includes('<html') && htmlCode.includes('</html>');

      if (!htmlCode || !hasChartJs || !hasCanvas || !hasBasicHtml) {
        this.logger.warn('Generated HTML validation failed', {
          hasHtmlCode: !!htmlCode,
          hasChartJs,
          hasCanvas,
          hasBasicHtml,
          codePreview: htmlCode?.substring(0, 200) + (htmlCode?.length > 200 ? '...' : ''),
        });
        throw new Error('Generated invalid Chart.js HTML');
      }

      const cost = 0; // Calculate based on usage if needed

      this.logger.debug(`Generated Chart.js code for ${sectionHeading}:`, {
        codeLength: htmlCode.length,
        hasCanvas: htmlCode.includes('<canvas'),
        hasChartJs: htmlCode.toLowerCase().includes('chart.js'),
        hasNewChart: htmlCode.includes('new Chart'),
        codePreview: htmlCode.substring(0, 300) + (htmlCode.length > 300 ? '...' : ''),
      });

      return { htmlCode, cost };
    } catch (error) {
      this.logger.error('Failed to generate Chart.js code', {
        error: error instanceof Error ? error.message : String(error),
        sectionHeading,
        chartData,
      });
      throw error;
    }
  }

  private determineChartType(chartData: any): string {
    // Simple logic to determine chart type based on data structure
    if (chartData.datasets?.length > 1) {
      return 'line'; // Multiple datasets work well with line charts
    }

    if (chartData.labels?.length > 7) {
      return 'line'; // Many data points work better as line charts
    }

    if (chartData.data?.every((val: any) => typeof val === 'number' && val >= 0)) {
      // All positive numbers could be a pie chart
      if (chartData.labels?.length <= 6) {
        return 'pie';
      }
    }

    // Default to bar chart for most educational content
    return 'bar';
  }

  private async renderChartToPNG(htmlCode: string): Promise<Buffer> {
    const browser = await this.getBrowser();
    const page = await browser.newPage();

    try {
      // Set viewport for charts
      await page.setViewport({ width: 900, height: 700 });

      // Set content
      await page.setContent(htmlCode, {
        waitUntil: 'networkidle0',
      });

      // Wait for chart to render
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Find the canvas element and take screenshot
      const canvasElement = await page.$('canvas');
      if (!canvasElement) {
        throw new Error('No canvas element found in generated HTML');
      }

      const screenshot = await canvasElement.screenshot({
        type: 'png',
      });

      return Buffer.from(screenshot);
    } finally {
      await page.close();
    }
  }

  private generateS3Key(chartId: string, type: string): string {
    return `generated-content/${type}/${chartId}.png`;
  }

  private createEmptyPlaceholder(
    id: string,
    placeholder: string,
    title: string,
    chartData: any,
  ): ChartArtifact {
    return {
      id,
      type: 'chart',
      title,
      description: `Chart showing ${chartData.labels?.join(', ') || 'data'}`,
      chartType: 'chartjs',
      data: chartData,
      cost: 0,
      placeholder,
      // No imageUrl or s3Key - will be handled by placeholder replacement
    };
  }

  async generateMultipleCharts(
    sectionHeading: string,
    chartDataArray: any[],
    content: string,
    gradeLevel: string,
  ): Promise<ChartArtifact[]> {
    const charts: ChartArtifact[] = [];

    for (const chartData of chartDataArray) {
      try {
        const chart = await this.generateChart(sectionHeading, chartData, content, gradeLevel);
        charts.push(chart);
      } catch (error) {
        this.logger.error(`Failed to generate chart for data:`, {
          error: error instanceof Error ? error.message : String(error),
          chartData,
        });
        // Continue with other charts
      }
    }

    return charts;
  }

  async onModuleDestroy() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
