import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export enum ContentFormat {
  TEXT = 'text',
  SLIDE = 'slide',
  HTML_CANVAS = 'html_canvas',
  WEBGL = 'webgl',
  LATEX = 'latex',
}

export interface VisualPlaceholder {
  id: string;
  type: 'chart' | 'graph' | 'diagram' | 'image' | 'citation';
  title: string;
  description: string;
  placeholder: string;
  cost: number;
  [key: string]: any; // For type-specific properties
}

export class CustomPropertyDto {
  @ApiProperty({
    description: 'Property key',
    example: 'difficulty_level',
  })
  @IsNotEmpty()
  @IsString()
  key!: string;

  @ApiProperty({
    description: 'Property value',
    example: 'intermediate',
  })
  @IsNotEmpty()
  @IsString()
  value!: string;
}

export class OutputSettingsDto {
  @ApiProperty({
    description: 'Desired length of the generated content',
    example: 'medium',
  })
  @IsNotEmpty()
  @IsString()
  length!: string;

  @ApiProperty({
    enum: ContentFormat,
    description: 'Desired format of the generated content',
    example: ContentFormat.TEXT,
  })
  @IsEnum(ContentFormat)
  format!: ContentFormat;

  @ApiProperty({
    description: 'Desired language of the generated content',
    example: 'English',
  })
  @IsNotEmpty()
  @IsString()
  language!: string;
}

export class GenerateContentDto {
  @ApiProperty({
    description: 'Category of curriculum material',
    example: 'Mathematics',
  })
  @IsNotEmpty()
  @IsString()
  category!: string;

  @ApiProperty({
    description: 'Name of the content snippet',
    example: 'Algebra Fundamentals',
  })
  @IsNotEmpty()
  @IsString()
  snippetName!: string;

  @ApiProperty({
    description: 'Description of the content snippet',
    example: 'Introduction to basic algebraic concepts and equations',
  })
  @IsNotEmpty()
  @IsString()
  snippetDescription!: string;

  @ApiProperty({
    description: 'Whether to use knowledge base for content generation',
    example: true,
  })
  @IsBoolean()
  knowledgeBase!: boolean;

  @ApiProperty({
    description: 'Whether to use web search for content generation',
    example: false,
  })
  @IsBoolean()
  webSearch!: boolean;

  @ApiProperty({
    description: 'Grade level for the content',
    example: '9th Grade',
  })
  @IsOptional()
  @IsString()
  grade?: string;

  @ApiProperty({
    description: 'User instructions for content generation',
    example: 'Focus on practical examples and include step-by-step solutions',
  })
  @IsOptional()
  @IsString()
  userInstructions?: string;

  @ApiProperty({
    description: 'Specific prompt for content generation',
    example: 'Generate a comprehensive lesson plan on linear equations',
  })
  @IsNotEmpty()
  @IsString()
  prompt!: string;

  @ApiProperty({
    type: OutputSettingsDto,
    description: 'Output settings for the generated content',
  })
  @ValidateNested()
  @Type(() => OutputSettingsDto)
  outputSettings!: OutputSettingsDto;

  @ApiPropertyOptional({
    type: [String],
    description: 'Optional file URLs to upload for temporary knowledge base',
    example: ['https://example.com/files/document.pdf', 'https://example.com/files/worksheet.docx'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fileUrls?: string[];

  @ApiPropertyOptional({
    type: [String],
    description: 'Optional URLs to extract content from for temporary knowledge base',
    example: ['https://example.com/article', 'https://wikipedia.org/wiki/Linear_equation'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  urls?: string[];

  @ApiPropertyOptional({
    type: [CustomPropertyDto],
    description: 'Custom properties as key-value pairs',
    example: [
      { key: 'difficulty_level', value: 'intermediate' },
      { key: 'duration', value: '45 minutes' },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomPropertyDto)
  customProperties?: CustomPropertyDto[];
}

export class ContentGenerationResponseDto {
  @ApiProperty({
    description: 'Generated content text with placeholders for visual elements',
    example: 'This is the generated curriculum content... {{CHART_1}} ... {{IMAGE_1}} ...',
  })
  content!: string;

  @ApiProperty({
    description: 'Length of the generated content in characters',
    example: 1250,
  })
  length!: number;

  @ApiProperty({
    enum: ContentFormat,
    description: 'Format of the generated content',
    example: ContentFormat.TEXT,
  })
  format!: ContentFormat;

  @ApiProperty({
    description: 'Language of the generated content',
    example: 'English',
  })
  language!: string;

  @ApiPropertyOptional({
    description: 'Total cost for generating all visual elements',
    example: 0.125,
  })
  totalCost?: number;
}

export class ContentGenerationListQueryDto {
  @ApiPropertyOptional({
    description: 'Search query for content',
    example: 'mathematics algebra',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by category',
    example: 'Mathematics',
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({
    description: 'Filter by grade',
    example: '9th Grade',
  })
  @IsOptional()
  @IsString()
  grade?: string;

  @ApiPropertyOptional({
    enum: ContentFormat,
    description: 'Filter by content format',
    example: ContentFormat.TEXT,
  })
  @IsOptional()
  @IsEnum(ContentFormat)
  format?: ContentFormat;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of results to return (1-100)',
    minimum: 1,
    maximum: 100,
    example: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of results to skip',
    minimum: 0,
    default: 0,
    example: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  offset?: number = 0;
}

export class ContentGenerationListResponseDto {
  @ApiProperty({
    type: [ContentGenerationResponseDto],
    description: 'List of generated content items',
  })
  items!: ContentGenerationResponseDto[];

  @ApiProperty({
    type: Number,
    description: 'Total number of items available',
    example: 150,
  })
  total!: number;

  @ApiProperty({
    type: Number,
    description: 'Number of items returned in this response',
    example: 20,
  })
  limit!: number;

  @ApiProperty({
    type: Number,
    description: 'Number of items skipped',
    example: 0,
  })
  offset!: number;

  @ApiProperty({
    type: Boolean,
    description: 'Whether there are more items available',
    example: true,
  })
  hasMore!: boolean;
}
