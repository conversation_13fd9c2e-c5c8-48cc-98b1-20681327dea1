import type { PathsObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import basicAuth from 'express-basic-auth';

import config from '@/common/configs/config';

export function generateOpenAPISpec(app: INestApplication) {
  if (config().swagger.enabled) {
    config().swagger.docs.forEach((doc) => {
      app.use(
        [`/${doc.path}`],
        basicAuth({
          challenge: true,
          users: {
            [doc.username]: doc.password,
          },
        }),
      );

      const options = new DocumentBuilder()
        .setTitle(doc.title)
        .setDescription(doc.description)
        .setVersion(doc.version)
        .addBearerAuth()
        .build();

      const document = SwaggerModule.createDocument(app, options, {});
      if (doc.pathNameFilter) {
        document.paths = Object.entries(document.paths).reduce(
          (prev: PathsObject, [pathName, docProps]) => {
            if (pathName.includes(doc.pathNameFilter || '')) {
              prev[pathName] = docProps;
            }
            return prev;
          },
          {},
        );
      }
      SwaggerModule.setup(doc.path, app, document, {
        swaggerOptions: doc.pathNameFilter ? { defaultModelsExpandDepth: -1 } : {},
        customSiteTitle: doc.pageTitle,
        customfavIcon: config().swagger.favIcon,
      });
    });
  }
}
