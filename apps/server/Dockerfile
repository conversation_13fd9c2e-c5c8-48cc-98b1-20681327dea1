# syntax=docker/dockerfile:1
# بسم الله الرحمن الرحيم

FROM node:22 AS base

# --- Builder stage: install turbo and prune the monorepo ---
FROM base AS builder
WORKDIR /app

# Install turbo globally (replace <your-major-version> if needed)
RUN npm install -g turbo

# Copy only the necessary files for pruning
COPY . .

# Prune the monorepo for the server workspace (NestJS)
RUN turbo prune @ps/server --docker

# --- Installer stage: install dependencies for the pruned app ---
FROM base AS installer

# Install necessary dependencies for Puppeteer and Chromium
RUN apt-get update && apt-get install -y --no-install-recommends \
  fonts-liberation \
  libasound2 \
  libatk-bridge2.0-0 \
  libatk1.0-0 \
  libcups2 \
  libdrm2 \
  libgbm1 \
  libgtk-3-0 \
  libnspr4 \
  libnss3 \
  libx11-xcb1 \
  libxcomposite1 \
  libxdamage1 \
  libxrandr2 \
  xdg-utils \
  libu2f-udev \
  libxshmfence1 \
  libglu1-mesa \
  chromium \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH="/usr/bin/chromium"

WORKDIR /app

# Enable and prepare pnpm with Corepack
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy pruned package.json and lockfile for dependency install
COPY --from=builder /app/out/json/ .

# Install dependencies using pnpm
RUN pnpm install --frozen-lockfile

# Copy the full pruned source
COPY --from=builder /app/out/full/ .

# Build the NestJS app using pnpm
RUN pnpm run build

# Expose the port (default NestJS port)
EXPOSE 3333

# Start the NestJS server
CMD ["node", "apps/server/dist/main.js"]
