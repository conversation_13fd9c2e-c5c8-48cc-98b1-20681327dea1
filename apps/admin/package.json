{"name": "@ps/admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "refine dev", "build": "tsc && refine build", "start": "refine start", "lint": "eslint . --max-warnings 0 --fix", "check-types": "tsc --noEmit", "refine": "refine"}, "dependencies": {"@nestjsx/crud-request": "5.0.0-alpha.3", "@ps/common": "workspace:*", "@ps/ui": "workspace:*", "@refinedev/cli": "^2.16.46", "@refinedev/core": "^4.57.9", "@refinedev/devtools": "^1.2.16", "@refinedev/kbar": "^1.3.16", "@refinedev/nestjsx-crud": "^5.0.12", "@refinedev/react-hook-form": "^4.10.1", "@refinedev/react-router": "^1.0.1", "@refinedev/react-table": "^5.6.17", "@t3-oss/env-core": "^0.13.8", "@tanstack/react-query": "^4.40.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-router": "^7.6.2", "react-use": "^17.6.0", "sonner": "^2.0.5", "zod": "^3.25.67"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/types": "workspace:*", "@ps/typescript-config": "workspace:*", "@types/lodash": "^4.17.18", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-mkcert": "^1.17.8"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "uWi6eG-J2rusz-PwgCqa"}}