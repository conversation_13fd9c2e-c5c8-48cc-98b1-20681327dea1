'use client';

import type { Resource } from '@/types';

import { LuLayoutDashboard } from 'react-icons/lu';

import {
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  Sidebar,
} from '@ps/ui/components/sidebar';
import appResources from '@/pages';

import { AppInfo } from './app-info';
import { NavMain } from './nav-main';
import { NavUser } from './nav-user';

const resources = [
  {
    title: 'Overview',
    url: '/',
    icon: LuLayoutDashboard,
    isActive: true,
  },
  ...(appResources as unknown as Resource[]),
];

export function DashboardSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <AppInfo />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={resources} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
