'use client';

import type { User } from '@ps/types';

import { LuChevronsUpDown, LuLogOut, LuMoon, LuSun } from 'react-icons/lu';
import { useGetIdentity, useLogout } from '@refinedev/core';

import {
  DropdownMenuSeparator,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenu,
  useSidebar,
} from '@ps/ui/components/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@ps/ui/components/avatar';
import { getInitials } from '@ps/common/utils/string';
import { Switch } from '@ps/ui/components/switch';
import useTheme from '@/providers/theme/use-theme';

export function NavUser() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { mutate: logout } = useLogout();
  const { isMobile } = useSidebar();

  const currentTheme = theme === 'system' ? resolvedTheme : theme;
  const isDark = currentTheme === 'dark';

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              size="lg"
            >
              <UserInfo />
              <LuChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <UserInfo />
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* <DropdownMenuGroup>
              <DropdownMenuItem>
                <LuBell />
                Notifications
              </DropdownMenuItem>
            </DropdownMenuGroup> */}
            <DropdownMenuGroup>
              <DropdownMenuItem
                className="flex cursor-pointer items-center justify-between gap-2"
                onClick={(e) => e.preventDefault()}
              >
                <div className="flex items-center gap-2">
                  {isDark ? <LuMoon className="size-4" /> : <LuSun className="size-4" />}
                  <span>Dark mode</span>
                </div>
                <Switch
                  onCheckedChange={() => setTheme(isDark ? 'light' : 'dark')}
                  className="cursor-pointer"
                  checked={isDark}
                />
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => logout()}>
              <LuLogOut />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

const UserInfo = () => {
  const { data: user } = useGetIdentity<User>();

  return (
    <>
      <Avatar className="size-8 rounded-lg">
        <AvatarImage src={user?.profilePicture} alt={user?.name} />
        <AvatarFallback className="rounded-lg">{getInitials(user?.name)}</AvatarFallback>
      </Avatar>
      <div className="grid flex-1 text-left text-sm leading-tight">
        <span className="truncate font-medium">{user?.name}</span>
        <span className="truncate text-xs">{user?.email}</span>
      </div>
    </>
  );
};
