'use client';

import { SidebarMenuButton, SidebarMenuItem, SidebarMenu } from '@ps/ui/components/sidebar';
import config from '@/config';

export function AppInfo() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          size="lg"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div className="text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
            <img src={config.app.logo} alt={config.app.name} className="size-8" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-medium">{config.app.name} Admin</span>
            <span className="truncate text-xs">Super Admin</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
