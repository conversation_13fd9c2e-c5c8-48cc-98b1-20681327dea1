import { useBreadcrumb } from '@refinedev/core';
import { Fragment } from 'react';

import {
  BreadcrumbSeparator,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  Breadcrumb,
} from '@ps/ui/components/breadcrumb';

export default function DashboardBreadcrumb() {
  const { breadcrumbs } = useBreadcrumb();

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((breadcrumb, index) => {
          return (
            <Fragment key={breadcrumb.label}>
              {index !== 0 && <BreadcrumbSeparator className="hidden md:block" />}

              {index !== breadcrumbs.length - 1 ? (
                <BreadcrumbItem className="hidden md:block" key={breadcrumb.label}>
                  <BreadcrumbLink className="capitalize" href={breadcrumb.href}>
                    {breadcrumb.label}
                  </BreadcrumbLink>
                </BreadcrumbItem>
              ) : (
                <BreadcrumbPage className="capitalize">{breadcrumb.label}</BreadcrumbPage>
              )}
            </Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
