import { SidebarProvider, SidebarTrigger, SidebarInset } from '@ps/ui/components/sidebar';

import { Separator } from '@ps/ui/components/separator';
import { Sonner } from '@ps/ui/components/sonner';

import { DashboardSidebar } from './sidebar/app-sidebar';
import { ThemeToggle } from '../theme-toggle';
import DashboardBreadcrumb from './breadcrumb';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <DashboardSidebar />
      <SidebarInset>
        <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-16 shrink-0 items-center justify-between gap-2 border-b pr-6 transition-[width,height] ease-linear">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <DashboardBreadcrumb />
          </div>
          <ThemeToggle />
        </header>

        <div className="p-6">{children}</div>
      </SidebarInset>

      <Sonner />
    </SidebarProvider>
  );
}
