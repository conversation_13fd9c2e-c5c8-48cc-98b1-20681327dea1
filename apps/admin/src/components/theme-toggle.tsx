import type { Theme } from '@/providers/theme/context';

import { LuMonitor, <PERSON><PERSON>oon, <PERSON>Sun } from 'react-icons/lu';

import { Button } from '@ps/ui/components/button';
import useTheme from '@/providers/theme/use-theme';

const getIcon = (theme: Theme) => {
  switch (theme) {
    case 'light':
      return <LuSun className="size-4" />;
    case 'dark':
      return <LuMoon className="size-4" />;
    case 'system':
      return <LuMonitor className="size-4" />;
  }
};

const getLabel = (theme: Theme) => {
  switch (theme) {
    case 'light':
      return 'Light mode';
    case 'dark':
      return 'Dark mode';
    case 'system':
      return 'System mode';
  }
};

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const cycleTheme = () => {
    switch (theme) {
      case 'light':
        setTheme('dark');
        break;
      case 'dark':
        setTheme('system');
        break;
      case 'system':
        setTheme('light');
        break;
    }
  };

  return (
    <Button
      className="size-9 p-0"
      variant="outline"
      size="sm"
      onClick={cycleTheme}
      title={getLabel(theme)}
    >
      {getIcon(theme)}
      <span className="sr-only">{getLabel(theme)}</span>
    </Button>
  );
}
