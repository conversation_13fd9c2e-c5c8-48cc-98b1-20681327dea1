import type { InternalAxiosRequestConfig, AxiosError } from 'axios';

import { axiosInstance } from '@refinedev/nestjsx-crud';

import { logout } from '@/providers/auth';
import { env } from '@/config/env';

// these errors happen on expired or malformed token, remove token from cache and reload page
const errorsRequiringReload = ['token', 'signature', 'unauthorized'];
const ERROR_MSG_DEFAULT = 'Something went wrong! Please try again.';

const errorHandler = (err: AxiosError) => {
  // console.log('API', err?.response);
  try {
    const data = err?.response?.data as Error;
    const message = data?.message || err?.message;

    if (message && typeof message === 'object') {
      return Promise.reject(message);
    }
    if (message) {
      for (const _error of errorsRequiringReload) {
        const _message = message.toLowerCase();
        if (_message.includes(_error)) {
          logout();
          return window.location.reload();
        }
      }
      return Promise.reject(err);
    }
    return Promise.reject(ERROR_MSG_DEFAULT);
  } catch (_) {
    return Promise.reject(ERROR_MSG_DEFAULT);
  }
};

axiosInstance.defaults.baseURL = env.VITE_API_URL;
axiosInstance.defaults.timeout = 25 * 1000;

axiosInstance.interceptors.request.use((request: InternalAxiosRequestConfig) => {
  const token = localStorage.getItem('token');

  request.headers.set('Authorization', `Bearer ${token}`);
  request.headers.set('x-app-name', 'admin');

  return request;
});

axiosInstance.interceptors.response.use((r) => r, errorHandler);

export { axiosInstance };
