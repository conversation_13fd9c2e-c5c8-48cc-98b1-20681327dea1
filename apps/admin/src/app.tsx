import type { IResourceItem, Action } from '@refinedev/core';

import routerBindings, {
  UnsavedChangesNotifier,
  DocumentTitleHandler,
} from '@refinedev/react-router';
import { DevtoolsProvider, DevtoolsPanel } from '@refinedev/devtools';
import { RefineKbarProvider, RefineKbar } from '@refinedev/kbar';
import { Refine } from '@refinedev/core';
import capitalize from 'lodash/capitalize';

import { BrowserRouter } from 'react-router';

import { ThemeProvider } from '@/providers/theme';
import { Sonner } from '@ps/ui/components/sonner';
import { Routes } from '@/router';
import authProvider from '@/providers/auth';
import dataProvider from '@/providers/data';
import resources from '@/pages';
import config from '@/config';

import '@ps/ui/globals.css';

const customTitleHandler = ({
  resource,
  action,
}: {
  resource?: IResourceItem;
  action?: Action;
}) => {
  let title = `${config.app.name} Admin`;

  if (resource?.name) {
    title = `${resource.name}${action ? ` ${action}` : ''} - ${title}`;
  }

  return capitalize(title);
};

function App() {
  return (
    <BrowserRouter>
      <ThemeProvider>
        <RefineKbarProvider>
          <DevtoolsProvider>
            <Refine
              routerProvider={routerBindings}
              dataProvider={dataProvider()}
              authProvider={authProvider}
              resources={resources}
              options={{
                projectId: 'uWi6eG-J2rusz-PwgCqa',
                warnWhenUnsavedChanges: true,
                syncWithLocation: true,
                useNewQueryKeys: true,
              }}
            >
              <Routes />

              <RefineKbar />
              <UnsavedChangesNotifier />
              <DocumentTitleHandler handler={customTitleHandler} />
              <Sonner />
            </Refine>
            <DevtoolsPanel />
          </DevtoolsProvider>
        </RefineKbarProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
}

export default App;
