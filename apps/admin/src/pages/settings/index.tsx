import type { IResourceComponentsProps } from '@refinedev/core';

import { BaseCreate, BaseEdit, BaseList, BaseShow } from '@/pages/base';
import SettingsForm from './components/settings-form';

import actions from './actions';

export const SettingsCreate: React.FC<IResourceComponentsProps> = () => (
  <BaseCreate Form={SettingsForm} />
);

export const SettingsEdit: React.FC<IResourceComponentsProps> = () => (
  <BaseEdit Form={SettingsForm} />
);

export const SettingsList: React.FC<IResourceComponentsProps> = () => (
  <BaseList actions={actions} />
);

export const SettingsShow: React.FC<IResourceComponentsProps> = () => <BaseShow />;
