import type { Resource } from '@/types';

import { LuSettings } from 'react-icons/lu';

const settingsResource: Resource = {
  isActive: true,
  icon: LuSettings,
  title: 'Settings',
  name: 'settings',
  url: '/settings',
  list: '/settings',
  create: '/settings/create',
  edit: '/settings/edit/:id',
  show: '/settings/show/:id',
  meta: {
    canEdit: true,
    canCreate: true,
    canDelete: true,
    fields: [
      {
        name: 'Type',
        type: 'select',
        options: [
          { label: 'Text', value: 'text' },
          { label: 'Number', value: 'number' },
          { label: 'Boolean', value: 'boolean' },
          { label: 'Date', value: 'date' },
          { label: 'Array', value: 'tag' },
          { label: 'JSON', value: 'json' },
        ],
        defaultValue: 'text',
      },
      { name: 'Key', meta: { isFilterable: true, isSortable: true, isRaw: true } },
      { name: 'Value', type: 'dynamic', dynamicBasedOn: 'type' },
    ],
  },
};

export default settingsResource;
