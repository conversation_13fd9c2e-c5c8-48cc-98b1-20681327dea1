import type { Control, FieldValues } from 'react-hook-form';

import { useController, useWatch } from 'react-hook-form';
import { LuCalendar } from 'react-icons/lu';
import { format } from 'date-fns';
import { useState } from 'react';

import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import { PopoverContent, PopoverTrigger, Popover } from '@ps/ui/components/popover';
import { Checkbox } from '@ps/ui/components/checkbox';
import { Calendar } from '@ps/ui/components/calendar';
import { Textarea } from '@ps/ui/components/textarea';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { Label } from '@ps/ui/components/label';

interface SettingsFormProps {
  mode: 'create' | 'edit';
  control: Control<FieldValues>;
}

const SettingsForm: React.FC<SettingsFormProps> = ({ control }) => {
  const [calendarOpen, setCalendarOpen] = useState(false);

  // Watch the type field to determine how to render the value field
  const selectedType = useWatch({
    control,
    name: 'type',
    defaultValue: 'text',
  });

  const {
    field: keyField,
    fieldState: { error: keyError },
  } = useController({
    name: 'key',
    control,
    rules: { required: 'Key is required' },
  });

  const {
    field: typeField,
    fieldState: { error: typeError },
  } = useController({
    name: 'type',
    control,
    rules: { required: 'Type is required' },
  });

  const {
    field: valueField,
    fieldState: { error: valueError },
  } = useController({
    name: 'value',
    control,
    rules: { required: 'Value is required' },
  });

  const renderValueField = () => {
    switch (selectedType) {
      case 'text':
        return (
          <Input
            {...valueField}
            placeholder="Enter text value"
            className={valueError ? 'border-red-500' : ''}
          />
        );

      case 'number':
        return (
          <Input
            {...valueField}
            type="number"
            placeholder="Enter number value"
            className={valueError ? 'border-red-500' : ''}
            onChange={(e) => valueField.onChange(Number(e.target.value))}
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id="value-checkbox"
              checked={valueField.value || false}
              onCheckedChange={(checked) => valueField.onChange(checked)}
            />
            <Label htmlFor="value-checkbox">{valueField.value ? 'True' : 'False'}</Label>
          </div>
        );

      case 'date':
        return (
          <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={`w-full justify-start text-left font-normal ${
                  valueError ? 'border-red-500' : ''
                } ${!valueField.value ? 'text-muted-foreground' : ''}`}
              >
                <LuCalendar className="mr-2 h-4 w-4" />
                {valueField.value ? (
                  format(new Date(valueField.value), 'PPP')
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={valueField.value ? new Date(valueField.value) : undefined}
                onSelect={(date) => {
                  valueField.onChange(date?.toISOString());
                  setCalendarOpen(false);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'tag':
        return (
          <Input
            {...valueField}
            placeholder="Enter comma-separated values"
            className={valueError ? 'border-red-500' : ''}
            onChange={(e) => {
              const tags = e.target.value
                .split(',')
                .map((tag) => tag.trim())
                .filter(Boolean);
              valueField.onChange(tags);
            }}
            value={Array.isArray(valueField.value) ? valueField.value.join(', ') : ''}
          />
        );

      case 'json':
        return (
          <Textarea
            {...valueField}
            placeholder="Enter valid JSON"
            className={`min-h-[120px] ${valueError ? 'border-red-500' : ''}`}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                valueField.onChange(parsed);
              } catch {
                // Keep the raw string if it's not valid JSON yet
                valueField.onChange(e.target.value);
              }
            }}
            value={
              typeof valueField.value === 'string'
                ? valueField.value
                : JSON.stringify(valueField.value, null, 2)
            }
          />
        );

      default:
        return (
          <Input
            {...valueField}
            placeholder="Enter value"
            className={valueError ? 'border-red-500' : ''}
          />
        );
    }
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      {/* Key Field */}
      <div className="space-y-2">
        <Label htmlFor="key">Key</Label>
        <Input
          id="key"
          {...keyField}
          placeholder="Enter setting key"
          className={keyError ? 'border-red-500' : ''}
        />
        {keyError && <p className="text-sm text-red-500">{keyError.message}</p>}
      </div>

      {/* Type Field */}
      <div className="space-y-2">
        <Label htmlFor="type">Type</Label>
        <Select value={typeField.value} onValueChange={typeField.onChange}>
          <SelectTrigger className={typeError ? 'border-red-500' : ''}>
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="text">Text</SelectItem>
            <SelectItem value="number">Number</SelectItem>
            <SelectItem value="boolean">Boolean</SelectItem>
            <SelectItem value="date">Date</SelectItem>
            <SelectItem value="tag">Array/Tags</SelectItem>
            <SelectItem value="json">JSON</SelectItem>
          </SelectContent>
        </Select>
        {typeError && <p className="text-sm text-red-500">{typeError.message}</p>}
      </div>

      {/* Dynamic Value Field */}
      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="value">
          Value
          <span className="text-muted-foreground ml-2 text-sm">({selectedType})</span>
        </Label>
        {renderValueField()}
        {valueError && <p className="text-sm text-red-500">{valueError.message}</p>}

        {/* Help text for different types */}
        <div className="text-muted-foreground text-sm">
          {selectedType === 'tag' && 'Enter comma-separated values for array/tags'}
          {selectedType === 'json' && 'Enter valid JSON format'}
          {selectedType === 'boolean' && 'Check the box for true, uncheck for false'}
          {selectedType === 'date' && 'Select a date from the calendar'}
          {selectedType === 'number' && 'Enter a numeric value'}
          {selectedType === 'text' && 'Enter any text value'}
        </div>
      </div>
    </div>
  );
};

export default SettingsForm;
