import type { Category } from '@ps/types';
import type { Resource } from '@/types';

import { RiCodeBoxLine } from 'react-icons/ri';

const snippetResource: Resource = {
  icon: RiCodeBoxLine,
  isActive: true,
  title: 'Snippets',
  name: 'snippets',
  url: '/snippets',
  list: '/snippets',
  create: '/snippets/create',
  edit: '/snippets/edit/:id',
  show: '/snippets/show/:id',
  meta: {
    join: ['category'],
    canEdit: true,
    canCreate: true,
    canDelete: true,
    fields: [
      {
        name: 'Icon',
        key: 'icon',
        type: 'image',
        meta: { isSortable: false, uploadFolder: 'icons/snippets' },
      },
      { name: 'Name', meta: { isFilterable: true, isSortable: true } },
      { name: 'Description', type: 'text', meta: { isFilterable: true } },
      {
        name: 'Prompt Template',
        key: 'promptTemplate',
        type: 'text',
        meta: { isSortable: false, hideInList: true },
      },
      {
        name: 'Category',
        type: 'select',
        relation: {
          resource: 'category',
          field: 'category.name',
          path: 'categories',
        },
        meta: {
          optionFetchUrl: '/categories',
          onOptionFetch: (data: ListResponse<Category>) =>
            data.data.map((d: Category) => ({ label: d.name, value: String(d._id) })),
        },
      },
      {
        name: 'Enable Chatbox',
        key: 'enableChatBox',
        type: 'boolean',
        defaultValue: true,
        meta: {
          isOptional: true,
        },
      },
      { name: 'Chatbox Placeholder', key: 'chatboxPlaceholder', meta: { isOptional: true } },
      {
        name: 'Enable Grade Field',
        key: 'enableGradeField',
        type: 'boolean',
        defaultValue: true,
        meta: {
          isOptional: true,
        },
      },
      {
        name: 'Input Fields',
        key: 'inputFields',
        type: 'array',
        meta: {
          isSortable: false,
          hideInList: true,
          isOptional: true,
        },
        children: [
          { name: 'Label' },
          { name: 'Placeholder', meta: { isOptional: true } },
          {
            name: 'Type',
            type: 'select',
            options: [
              { label: 'Text', value: 'text' },
              { label: 'Number', value: 'number' },
              { label: 'Date', value: 'date' },
              { label: 'Checkbox', value: 'checkbox' },
              { label: 'Radio', value: 'radio' },
              { label: 'Select', value: 'select' },
            ],
          },
          {
            name: 'Options',
            type: 'array',
            meta: { isOptional: true },
            children: [
              { name: 'Label', type: 'text' },
              { name: 'Value', type: 'text' },
            ],
          },
          // { name: 'Description', type: 'text' },
          // { name: 'Default Value', type: 'text' },
          // { name: 'Min', type: 'number' },
          // { name: 'Max', type: 'number' },
          // { name: 'Step', type: 'number' },
          // { name: 'Min Length', type: 'number' },
          // { name: 'Max Length', type: 'number' },
          // { name: 'Pattern', type: 'text' },
          // { name: 'Multiple', type: 'boolean' },
          // { name: 'Accept', type: 'text' },
          // { name: 'Rows', type: 'number' },
        ],
      },
    ],
  },
};

export default snippetResource;
