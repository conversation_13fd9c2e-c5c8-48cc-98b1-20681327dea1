import type { IResourceComponentsProps } from '@refinedev/core';

import { BaseCreate, BaseEdit, BaseList, BaseShow } from '@/pages/base';

import actions from './actions';

export const SnippetCreate: React.FC<IResourceComponentsProps> = () => <BaseCreate />;
export const SnippetEdit: React.FC<IResourceComponentsProps> = () => <BaseEdit />;
export const SnippetList: React.FC<IResourceComponentsProps> = () => <BaseList actions={actions} />;
export const SnippetShow: React.FC<IResourceComponentsProps> = () => <BaseShow />;
