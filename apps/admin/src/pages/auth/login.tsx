import { useEffect } from 'react';
import { useLogin } from '@refinedev/core';
import { FaGoogle } from 'react-icons/fa';

import { getSocialAuthUrlWithRedirect } from '@ps/common/utils/auth';
import { axiosInstance } from '@/services/api';
import { ThemeToggle } from '@/components/theme-toggle';
import { getState } from '@/utils/auth';
import { Button } from '@ps/ui/components/button';
import { env } from '@/config/env';
import config from '@/config';

export default function Login() {
  const { mutate: login } = useLogin();
  const token = new URLSearchParams(window.location.search).get('token') || '';

  useEffect(() => {
    if (token) {
      localStorage.setItem('token', token);
      axiosInstance.get('/me').then(({ data: me }) => login(me));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token]);

  return (
    <div className="bg-background min-h-screen">
      <header className="border-border border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="bg-primary flex size-8 items-center justify-center rounded-md">
              <span className="text-primary-foreground text-sm font-bold">B</span>
            </div>
            <span className="text-foreground text-xl font-semibold">{config.app.name} Admin</span>
          </div>

          <ThemeToggle />
        </div>
      </header>

      {/* Main Content */}
      <main className="flex min-h-[calc(100vh-80px)] items-center justify-center">
        <div className="w-full max-w-md space-y-6 p-6">
          <div className="space-y-2 text-center">
            <h1 className="text-foreground text-2xl font-bold">Welcome back</h1>
            <p className="text-muted-foreground">Sign in to your admin account</p>
          </div>

          <Button className="w-full max-w-sm" size="lg" asChild>
            <a
              href={getSocialAuthUrlWithRedirect('google', {
                redirectUrl: `${env.VITE_API_URL}/auth/google/callback`,
                clientId: env.VITE_GOOGLE_CLIENT_ID,
                state: getState(),
              })}
            >
              <FaGoogle />
              Sign in with Google
            </a>
          </Button>
        </div>
      </main>
    </div>
  );
}
