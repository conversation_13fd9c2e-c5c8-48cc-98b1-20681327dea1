import type { IResourceComponentsProps } from '@refinedev/core';

import { BaseCreate, BaseEdit, BaseList, BaseShow } from '@/pages/base';

import actions from './actions';

export const CategoryCreate: React.FC<IResourceComponentsProps> = () => <BaseCreate />;
export const CategoryEdit: React.FC<IResourceComponentsProps> = () => <BaseEdit />;
export const CategoryList: React.FC<IResourceComponentsProps> = () => (
  <BaseList actions={actions} />
);
export const CategoryShow: React.FC<IResourceComponentsProps> = () => <BaseShow />;
