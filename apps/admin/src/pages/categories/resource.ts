import type { Resource } from '@/types';

import { BiCategory } from 'react-icons/bi';

const categoryResource: Resource = {
  icon: BiCategory,
  isActive: true,
  title: 'Categories',
  name: 'categories',
  url: '/categories',
  list: '/categories',
  create: '/categories/create',
  edit: '/categories/edit/:id',
  show: '/categories/show/:id',
  meta: {
    canEdit: true,
    canCreate: true,
    canDelete: true,
    sorters: [
      { field: 'sequence', order: 'asc' },
      { field: 'createdAt', order: 'desc' },
    ],
    fields: [
      { name: 'Name', meta: { isFilterable: true, isSortable: true } },
      {
        name: 'Type',
        meta: { isFilterable: true, isSortable: true },
        type: 'select',
        defaultValue: 'lesson',
        options: [
          { label: 'Lesson', value: 'lesson' },
          { label: 'Media', value: 'media' },
          { label: 'Internal', value: 'internal' },
        ],
      },
      {
        name: 'Sequence',
        type: 'number',
        defaultValue: 0,
        meta: { isFilterable: false, isSortable: true },
      },
    ],
  },
};

export default categoryResource;
