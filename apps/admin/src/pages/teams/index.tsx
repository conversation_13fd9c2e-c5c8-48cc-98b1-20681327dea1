import type { IResourceComponentsProps } from '@refinedev/core';

import { BaseCreate, BaseEdit, BaseList, BaseShow } from '@/pages/base';

import actions from './actions';

export const TeamCreate: React.FC<IResourceComponentsProps> = () => <BaseCreate />;
export const TeamEdit: React.FC<IResourceComponentsProps> = () => <BaseEdit />;
export const TeamList: React.FC<IResourceComponentsProps> = () => <BaseList actions={actions} />;
export const TeamShow: React.FC<IResourceComponentsProps> = () => <BaseShow />;
