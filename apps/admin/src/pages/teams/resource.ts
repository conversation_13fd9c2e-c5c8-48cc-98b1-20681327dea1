import type { Resource } from '@/types';

import { RiTeamLine } from 'react-icons/ri';

const teamResource: Resource = {
  icon: RiTeamLine,
  isActive: true,
  title: 'Teams',
  name: 'teams',
  url: '/teams',
  list: '/teams',
  create: '/teams/create',
  edit: '/teams/edit/:id',
  show: '/teams/show/:id',
  meta: {
    canEdit: true,
    canCreate: false,
    canDelete: false,
    fields: [
      { name: 'ID', key: '_id', meta: { isFilterable: true, disableEdit: true } },
      { name: 'Name', meta: { isFilterable: true, isSortable: true, disableEdit: true } },
      {
        name: 'Plan',
        key: 'subscriptionPlan',
        meta: { isFilterable: true, isSortable: true },
      },
      {
        name: 'Subscription Status',
        key: 'subscriptionStatus',
        type: 'select',
        options: [
          { label: 'Active', value: 'active' },
          { label: 'Inactive', value: 'inactive' },
        ],
      },
      {
        name: 'Joined',
        key: 'createdAt',
        type: 'date',
        meta: { isSortable: true, disableEdit: true },
      },
    ],
  },
};

export default teamResource;
