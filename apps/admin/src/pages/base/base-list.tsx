import type { IResourceComponentsProps, BaseRecord } from '@refinedev/core';
import type { ResourceAction, ResourceMeta } from '@/types';
import type { ColumnDef } from '@tanstack/react-table';

import { useNavigation, useTranslate, useResource, useDelete } from '@refinedev/core';
import { LuTrash2, LuP<PERSON>cil, <PERSON><PERSON>ye } from 'react-icons/lu';
import { flexRender } from '@tanstack/react-table';
import { useTable } from '@refinedev/react-table';
import { useMemo } from 'react';
import { toast } from 'sonner';

import {
  AlertDialogDescription,
  AlertDialogContent,
  AlertDialogTrigger,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialog,
} from '@ps/ui/components/alert-dialog';
import {
  PaginationEllipsis,
  PaginationPrevious,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  Pagination,
} from '@ps/ui/components/pagination';
import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import { CardContent, CardHeader, CardTitle, Card } from '@ps/ui/components/card';
import { getFieldName } from '@/utils/string';
import { Button } from '@ps/ui/components/button';

import ActionButton from './components/action-button';
import FieldDisplay from './components/field-display';

export const BaseList: React.FC<IResourceComponentsProps & { actions?: ResourceAction[] }> = ({
  actions = [],
}) => {
  const { mutate: deleteResource, isPending: isDeleting } = useDelete();
  const { create, show, edit } = useNavigation();
  const { resource } = useResource();
  const translate = useTranslate();

  const meta = resource?.meta as ResourceMeta;

  const columns = useMemo<ColumnDef<BaseRecord>[]>(() => {
    const fields = meta?.fields || [];
    const fieldColumns: ColumnDef<BaseRecord>[] = fields
      .filter((f) => !f.meta?.hideInList)
      .map((field) => {
        const fieldName = getFieldName(field.name, field.key);

        return {
          id: fieldName,
          accessorKey: fieldName,
          header: () => <div className="text-left font-medium">{translate(field.name)}</div>,
          cell: ({ getValue, row }) => (
            <FieldDisplay mode="list" field={field} record={row.original} value={getValue()} />
          ),
          enableSorting: field.meta?.isSortable || false,
        };
      });

    const actionsColumn: ColumnDef<BaseRecord> = {
      id: 'actions',
      header: () => <div className="text-right font-medium">{translate('Actions')}</div>,
      cell: ({ row }) => (
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => show(resource?.name || '', row.original._id)}
            className="h-8 w-8 p-0"
          >
            <LuEye className="h-4 w-4" />
          </Button>
          {meta.canEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => edit(resource?.name || '', row.original._id)}
              className="h-8 w-8 p-0"
            >
              <LuPencil className="h-4 w-4" />
            </Button>
          )}
          {actions.map((action, i) => (
            <ActionButton key={i} id={row.original._id} record={row.original} {...action} />
          ))}
          {meta.canDelete && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  className="text-destructive hover:text-destructive size-8"
                  disabled={isDeleting}
                  variant="outline"
                  size="sm"
                >
                  <LuTrash2 className="size-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete this record? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() =>
                      deleteResource(
                        { resource: resource?.name || '', id: row.original._id },
                        { onSuccess: () => toast.success('Resource deleted successfully') },
                      )
                    }
                    disabled={isDeleting}
                    className="bg-destructive hover:bg-destructive/90"
                  >
                    {isDeleting ? 'Deleting...' : 'Delete'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      ),
      enableSorting: false,
    };

    return [...fieldColumns, actionsColumn];
  }, [translate, resource, meta, actions, show, edit, deleteResource, isDeleting]);

  const {
    getCanPreviousPage,
    getHeaderGroups,
    getCanNextPage,
    getPageCount,
    setPageIndex,
    previousPage,
    getRowModel,
    setPageSize,
    getState,
    nextPage,
    refineCore: {
      tableQueryResult: { isLoading },
    },
  } = useTable({
    columns,
    refineCoreProps: {
      resource: resource?.name,
      pagination: {
        mode: 'server',
      },
      sorters: {
        mode: 'server',
        initial: meta?.sorters || [{ field: 'createdAt', order: 'desc' }],
      },
      filters: {
        mode: 'server',
        initial: meta?.filters || [],
      },
    },
  });

  const { pagination } = getState();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold capitalize">
            {translate(`${resource?.name}.list`, resource?.name)}
          </CardTitle>
          {meta?.canCreate && (
            <Button className="capitalize" onClick={() => create(resource?.name || '')}>
              {translate('buttons.create', `Add ${resource?.name}`)}
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search/Filter Bar */}
            {/* <div className="flex items-center space-x-4">
              <Input placeholder={translate('Search...')} className="max-w-sm" />
            </div> */}

            {/* Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  {getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead key={header.id} className="whitespace-nowrap">
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {getRowModel().rows?.length ? (
                    getRowModel().rows.map((row) => (
                      <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id} className="whitespace-nowrap">
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="h-24 text-center">
                        {isLoading ? 'Loading...' : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between">
              <Pagination className="block">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (getCanPreviousPage()) previousPage();
                      }}
                      className={!getCanPreviousPage() ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>

                  {/* First page */}
                  {pagination.pageIndex > 1 && (
                    <>
                      <PaginationItem>
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            setPageIndex(0);
                          }}
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                      {pagination.pageIndex > 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                    </>
                  )}

                  {/* Previous page */}
                  {pagination.pageIndex > 0 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setPageIndex(pagination.pageIndex - 1);
                        }}
                      >
                        {pagination.pageIndex}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Current page */}
                  <PaginationItem>
                    <PaginationLink href="#" isActive>
                      {pagination.pageIndex + 1}
                    </PaginationLink>
                  </PaginationItem>

                  {/* Next page */}
                  {pagination.pageIndex < getPageCount() - 1 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setPageIndex(pagination.pageIndex + 1);
                        }}
                      >
                        {pagination.pageIndex + 2}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Last page */}
                  {pagination.pageIndex < getPageCount() - 2 && (
                    <>
                      {pagination.pageIndex < getPageCount() - 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                      <PaginationItem>
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            setPageIndex(getPageCount() - 1);
                          }}
                        >
                          {getPageCount()}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (getCanNextPage()) nextPage();
                      }}
                      className={!getCanNextPage() ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>

              <div className="flex flex-shrink-0 items-center gap-4">
                <div className="flex-shrink-0 text-sm font-medium">
                  Page {pagination.pageIndex + 1} of {getPageCount()}
                </div>
                <div className="flex flex-shrink-0 items-center space-x-2">
                  <p className="text-sm font-medium">Rows per page</p>
                  <select
                    value={pagination.pageSize}
                    onChange={(e) => setPageSize(Number(e.target.value))}
                    className="border-input bg-background h-8 w-[70px] rounded border px-2 text-sm"
                  >
                    {[10, 20, 30, 40, 50].map((pageSize) => (
                      <option key={pageSize} value={pageSize}>
                        {pageSize}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
