import type { ResourceShowMode, ResourceField } from '@/types';
import type { BaseRecord } from '@refinedev/core';

import { Link } from 'react-router';

import { AvatarFallback, AvatarImage, Avatar } from '@ps/ui/components/avatar';
import { snakeCaseToTitleCase, getFieldName } from '@/utils/string';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';

const getNestedData = (record: BaseRecord, fieldName: string) => {
  let data = '';
  fieldName.split('.').reduce((r, key) => {
    if (r) {
      if (typeof r[key] != 'object') data = r[key];
      return r[key];
    }
    return {};
  }, record);

  return data;
};

const getDataFromAltFields = (record: BaseRecord, fields: string[] = []) => {
  for (let i = 0; i < fields?.length; i += 1) {
    const data = getNestedData(record, fields[i] || '');
    if (data) return data;
  }
  return '';
};

const FieldDisplay = ({
  mode,
  field: { relation, name, type, meta, key, defaultValue, ...field },
  record,
  value,
}: {
  mode: ResourceShowMode;
  field: ResourceField;
  record: BaseRecord;
  value?: any;
}) => {
  const fieldName = getFieldName(name, key);

  const dynamicType = field.dynamicBasedOn ? record?.[field.dynamicBasedOn] : '';
  const nonDynamicType = type || name.toLowerCase();
  const fieldType = type === 'dynamic' ? dynamicType : nonDynamicType;

  value =
    value ||
    getDataFromAltFields(record, field.altDataFields) ||
    record?.[fieldName] ||
    defaultValue ||
    '';

  // Avatar field
  if (fieldType === 'avatar') {
    return (
      <Avatar className="h-10 w-10">
        <AvatarImage
          src={value || `https://ui-avatars.com/api/?name=${record?.name}`}
          alt={record?.name || 'Avatar'}
        />
        <AvatarFallback>{record?.name?.charAt(0)?.toUpperCase() || '?'}</AvatarFallback>
      </Avatar>
    );
  }

  // Image field
  if (fieldType === 'image') {
    return (
      <img
        src={value}
        alt="Field image"
        className={cn('rounded object-cover', mode === 'list' ? 'h-7 max-w-10' : 'h-32')}
      />
    );
  }

  // Email field
  if (fieldType === 'email') {
    return (
      <a href={`mailto:${value}`} className="text-blue-600 underline hover:text-blue-800">
        {value}
      </a>
    );
  }

  // Date field
  if (fieldType === 'date') {
    const date = new Date(value);
    return (
      <span className="text-muted-foreground text-sm">
        {date.toLocaleString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })}
      </span>
    );
  }

  // Color field
  if (fieldType === 'color') {
    return (
      <div className="flex items-center gap-2">
        <div
          className="h-4 w-4 rounded border border-gray-300"
          style={{ backgroundColor: value }}
        />
        <Badge variant="secondary" className="text-xs">
          {value}
        </Badge>
      </div>
    );
  }

  // Boolean field
  if (fieldType === 'boolean' || typeof value === 'boolean') {
    return (
      <Badge variant={value ? 'default' : 'secondary'} className="text-xs">
        {value ? 'True' : 'False'}
      </Badge>
    );
  }

  // Relation field
  if (relation) {
    return (
      <Link
        to={`/${relation.path}/show/${record?.[relation.resource]?._id}`}
        className="text-blue-600 underline hover:text-blue-800"
      >
        {getNestedData(record, relation.field)}
      </Link>
    );
  }

  // Select field
  if (fieldType === 'select') {
    const option = field.options?.find((o: { label: string; value: string }) => o.value === value);
    return (
      <Badge variant="outline" className="text-xs">
        {option?.label || value}
      </Badge>
    );
  }

  // Default text field with proper formatting
  const displayValue = (() => {
    if (['object', 'array'].includes(fieldType)) {
      return (
        <pre className="bg-muted max-w-xs overflow-auto rounded p-2 text-xs">
          {JSON.stringify(value, null, 2)}
        </pre>
      );
    }

    if (typeof value === 'string' && value.includes('_') && !meta?.isRaw) {
      return snakeCaseToTitleCase(value);
    }

    if (meta?.isPrice) {
      return `$${(value / 100).toFixed(2)}`;
    }

    if (fieldType === 'tag' && Array.isArray(value)) {
      return (
        <div className="flex flex-wrap gap-1">
          {value.map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      );
    }

    return value;
  })();

  // Handle text truncation for list mode
  if (mode === 'list' && typeof displayValue === 'string' && displayValue.length > 50) {
    return (
      <div className="max-w-48">
        <p className="truncate text-sm" title={displayValue}>
          {displayValue}
        </p>
      </div>
    );
  }

  return <div className="text-sm">{displayValue}</div>;
};

export default FieldDisplay;
