import type { AxiosRequestConfig } from 'axios';
import type { ResourceAction } from '@/types';

import { BaseRecord, useNotification } from '@refinedev/core';
import { LuLoader } from 'react-icons/lu';
import { useState } from 'react';

import {
  AlertDialogDescription,
  AlertDialogContent,
  AlertDialogTrigger,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialog,
} from '@ps/ui/components/alert-dialog';
import { axiosInstance } from '@/services/api';
import { Button } from '@ps/ui/components/button';

const getAxiosMethod = (method?: AxiosRequestConfig['method']) => {
  switch (method) {
    case 'DELETE':
      return axiosInstance.delete;
    case 'PATCH':
      return axiosInstance.patch;
    case 'POST':
      return axiosInstance.post;
    case 'PUT':
      return axiosInstance.put;
    default:
      return axiosInstance.get;
  }
};

const ActionButton: React.FC<ResourceAction & { id: string; record: BaseRecord }> = ({
  name,
  icon,
  endpoint,
  method,
  danger,
  style,
  requireConfirm,
  onActionSuccess,
  id,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { open } = useNotification();

  const onAction = async () => {
    setIsLoading(true);
    try {
      const url = endpoint.replace(':id', id);
      const response = await getAxiosMethod(method)(url);

      if (response.data.success) {
        open?.({
          message: 'Action Successful.',
          description: 'Success',
          type: 'success',
        });
      }

      if (onActionSuccess) {
        onActionSuccess(response);
      }
    } catch (error: any) {
      const message = error?.response?.data?.message || error?.message;
      open?.({
        description: 'Error',
        type: 'error',
        message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const buttonContent = <>{isLoading ? <LuLoader className="h-4 w-4 animate-spin" /> : icon}</>;

  const button = (
    <Button
      variant={danger ? 'destructive' : 'outline'}
      size="sm"
      disabled={isLoading}
      onClick={requireConfirm ? undefined : onAction}
      style={style}
      className="h-8 w-8 p-0"
      title={name}
    >
      {buttonContent}
    </Button>
  );

  if (requireConfirm) {
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>{button}</AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Action</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {name.toLowerCase()}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={onAction}
              disabled={isLoading}
              className={danger ? 'bg-destructive hover:bg-destructive/90' : ''}
            >
              {isLoading ? (
                <>
                  <LuLoader className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                name
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return button;
};

export default ActionButton;
