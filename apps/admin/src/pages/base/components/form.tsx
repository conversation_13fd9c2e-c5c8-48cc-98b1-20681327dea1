import type { ResourceFieldType, ResourceFormMode, ResourceField, ResourceMeta } from '@/types';
import type { FieldValues, Control } from 'react-hook-form';

import { LuCalendar, LuMinus, LuPlus, LuUser } from 'react-icons/lu';
import { useController, useFieldArray } from 'react-hook-form';
import { useTranslate, useResource } from '@refinedev/core';
import { useEffect, useState } from 'react';
import { axiosInstance } from '@refinedev/nestjsx-crud';
import { format } from 'date-fns';

import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import { CardContent, CardHeader, CardTitle, Card } from '@ps/ui/components/card';
import { PopoverContent, PopoverTrigger, Popover } from '@ps/ui/components/popover';
import { AvatarFallback, AvatarImage, Avatar } from '@ps/ui/components/avatar';
import { FileUploadProvider, FileUpload } from '@ps/ui/custom/file-upload';
import { getFieldName } from '@/utils/string';
import { Separator } from '@ps/ui/components/separator';
import { Checkbox } from '@ps/ui/components/checkbox';
import { Calendar } from '@ps/ui/components/calendar';
import { Textarea } from '@ps/ui/components/textarea';
import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import { Input } from '@ps/ui/components/input';
import { Label } from '@ps/ui/components/label';

const isFieldDisabled = (mode: ResourceFormMode, field: ResourceField): boolean => {
  return (
    (mode === 'create' && !!field.meta?.disableCreate) ||
    (mode === 'edit' && !!field.meta?.disableEdit)
  );
};

const getInitialValue = (fields: ResourceField[]) => {
  return fields.reduce(
    (obj, field) => {
      if (['object'].includes(field.type as string) && field.children) {
        obj[getFieldName(field.name, field.key)] = getInitialValue(field.children);
      } else {
        obj[getFieldName(field.name, field.key)] = field.defaultValue;
      }
      return obj;
    },
    {} as Record<string, any>,
  );
};

const BaseForm = ({ mode, control }: { mode: ResourceFormMode; control: Control<FieldValues> }) => {
  const { resource } = useResource();
  const meta = resource?.meta as ResourceMeta;
  const fields = meta?.fields || [];

  return (
    <div className="space-y-6">
      <FormFields mode={mode} control={control} fields={fields} parents={[]} />
    </div>
  );
};

const FormFields = ({
  mode,
  control,
  fields,
  parents = [],
}: {
  mode: ResourceFormMode;
  control: Control<FieldValues>;
  fields: ResourceField[];
  parents: (string | number)[];
}) => {
  return (
    <div className="mx-auto grid max-w-2xl grid-cols-1 gap-6">
      {fields.map((field, i) => (
        <FormField key={i} control={control} mode={mode} field={field} parents={parents} />
      ))}
    </div>
  );
};

const FormField = ({
  control,
  mode,
  field,
  parents,
}: {
  control: Control<FieldValues>;
  mode: ResourceFormMode;
  field: ResourceField;
  parents: (string | number)[];
}) => {
  const [options, setOptions] = useState(field.options || []);
  const translate = useTranslate();

  const { name, type, meta } = field;
  const fieldName = getFieldName(field.name, field.key);
  const fullFieldName = [...parents, fieldName].join('.');
  const fieldType = type || (name.toLowerCase() as ResourceFieldType);

  const {
    field: controllerField,
    fieldState: { error },
  } = useController({
    name: fullFieldName,
    control,
    rules: { required: !meta?.isOptional || meta?.isRequired },
  });

  useEffect(() => {
    if (['select', 'tag'].includes(fieldType) && field.meta?.optionFetchUrl) {
      axiosInstance.get(field.meta?.optionFetchUrl).then(({ data }) => {
        if (field.meta?.onOptionFetch) {
          return setOptions(field.meta.onOptionFetch(data));
        }
        return setOptions(data);
      });
    }
  }, [fieldType, field.meta]);

  const renderField = () => {
    const isDisabled = isFieldDisabled(mode, field);

    switch (fieldType) {
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fullFieldName}
              checked={
                controllerField.value !== undefined
                  ? controllerField.value
                  : (field.defaultValue ?? false)
              }
              onCheckedChange={controllerField.onChange}
              disabled={isDisabled}
            />
            <Label htmlFor={fullFieldName}>{translate(name)}</Label>
          </div>
        );

      case 'date':
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
                disabled={isDisabled}
              >
                <LuCalendar className="mr-2 h-4 w-4" />
                {controllerField.value ? (
                  format(new Date(controllerField.value), 'PPP')
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={controllerField.value ? new Date(controllerField.value) : undefined}
                onSelect={(date: any) => controllerField.onChange(date?.toISOString())}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'select':
        return (
          <Select
            value={controllerField.value}
            onValueChange={controllerField.onChange}
            disabled={isDisabled}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={`Select ${translate(name)}`} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'tag':
        return (
          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              {Array.isArray(controllerField.value) &&
                controllerField.value.map((tag: any, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
            </div>
            <Select
              onValueChange={(value: any) => {
                const currentTags = Array.isArray(controllerField.value)
                  ? controllerField.value
                  : [];
                if (!currentTags.includes(value)) {
                  controllerField.onChange([...currentTags, value]);
                }
              }}
              disabled={isDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Add tags" />
              </SelectTrigger>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'number':
        return (
          <Input
            type="number"
            {...controllerField}
            disabled={isDisabled}
            placeholder={field.placeholder}
            min={field.meta?.min}
            max={field.meta?.max}
          />
        );

      case 'email':
        return (
          <Input
            type="email"
            {...controllerField}
            disabled={isDisabled}
            placeholder={field.placeholder}
          />
        );

      case 'url':
        return (
          <Input
            type="url"
            {...controllerField}
            disabled={isDisabled}
            placeholder={field.placeholder}
          />
        );

      case 'color':
        return (
          <div className="flex items-center space-x-2">
            <Input
              type="color"
              {...controllerField}
              disabled={isDisabled}
              className="h-10 w-16 rounded border p-1"
            />
            <Input
              type="text"
              value={controllerField.value || ''}
              onChange={controllerField.onChange}
              disabled={isDisabled}
              placeholder="#000000"
              className="flex-1"
            />
          </div>
        );

      case 'text':
      case 'json':
      case 'object':
        return (
          <Textarea
            {...controllerField}
            disabled={isDisabled}
            placeholder={`Enter ${fieldType} for ${translate(name)}`}
            rows={6}
            className="font-mono text-sm"
          />
        );

      case 'array':
        return (
          <ArrayField
            control={control}
            field={field}
            mode={mode}
            parents={[...parents, fieldName]}
          />
        );

      case 'avatar':
      case 'image':
        return (
          <FileUploadField
            field={field}
            value={controllerField.value}
            onChange={controllerField.onChange}
            disabled={isDisabled}
          />
        );

      default:
        return <Input {...controllerField} disabled={isDisabled} placeholder={field.placeholder} />;
    }
  };

  // For boolean fields, we don't need a separate label
  if (fieldType === 'boolean') {
    return (
      <div className="space-y-2">
        {renderField()}
        {error && <p className="text-destructive text-sm">{error.message}</p>}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={fullFieldName} className="text-sm font-medium">
        {translate(name)}
        {(!meta?.isOptional || meta?.isRequired) && (
          <span className="text-destructive ml-1">*</span>
        )}
      </Label>
      {renderField()}
      {error && <p className="text-destructive text-sm">{error.message}</p>}
    </div>
  );
};

const ArrayField = ({
  control,
  field,
  mode,
  parents,
}: {
  control: Control<FieldValues>;
  field: ResourceField;
  mode: ResourceFormMode;
  parents: (string | number)[];
}) => {
  // const fieldName = getFieldName(field.name, field.key);
  const fullFieldName = parents.join('.');

  const {
    fields: arrayFields,
    append,
    remove,
  } = useFieldArray({
    control,
    name: fullFieldName,
  });

  const translate = useTranslate();

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle className="text-lg">{translate(field.name)}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {arrayFields.map((arrayField, index) => (
          <div key={arrayField.id} className="space-y-4 rounded-lg border p-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Item {index + 1}</h4>
              {(!field.meta?.isRequired || arrayFields.length > 1) && (
                <Button type="button" variant="outline" size="sm" onClick={() => remove(index)}>
                  <LuMinus className="h-4 w-4" />
                </Button>
              )}
            </div>
            <FormFields
              mode={mode}
              control={control}
              fields={field.children || []}
              parents={[...parents, index]}
            />
            {index < arrayFields.length - 1 && <Separator />}
          </div>
        ))}

        <Button
          type="button"
          variant="outline"
          onClick={() => append(getInitialValue(field.children || []))}
          className="w-full"
        >
          <LuPlus className="mr-2 h-4 w-4" />
          Add {translate(field.name)}
        </Button>
      </CardContent>
    </Card>
  );
};

const FileUploadField = ({
  field,
  value,
  onChange,
  disabled,
}: {
  field: ResourceField;
  value: string;
  onChange: (value: string) => void;
  disabled: boolean;
}) => {
  return (
    <div className="space-y-4">
      {!disabled && (
        <FileUploadProvider type="image" folder={field.meta?.uploadFolder}>
          <FileUpload onUpload={(url) => onChange(url[0] || '')} />
        </FileUploadProvider>
      )}

      {value && (
        <div className="mt-4">
          {field.type === 'avatar' ? (
            <Avatar className="size-16">
              <AvatarImage src={value} alt="Preview" />
              <AvatarFallback>
                <LuUser className="size-4" />
              </AvatarFallback>
            </Avatar>
          ) : field.type === 'image' ? (
            <img src={value} alt="Preview" className="h-auto max-h-16 rounded border" />
          ) : (
            <p className="text-muted-foreground text-sm">File: {value}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default BaseForm;
