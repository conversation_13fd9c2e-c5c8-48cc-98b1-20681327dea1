import type { IResourceComponentsProps } from '@refinedev/core';

import { useNavigation, useTranslate, useResource } from '@refinedev/core';
import { LuArrowLeft, LuSave, LuEye } from 'react-icons/lu';
import { useForm } from '@refinedev/react-hook-form';

import { Card<PERSON>ontent, CardHeader, CardTitle, Card } from '@ps/ui/components/card';
import { Button } from '@ps/ui/components/button';
import { Control } from 'react-hook-form';

import BaseForm from './components/form';

export const BaseEdit: React.FC<
  IResourceComponentsProps & {
    Form?: React.ComponentType<{ mode: 'create' | 'edit'; control: Control }>;
  }
> = ({ Form = BaseForm }) => {
  const { list, show } = useNavigation();
  const { resource } = useResource();
  const translate = useTranslate();
  const {
    refineCore: { onFinish, queryResult },
    formState: { isSubmitting },
    handleSubmit,
    control,
  } = useForm();

  const record = queryResult?.data;

  const onSubmit = (data: any) => {
    onFinish(data);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold capitalize">
            {translate(`${resource?.name}.edit`, `Edit ${resource?.name}`)}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => list(resource?.name || '')}>
              <LuArrowLeft className="mr-2 h-4 w-4" />
              {translate('buttons.list', 'Back to List')}
            </Button>
            <Button variant="outline" onClick={() => show(resource?.name || '', record?.data?._id)}>
              <LuEye className="mr-2 h-4 w-4" />
              {translate('buttons.show', 'Show')}
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <Form mode="edit" control={control} />

            <div className="flex justify-end border-t pt-6">
              <Button type="submit" disabled={isSubmitting} className="min-w-32">
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-transparent border-t-current" />
                    Saving...
                  </>
                ) : (
                  <>
                    <LuSave className="mr-2 h-4 w-4" />
                    {translate('buttons.save', 'Save Changes')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
