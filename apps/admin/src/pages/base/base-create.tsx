import type { IResourceComponentsProps } from '@refinedev/core';

import { useNavigation, useTranslate, useResource } from '@refinedev/core';
import { LuArrowLeft, LuSave } from 'react-icons/lu';
import { useForm } from '@refinedev/react-hook-form';
import { Control } from 'react-hook-form';

import { Card<PERSON>ontent, CardHeader, CardTitle, Card } from '@ps/ui/components/card';
import { Button } from '@ps/ui/components/button';

import BaseForm from './components/form';

export const BaseCreate: React.FC<
  IResourceComponentsProps & {
    Form?: React.ComponentType<{ mode: 'create' | 'edit'; control: Control }>;
  }
> = ({ Form = BaseForm }) => {
  const { resource } = useResource();
  const { list } = useNavigation();
  const translate = useTranslate();

  const {
    formState: { isSubmitting },
    refineCore: { onFinish },
    handleSubmit,
    control,
  } = useForm();

  const onSubmit = (data: any) => {
    onFinish(data);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold capitalize">
            {translate(`${resource?.name}.create`, `Create ${resource?.name}`)}
          </CardTitle>
          <Button variant="outline" onClick={() => list(resource?.name || '')}>
            <LuArrowLeft className="mr-2 h-4 w-4" />
            {translate('buttons.list', 'Back to List')}
          </Button>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <Form mode="create" control={control} />

            <div className="flex justify-end border-t pt-6">
              <Button type="submit" disabled={isSubmitting} className="min-w-32">
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-transparent border-t-current" />
                    Creating...
                  </>
                ) : (
                  <>
                    <LuSave className="mr-2 h-4 w-4" />
                    {translate('buttons.save', 'Create')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
