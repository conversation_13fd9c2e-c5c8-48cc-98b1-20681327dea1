import type { IResourceComponentsProps, BaseRecord } from '@refinedev/core';
import type { ResourceMeta } from '@/types';

import { useNavigation, useTranslate, useResource, useShow } from '@refinedev/core';
import { <PERSON><PERSON><PERSON><PERSON>l, LuList } from 'react-icons/lu';
import { Fragment } from 'react';

import { CardContent, CardHeader, CardTitle, Card } from '@ps/ui/components/card';
import { getFieldName } from '@/utils/string';
import { Separator } from '@ps/ui/components/separator';
import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';

import FieldDisplay from './components/field-display';

export const BaseShow: React.FC<IResourceComponentsProps> = () => {
  const { queryResult } = useShow();
  const { resource } = useResource();
  const { list, edit } = useNavigation();
  const translate = useTranslate();

  const { data, isLoading } = queryResult;
  const record = data?.data as BaseRecord;
  const meta = resource?.meta as ResourceMeta;
  const fields = meta?.fields || [];

  if (isLoading) {
    return (
      <div className="space-y-6 px-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="space-y-2">
                <div className="bg-muted h-4 w-[250px] animate-pulse rounded" />
                <div className="bg-muted h-4 w-[200px] animate-pulse rounded" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold capitalize">
            {translate(`${resource?.name}.show`, `Show ${resource?.name}`)}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => list(resource?.name || '')}>
              <LuList className="mr-2 h-4 w-4" />
              {translate('buttons.list', 'List')}
            </Button>
            {meta?.canEdit && (
              <Button onClick={() => edit(resource?.name || '', record?._id)}>
                <LuPencil className="mr-2 h-4 w-4" />
                {translate('buttons.edit', 'Edit')}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {fields
              .filter((f) => !f.meta?.hideInDetails)
              .map((field, i) => {
                const fieldName = getFieldName(field.name, field.key);
                const value = record?.[fieldName];

                return (
                  <Fragment key={i}>
                    <div className="space-y-2">
                      <h3 className="text-muted-foreground text-sm font-medium uppercase tracking-wide">
                        {translate(field.name)}
                      </h3>

                      <div className="flex min-h-[2rem] items-start">
                        {value !== undefined && value !== null && value !== '' ? (
                          <FieldDisplay mode="show" field={field} record={record} value={value} />
                        ) : (
                          <Badge variant="secondary" className="text-xs">
                            No data
                          </Badge>
                        )}
                      </div>
                    </div>

                    {i < fields.filter((f) => !f.meta?.hideInDetails).length - 1 && <Separator />}
                  </Fragment>
                );
              })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
