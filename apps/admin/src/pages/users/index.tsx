import type { IResourceComponentsProps } from '@refinedev/core';

import { BaseCreate, BaseEdit, BaseList, BaseShow } from '@/pages/base';

import actions from './actions';

export const UserCreate: React.FC<IResourceComponentsProps> = () => <BaseCreate />;
export const UserEdit: React.FC<IResourceComponentsProps> = () => <BaseEdit />;
export const UserList: React.FC<IResourceComponentsProps> = () => <BaseList actions={actions} />;
export const UserShow: React.FC<IResourceComponentsProps> = () => <BaseShow />;
