import type { Resource } from '@/types';

import { LuUser } from 'react-icons/lu';

const userResource: Resource = {
  icon: LuUser,
  title: 'Users',
  url: '/users',
  isActive: true,
  name: 'users',
  list: '/users',
  create: '/users/create',
  edit: '/users/edit/:id',
  show: '/users/show/:id',
  meta: {
    join: ['team'],
    canEdit: true,
    canCreate: false,
    canDelete: false,
    fields: [
      { name: 'Avatar', type: 'avatar', key: 'profilePicture', meta: { disableEdit: true } },
      { name: 'ID', key: '_id', meta: { isFilterable: true, disableEdit: true } },
      {
        name: 'Team ID',
        key: '_id',
        relation: {
          resource: 'team',
          field: 'team._id',
        },
        meta: { isFilterable: true, disableEdit: true },
      },
      { name: 'Name', meta: { isFilterable: true, isSortable: true, disableEdit: true } },
      { name: 'Email', meta: { isFilterable: true, isSortable: true, disableEdit: true } },
      // { name: 'Found Us From', meta: { isFilterable: true, disableEdit: true } },
      // {
      //   name: 'Plan',
      //   relation: {
      //     resource: 'business',
      //     field: 'business.subscriptionPlan',
      //   },
      //   meta: { isFilterable: true, disableEdit: true },
      // },
      {
        name: 'Joined',
        key: 'createdAt',
        type: 'date',
        meta: { isSortable: true, disableEdit: true },
      },
    ],
  },
};

export default userResource;
