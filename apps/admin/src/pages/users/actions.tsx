import type { ResourceAction } from '@/types';

import { AiOutlineUserDelete, AiOutlineDelete } from 'react-icons/ai'; // AiOutlineLogin

const actions: ResourceAction[] = [
  // {
  //   name: 'Impersonate',
  //   icon: <AiOutlineLogin />,
  //   endpoint: `/users/:id/impersonate`,
  //   onActionSuccess: ({ data }) => {
  //     if (data.url) {
  //       window.open(data.url, '_blank');
  //     }
  //   },
  // },
  {
    name: 'Deactivate',
    icon: <AiOutlineUserDelete />,
    endpoint: `/users/:id/deactivate`,
    method: 'PUT',
    danger: true,
    requireConfirm: true,
  },
  {
    name: 'Delete',
    icon: <AiOutlineDelete />,
    endpoint: `/users/:id/delete`,
    method: 'DELETE',
    danger: true,
    requireConfirm: true,
    onActionSuccess: () => {
      window.location.reload();
    },
  },
];

export default actions;
