import { ResourceProps } from '@refinedev/core';

import categoryResource from '@/pages/categories/resource';
import settingsResource from '@/pages/settings/resource';
import snippetResource from '@/pages/snippets/resource';
import teamResource from '@/pages/teams/resource';
import userResource from '@/pages/users/resource';

// The order of the resources is important for the sidebar to be in the correct order
const resources = [
  userResource,
  teamResource,
  categoryResource,
  snippetResource,
  settingsResource,
] as unknown as ResourceProps[];

export default resources;
