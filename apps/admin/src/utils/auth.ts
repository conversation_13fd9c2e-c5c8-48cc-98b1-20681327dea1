export function getState() {
  const redirectToFromQuery = new URLSearchParams(window.location.search).get('redirectTo') || '';
  const initialPath = encodeURI(window.location.pathname);

  const redirectTo =
    redirectToFromQuery ||
    (/login|signup/.test(window.location.pathname) ? '/dashboard' : window.location.pathname) ||
    '/';

  return JSON.stringify({
    app: 'admin',
    initialPath,
    redirectTo,
  });
}
