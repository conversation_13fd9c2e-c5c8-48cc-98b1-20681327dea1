import { toCamelCase } from '@ps/common/utils/string';

export const capitalizeFirstLetter = (word: string) => {
  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
};

export const snakeCaseToTitleCase = (str: string): string =>
  str.split('_').map(capitalizeFirstLetter).join(' ');

export const getFieldName = (name: string, key?: string): string => (key ? key : toCamelCase(name));

export { toCamelCase };
