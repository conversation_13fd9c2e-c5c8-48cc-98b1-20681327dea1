import type { AuthProvider } from '@refinedev/core';
import type { User } from '@ps/types';

import { axiosInstance } from '@refinedev/nestjsx-crud';

export function logout() {
  const token = localStorage.getItem('token');

  if (token && typeof window !== 'undefined') {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    axiosInstance.defaults.headers.common = {};
  }
}

const authProvider: AuthProvider = {
  login: async (user: User) => {
    if (user) {
      localStorage.setItem('user', JSON.stringify({ ...user, avatar: user.profilePicture }));

      return {
        success: true,
        redirectTo: '/',
      };
    }

    return {
      success: false,
    };
  },
  logout: async () => {
    logout();
    return {
      success: true,
      redirectTo: '/login',
    };
  },
  onError: async (error) => {
    return { error };
  },
  check: async () => {
    const token = localStorage.getItem('token');

    if (token) {
      return {
        authenticated: true,
      };
    }

    return {
      authenticated: false,
      error: {
        message: 'Check failed',
        name: 'Token not found',
      },
      logout: true,
      redirectTo: '/login',
    };
  },
  getPermissions: async () => null,
  getIdentity: async () => {
    const user = localStorage.getItem('user');
    if (user) {
      return JSON.parse(user);
    }

    return null;
  },
};

export default authProvider;
