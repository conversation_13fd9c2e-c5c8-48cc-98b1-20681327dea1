import type { Theme } from '@/providers/theme/context';

import { useEffect, useState } from 'react';
import { useLocalStorage } from 'react-use';

import { ThemeContext } from '@/providers/theme/context';

function getSystemTheme(): Exclude<Theme, 'system'> {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

function applyTheme(theme: Exclude<Theme, 'system'>) {
  if (typeof document === 'undefined') return;

  if (theme === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useLocalStorage<Theme>('theme', 'system');

  const [systemTheme, setSystemTheme] = useState<Exclude<Theme, 'system'>>(() => getSystemTheme());

  const resolvedTheme = theme === 'system' ? systemTheme : theme;

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  useEffect(() => {
    applyTheme(resolvedTheme as Exclude<Theme, 'system'>);
  }, [resolvedTheme]);

  return (
    <ThemeContext.Provider
      value={{
        resolvedTheme: resolvedTheme as Exclude<Theme, 'system'>,
        theme: theme as Theme,
        setTheme,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
