import type { DataProvider } from '@refinedev/core';

import nestJSXDataProvider, { handleJoin } from '@refinedev/nestjsx-crud';
import { RequestQueryBuilder } from '@nestjsx/crud-request';

import { axiosInstance } from '@/services/api';
import { env } from '@/config/env';

const dataProvider = (): DataProvider => {
  return {
    ...nestJSXDataProvider(env.VITE_API_URL, axiosInstance),
    getOne: async ({ resource, meta, id }) => {
      let query = RequestQueryBuilder.create();
      query = handleJoin(query, meta?.join);
      let requestUrl = `${env.VITE_API_URL}/${resource}/${id}`;

      if (query && !window.location.href.includes('edit')) {
        requestUrl = `${requestUrl}?${query.query()}`;
      }

      return axiosInstance.get(requestUrl);
    },
  };
};

export default dataProvider;
