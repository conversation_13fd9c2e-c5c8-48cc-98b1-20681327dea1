import { createEnv } from '@t3-oss/env-core';
import { z } from 'zod';

export const env = createEnv({
  clientPrefix: 'VITE_',
  client: {
    VITE_RELEASE: z.enum(['production', 'staging', 'development']),
    VITE_API_URL: z.string().url(),
    VITE_GOOGLE_CLIENT_ID: z.string(),
  },
  runtimeEnv: {
    VITE_RELEASE: import.meta.env.VITE_RELEASE || 'development',
    VITE_API_URL: import.meta.env.VITE_API_URL || '',
    VITE_GOOGLE_CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
  },
});
