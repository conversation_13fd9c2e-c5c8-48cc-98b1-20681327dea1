import { Routes as ReactRoutes, Outlet, Route } from 'react-router';
import { NavigateToResource, CatchAllNavigate } from '@refinedev/react-router';
import { Authenticated, ErrorComponent } from '@refinedev/core';

import { CategoryCreate, CategoryEdit, CategoryList, CategoryShow } from '@/pages/categories';
import { SettingsCreate, SettingsEdit, SettingsList, SettingsShow } from '@/pages/settings';
import { SnippetCreate, SnippetEdit, SnippetList, SnippetShow } from '@/pages/snippets';
import { TeamCreate, TeamEdit, TeamList, TeamShow } from '@/pages/teams';
import { UserCreate, UserEdit, UserList, UserShow } from '@/pages/users';
import Overview from '@/pages/overview';
import Layout from '@/components/layout';
import Login from '@/pages/auth/login';

export const Routes = () => (
  <ReactRoutes>
    <Route
      element={
        <Authenticated key="authenticated-inner" fallback={<CatchAllNavigate to="/login" />}>
          <Layout>
            <Outlet />
          </Layout>
        </Authenticated>
      }
    >
      <Route index element={<Overview />} />

      <Route path="/categories">
        <Route index element={<CategoryList />} />
        <Route path="create" element={<CategoryCreate />} />
        <Route path="edit/:id" element={<CategoryEdit />} />
        <Route path="show/:id" element={<CategoryShow />} />
      </Route>

      <Route path="/settings">
        <Route index element={<SettingsList />} />
        <Route path="create" element={<SettingsCreate />} />
        <Route path="edit/:id" element={<SettingsEdit />} />
        <Route path="show/:id" element={<SettingsShow />} />
      </Route>

      <Route path="/snippets">
        <Route index element={<SnippetList />} />
        <Route path="create" element={<SnippetCreate />} />
        <Route path="edit/:id" element={<SnippetEdit />} />
        <Route path="show/:id" element={<SnippetShow />} />
      </Route>

      <Route path="/teams">
        <Route index element={<TeamList />} />
        <Route path="create" element={<TeamCreate />} />
        <Route path="edit/:id" element={<TeamEdit />} />
        <Route path="show/:id" element={<TeamShow />} />
      </Route>

      <Route path="/users">
        <Route index element={<UserList />} />
        <Route path="create" element={<UserCreate />} />
        <Route path="edit/:id" element={<UserEdit />} />
        <Route path="show/:id" element={<UserShow />} />
      </Route>

      <Route path="*" element={<ErrorComponent />} />
    </Route>
    <Route
      element={
        <Authenticated key="authenticated-outer" fallback={<Outlet />}>
          <NavigateToResource />
        </Authenticated>
      }
    >
      <Route path="/login" element={<Login />} />
    </Route>
  </ReactRoutes>
);
