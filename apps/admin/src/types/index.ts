import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ResourceProps } from '@refinedev/core';
import type { CSSProperties } from 'react';
import type { IconType } from 'react-icons/lib';

export type ResourceShowMode = 'list' | 'show';
export type ResourceFormMode = 'create' | 'edit';

export interface ResourceAction {
  name: string;
  icon: React.ReactNode;
  endpoint: string;

  method?: AxiosRequestConfig['method'];

  style?: CSSProperties;
  danger?: boolean;
  requireConfirm?: boolean;
  onActionSuccess?: (resp: AxiosResponse) => void;
}

export type ResourceFieldType =
  | 'text'
  | 'number'
  | 'email'
  | 'url'
  | 'color'
  | 'date'
  | 'boolean'
  | 'select'
  | 'tag'
  | 'avatar'
  | 'image'
  | 'object'
  | 'array'
  | 'json'
  | 'dynamic';

export type ResourceSelectOption = { label: string; value: string };

export interface ResourceField {
  name: string;
  key?: string;
  type?: ResourceFieldType;
  dynamicBasedOn?: string; // Required when type === 'dynamic'
  options?: ResourceSelectOption[];
  altDataFields?: string[];
  defaultValue?: string | number | any;
  placeholder?: string;
  relation?: {
    resource: string;
    field: string;
    path?: string;
  };
  meta?: {
    isFilterable?: boolean;
    isSortable?: boolean;

    disableCreate?: boolean;
    disableEdit?: boolean;
    hideInDetails?: boolean;
    hideInList?: boolean;

    isRequired?: boolean;
    isOptional?: boolean;

    isPrice?: boolean;
    isRaw?: boolean;

    uploadFolder?: string;
    optionFetchUrl?: string;
    onOptionFetch?: (_: any) => ResourceSelectOption[];

    min?: number;
    max?: number;
  };

  styles?: Record<string, string | number>;

  children?: ResourceField[];
}

export type ResourceMeta = ResourceProps['meta'] & {
  canCreate?: boolean;
  canEdit?: boolean;
  fields: ResourceField[];
};

export interface Resource extends Omit<ResourceProps, 'icon'> {
  icon: IconType;
  title: string;
  name: string;
  url: string;
  isActive: boolean;
  meta: ResourceMeta;
}

export interface ResourceListResponse<Resource> {
  data: Resource[];
  total: number;
}
